// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_1_8
      targetCompatibility JavaVersion.VERSION_1_8
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-file-opener')
    implementation project(':capacitor-community-http')
    implementation project(':capacitor-community-keep-awake')
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capacitor-native-biometric')
    implementation "androidx.webkit:webkit:1.4.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
}
apply from: "../../node_modules/cordova-plugin-aes256-encryption/src/android/plugin.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
