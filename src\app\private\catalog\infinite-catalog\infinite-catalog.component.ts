/**
 * InfiniteCatalogComponent
 * 
 * Componente per la visualizzazione infinita del catalogo che supporta:
 * - Navigazione tramite categorie
 * - Ricerca per ID categoria o prodotto tramite parametro 'search'
 * - Scroll infinito bidirezionale (up/down)
 * - Visualizzazione di categorie e prodotti
 * 
 * Parametri URL supportati:
 * - search: ID di una categoria o di un prodotto da visualizzare
 * - rootId: ID della categoria radice
 * - categoryId: ID della categoria specifica
 * - firstVisibleProduct: ID del primo prodotto visibile per la navigazione
 * 
 * Quando viene utilizzato il parametro 'search' con un ID di prodotto,
 * il componente:
 * 1. Cerca prima tra le categorie nel store
 * 2. Se non trovato, cerca nel database delle categorie
 * 3. Se ancora non trovato, cerca nel database dei prodotti
 * 4. Converte il prodotto in una categoria per la visualizzazione
 * 5. Naviga alla categoria padre del prodotto
 */

import { Component, inject, Inject, Input, OnInit, Output, EventEmitter, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { IonCol, IonContent, IonGrid, IonIcon, IonInfiniteScroll, IonInfiniteScrollContent, IonRow } from '@ionic/angular/standalone';
import { InfiniteCatalogCardComponent } from '../catalog-card/infinite-catalog-card/infinite-catalog-card.component';
import { addIcons } from 'ionicons';
import { caretForwardCircle } from 'ionicons/icons';
import { CatalogService, ViewType, CardType } from 'src/app/service/catalog/catalog.service';
import { CommonModule } from '@angular/common';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { ActivatedRoute, Router } from '@angular/router';
import { SyncroUnifiedService } from 'src/app/service/syncro-unified/syncro-unified.service';
import { select, Store } from '@ngrx/store';
import { Category } from 'src/app/service/data/category';
import { Favorite } from 'src/app/service/data/favorite';
import Utils from 'src/app/shared/utils';
import { Subject } from 'rxjs';
import { takeUntil, throttleTime } from 'rxjs/operators';
import { TagFilterService } from '../../services/tag-filter.service';
import { CategoryService } from 'src/app/service/category/category.service';

export interface CardDimension {
  columns: number;
  rows: number;
}

@Component({
  selector: 'app-infinite-catalog',
  templateUrl: './infinite-catalog.component.html',
  styleUrls: ['./infinite-catalog.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonRow,
    IonContent,
    IonInfiniteScroll,
    IonInfiniteScrollContent,
    IonGrid,
    IonCol,
    InfiniteCatalogCardComponent,
    IonIcon,
  ]
})
export class InfiniteCatalogComponent implements OnInit, OnDestroy {

  showTopInfiniteLoader: boolean = true;
  showBottomInfiniteLoader: boolean = true;
  showNextCategoryButton: boolean = false;
  nextRootCategory: any = null;

  goToNextCategory = new EventEmitter<void>();

  // Services injection
  protected catalogService = inject(CatalogService);
  private _activatedRoute = inject(ActivatedRoute);
  private _syncroUnifiedService = inject(SyncroUnifiedService);
  private _store = inject(Store);
  private _cd = inject(ChangeDetectorRef);
  private _router = inject(Router);
  private _tagFilterService = inject(TagFilterService);
  private _categoryService = inject(CategoryService);

  // Destroy subject per unsubscribe
  private destroy$ = new Subject<void>();
  private scrollSubject$ = new Subject<any>();

  // Configuration
  protected infiniteScrollThreshold = '15%';
  protected longPressEnabled: boolean = false;
  protected columnsOccupancy: number = 4;
  protected cardDimension: CardDimension = { columns: 3, rows: 4 };

  // Data management
  protected allCategories: Category[] = [];
  protected favorites: any[] = [];
  protected loadedItems: CatalogCard[] = [];
  protected isLoading = false;
  protected itemsPerLoad = 24; // Numero di elementi da caricare per volta
  protected itemsPerPage = 24; // Per compatibilità con il sistema di paginazione esistente

  // Navigation state
  protected rootId: string | null = null;
  protected categoryId: string | null = null;
  protected firstVisibleProductId: string | null = null;

  // Current view configuration
  activeCardView: CardType = CardType.SMALL;

  // Loading states
  protected hasMoreItemsUp = false;
  protected hasMoreItemsDown = true;
  protected currentStartIndex = 0;
  protected currentEndIndex = 0;
  private lastUrlUpdateTime = 0;
  private readonly URL_UPDATE_THROTTLE = 1000; // Minimo 1 secondo tra aggiornamenti URL

  constructor() {
    addIcons({
      'caret-forward-circle': caretForwardCircle,
    });
    console.log('InfiniteCatalogComponent initialized');
  }

  ngOnInit() {
    // Setup del throttled scroll per l'aggiornamento URL
    this.scrollSubject$.pipe(
      throttleTime(500), // Aggiorna URL max ogni 500ms
      takeUntil(this.destroy$)
    ).subscribe(() => {
      this.updateFirstVisibleProductInUrl();
    });

    this.dispatch();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.scrollSubject$.complete();
  }


  onInfiniteScroll(event: any) {
    const ionContent: HTMLElement = event.target as HTMLElement;
    const ionCols = ionContent.querySelectorAll('app-infinite-catalog-card');
    const viewportHeight = ionContent.clientHeight;
    const visibleCols: string[] = [];

    ionCols.forEach((col: any) => {
      const rect = col.getBoundingClientRect();
      if (rect.top >= 0 && rect.bottom <= viewportHeight && col.classList.contains('category-card')) {
        visibleCols.push(col.innerText);
      }
    });

    if (visibleCols.length > 0) {
      // Aggiorna la categoria corrente se necessario
    }

    // Emetti l'evento di scroll per l'aggiornamento throttled dell'URL
    this.scrollSubject$.next(event);
  }

  onScrollStart() {
    this.longPressEnabled = true;
  }

  onScrollEnd() {
    this.longPressEnabled = false;
  }

  async onIonInfinite(event: any, direzione: 'up' | 'down') {
    console.log('Infinite: onIonInfinite called with direction:', direzione);

    if (this.isLoading) {
      event.target.complete();
      return;
    }

    this.isLoading = true;

    try {
      await this.loadMoreData(direzione);
    } catch (error) {
      console.error('Error loading more data:', error);
    } finally {
      this.isLoading = false;
      setTimeout(() => {
        event.target.complete();
      }, 500);
    }
  }

  onGoToNextCategory() {
    this.goToNextCategory.emit();
  }

  get containsItems(): boolean {
    return this.loadedItems && this.loadedItems.length > 0;
  }

  // Dispatch method per gestire i parametri della rotta
  private dispatch() {
    console.log("Infinite: Dispatching...");


    // Handle route parameters
    this._activatedRoute.queryParams.pipe(
      takeUntil(this.destroy$)
    ).subscribe(async params => {
      console.log('Infinite: Route params:', params);

      if (params.firstVisibleProduct) {
        const firstVisibleProduct = params.firstVisibleProduct || null;
        this.firstVisibleProductId = firstVisibleProduct;
        if (firstVisibleProduct) {
          await this.navigateToFirstVisibleProduct(firstVisibleProduct);
        }
      } else if (params.search) {
        const searchId = params.search;
        this.loadNearItems(searchId);
      } else {

        if (params.rootId) {
          const newRootId = params.rootId;
          const categoryId = params.categoryId || null;

          // Se è cambiata la root category, ricarica tutto
          const isNewRoot = this.rootId !== newRootId;

          this.rootId = newRootId;
          this.categoryId = categoryId;

          if (categoryId) {
            await this.showProducts();
          } else {
            if (isNewRoot) {
              this.allCategories = [];
            }
            console.log(`Infinite: Showing categories for rootId: ${this.rootId}`);
            await this.showCategories();
          }
        }
      }
    });
  }

  /**
   * Apre una pagina basata su un ID di ricerca che può essere sia una categoria che un prodotto
   * @param searchId L'ID della categoria o del prodotto da cercare
   */
  async loadNearItems(searchId: string) {
    console.log(`🔍 Searching for item with ID: ${searchId}`);

    // Prima cerca tra le categorie nel store
    let categories: Category[] = [];
    this._store.pipe(select('categories'), takeUntil(this.destroy$)).subscribe(res => {
      categories = [...res];
    }).unsubscribe();

    let foundItem = categories.find((category: Category) => {
      return category.id.toString() === searchId.toString();
    });

    // Se non trovato nelle categorie del store, cerca nel database
    if (!foundItem) {
      console.log(`🔍 Item not found in store, searching in database...`);
      try {
        // Cerca prima come categoria
        foundItem = await this._syncroUnifiedService.getCategoryById(searchId);
        
        if (foundItem) {
          console.log(`✅ Found category in database:`, foundItem);
        } else {
          // Se non trovato come categoria, cerca come prodotto
          console.log(`🔍 Not found as category, searching as product...`);
          const product = await this._syncroUnifiedService.getProductByCode(searchId);

          if (product) {
            // Converte il prodotto in una categoria per compatibilità
            foundItem = new Category();
            foundItem.id = product.code;
            foundItem.name = product.name;
            foundItem.description = product.description;
            foundItem.image = product.image;
            foundItem.idRootCategory = product.idCategory;
            foundItem.idSubCategory = product.idSubCategory;
            foundItem.isProduct = true;
            foundItem.level = "99"; // Livello alto per i prodotti
            foundItem.media = [];
            foundItem.thumbnail = product.thumbnail;
            foundItem.subcategories = null;
            foundItem.hierarchyCodes = null;
            foundItem.images = [];
            foundItem.cancellationInstant = null;
            
            console.log(`✅ Found product in database and converted to category:`, foundItem);
          }
        }
      } catch (error) {
        console.error('❌ Error searching in database:', error);
      }
    }

    if (!foundItem) {
      console.warn(`⚠️ Item with ID ${searchId} not found in categories or products`);
      return;
    }

    // Se è un prodotto, naviga direttamente alla sua categoria padre
    if (foundItem.isProduct) {
      console.log(`✅ Found product ${foundItem.id}, navigating to its category`);
      this.rootId = foundItem.idRootCategory;
      await this.showCategories();

      // Trova l'indice del prodotto nella lista filtrata e carica l'intorno
      const targetIndex = this.allCategories.findIndex(cat =>
        cat.id.toString() === searchId.toString()
      );

      if (targetIndex !== -1) {
        await this.loadAroundTargetIndex(targetIndex);
      } else {
        console.warn(`⚠️ Product ${searchId} not found in filtered categories list`);
      }
    } else {
      // È una categoria normale
      console.log(`✅ Found category ${foundItem.id}`);
      this.rootId = foundItem.idRootCategory;
      await this.showCategories();

      // Trova l'indice nella lista filtrata e carica l'intorno
      const targetIndex = this.allCategories.findIndex(cat =>
        cat.id.toString() === searchId.toString()
      );

      if (targetIndex !== -1) {
        await this.loadAroundTargetIndex(targetIndex);
      } else {
        console.warn(`⚠️ Category ${searchId} not found in filtered categories list`);
      }
    }
  }

  async showCategories() {
    console.log(`Infinite: Showing categories for rootId: ${this.rootId}`);

    this.catalogService.setRootView(false);
    this.catalogService.setIntoSubcategory(true);

    try {
      const childCategories = await this._syncroUnifiedService.getCategoriesByParent(this.rootId);
      console.log(`Infinite: Found ${childCategories.length} child categories for rootId ${this.rootId}`);

      let candidateCategories: Category[] = [];

      if (childCategories.length > 0) {
        console.log('Infinite: Sample child categories:', childCategories.slice(0, 3).map(c => ({
          id: c.id,
          name: c.name,
          isProduct: c.isProduct,
          level: c.level
        })));

        // Applica lo stesso filtro del componente paginato
        candidateCategories = childCategories.filter((item, index) => {
          if (this.activeCardView === CardType.SMALL) {
            // Per le card small, mostra le categorie che possono essere mostrate o i prodotti
            return this.canBeShownInArray(item, index, childCategories) || item.isProduct;
          } else {
            // Per le altre card view, mostra solo i prodotti
            return item.isProduct;
          }
        });

        console.log(`Infinite: After basic filtering for CardType.${this.activeCardView}: ${candidateCategories.length} categories`);
      } else {
        console.log('Infinite: No child categories found, using fallback method');
        // Fallback al metodo originale
        let categories = [];
        this._store.pipe(select('categories')).subscribe(res => categories = [...res]).unsubscribe();

        candidateCategories = categories.filter((item) => {
          return item.idApp.toString().includes(this.rootId.toString()) &&
            item.idApp.toString() != this.rootId.toString() &&
            item.level > 2;
        });

        console.log(`Infinite: Fallback found ${candidateCategories.length} categories`);
      }

      // Applica il filtraggio basato sui tag del cliente
      console.log(`🔍 Infinite: Applying tag filters to ${candidateCategories.length} candidate categories`);
      this.allCategories = await this._categoryService.getFilteredCategoriesByTags(candidateCategories);

      console.log(`✅ Infinite: After tag filtering: ${this.allCategories.length} categories visible`);

    } catch (error) {
      console.error('Infinite: Error loading categories:', error);
      // Fallback
      let categories = [];
      this._store.pipe(select('categories')).subscribe(res => categories = [...res]).unsubscribe();

      this.allCategories = categories.filter((item) => {
        return item.idApp.toString().includes(this.rootId.toString()) &&
          item.idApp.toString() != this.rootId.toString() &&
          item.level > 2;
      });

      console.log(`Infinite: Error fallback found ${this.allCategories.length} categories`);
    }

    console.log(`Infinite: Final allCategories length: ${this.allCategories.length}`);
    await this.loadInitialData();
    console.log(`Infinite: Categories loaded: ${this.allCategories.length} items`);
  }

  async showProducts() {
    console.log('Infinite: Showing products');
    this.catalogService.setRootView(false);
    this.catalogService.setIntoSubcategory(true);
  }

  // Data loading methods
  private async loadMoreData(direction: 'up' | 'down') {
    if (direction === 'down') {
      await this.loadMoreItemsDown();
    } else {
      await this.loadMoreItemsUp();
    }
  }

  private async loadMoreItemsDown() {
    console.log('Infinite: Loading more items down');

    if (!this.hasMoreItemsDown || this.currentEndIndex >= this.allCategories.length) {
      this.hasMoreItemsDown = false;
      return;
    }

    const startIndex = this.currentEndIndex;
    const endIndex = Math.min(startIndex + this.itemsPerLoad, this.allCategories.length);

    const newItems = await this.createCatalogCards(
      this.allCategories.slice(startIndex, endIndex)
    );

    this.loadedItems = [...this.loadedItems, ...newItems];
    this.currentEndIndex = endIndex;

    // Controlla se ci sono altri elementi da caricare
    this.hasMoreItemsDown = endIndex < this.allCategories.length;

    this._cd.detectChanges();
    console.log(`Infinite: Loaded ${newItems.length} items down. Total: ${this.loadedItems.length}`);
  }

  private async loadMoreItemsUp() {
    console.log('Infinite: Loading more items up');

    if (!this.hasMoreItemsUp || this.currentStartIndex <= 0) {
      this.hasMoreItemsUp = false;
      return;
    }

    const endIndex = this.currentStartIndex;
    const startIndex = Math.max(0, endIndex - this.itemsPerLoad);

    const newItems = await this.createCatalogCards(
      this.allCategories.slice(startIndex, endIndex)
    );

    this.loadedItems = [...newItems, ...this.loadedItems];
    this.currentStartIndex = startIndex;

    // Controlla se ci sono altri elementi da caricare
    this.hasMoreItemsUp = startIndex > 0;

    this._cd.detectChanges();
    console.log(`Infinite: Loaded ${newItems.length} items up. Total: ${this.loadedItems.length}`);
  }

  private async createCatalogCards(categories: Category[]): Promise<CatalogCard[]> {
    console.log('Infinite: createCatalogCards called with:', {
      categoriesLength: categories.length,
      activeCardView: this.activeCardView,
      sampleCategories: categories.slice(0, 3).map(c => ({
        id: c.id,
        name: c.name,
        isProduct: c.isProduct,
        type: Utils.objToJson(c.isProduct) ? 'PRODUCT' : 'CATEGORY'
      }))
    });

    // Usiamo la stessa logica del componente paginato
    const catalogCards = categories.map((item: Category) => {
      const card = CatalogCard.fromCategory(
        item,
        Utils.objToJson(item.isProduct) ? 'PRODUCT' : 'CATEGORY',
        this.isFavorite(item.id.toString())
      );
      console.log('Infinite: Created card:', {
        id: card.id,
        name: card.name,
        type: card.type,
        isProduct: item.isProduct
      });
      return card;
    });

    console.log('Infinite: Final catalog cards created:', catalogCards.length);
    return catalogCards;
  }

  private resetAndLoadData() {
    console.log('Infinite: Resetting and loading data');
    this.loadedItems = [];
    this.currentStartIndex = 0;
    this.currentEndIndex = 0;
    this.hasMoreItemsUp = false;
    this.hasMoreItemsDown = true;

    if (this.allCategories.length > 0) {
      this.loadInitialData();
    }
  }

  private async loadInitialData() {
    console.log('Infinite: Loading initial data');

    if (this.allCategories.length === 0) {
      console.log('Infinite: No categories available for initial load');
      this.loadedItems = [];
      this.hasMoreItemsUp = false;
      this.hasMoreItemsDown = false;
      this._cd.detectChanges();
      return;
    }

    this.isLoading = true;

    try {
      const initialLoadSize = this.itemsPerLoad * 2; // Carica 2 schermate inizialmente
      const endIndex = Math.min(initialLoadSize, this.allCategories.length);

      const initialItems = await this.createCatalogCards(
        this.allCategories.slice(0, endIndex)
      );

      this.loadedItems = initialItems;
      console.log(`Infinite: Initial load with ${initialItems.length} items from index 0 to ${endIndex}`);
      this.currentStartIndex = 0;
      this.currentEndIndex = endIndex;
      this.hasMoreItemsUp = false;
      this.hasMoreItemsDown = endIndex < this.allCategories.length;

      console.log(`Infinite: Initial load complete. Loaded ${initialItems.length} items`);
      this._cd.detectChanges();
    } catch (error) {
      console.error('Error during initial load:', error);
    } finally {
      this.isLoading = false;
    }
  }

  trackById(index: number, item: CatalogCard): string {
    return item.id;
  }

  // Utility methods
  private canBeShownInArray(item: Category, index: number, array: Category[]): boolean {
    // Se è l'ultimo item nell'array, può essere mostrato
    if (index === array.length - 1) {
      return true;
    }

    // Controlla se l'item successivo è un prodotto
    const nextItem = array[index + 1];
    return nextItem.isProduct === true;
  }

  public isFavorite(idCategory: string): boolean {
    const favorites: Favorite[] = this.favorites.filter((favorite) =>
      favorite.customerUid.toString() === this.catalogService.userUid.toString() &&
      favorite.idCategory.toString() === idCategory
    );
    return favorites.length === 1;
  }

  private async loadAroundTargetIndex(targetIndex: number) {
    console.log(`🎯 Loading around target index: ${targetIndex}`);

    // Reset dello stato corrente
    this.loadedItems = [];
    this.isLoading = true;

    try {
      // Calcola la finestra di caricamento intorno al target
      const windowSize = this.itemsPerLoad * 2; // Carica 2 schermate intorno al target
      const halfWindow = Math.floor(windowSize / 2);

      // Calcola gli indici di inizio e fine, assicurandoci che rimangano nei limiti
      let startIndex = Math.max(0, targetIndex - halfWindow);
      let endIndex = Math.min(this.allCategories.length, targetIndex + halfWindow);

      // Aggiusta la finestra se siamo vicini ai bordi
      if (endIndex - startIndex < windowSize && this.allCategories.length >= windowSize) {
        if (startIndex === 0) {
          endIndex = Math.min(this.allCategories.length, windowSize);
        } else if (endIndex === this.allCategories.length) {
          startIndex = Math.max(0, this.allCategories.length - windowSize);
        }
      }

      console.log(`🎯 Loading items from index ${startIndex} to ${endIndex} (target at ${targetIndex})`);

      // Carica gli elementi nella finestra calcolata
      const itemsToLoad = this.allCategories.slice(startIndex, endIndex);
      const catalogCards = await this.createCatalogCards(itemsToLoad);

      this.loadedItems = catalogCards;
      this.currentStartIndex = startIndex;
      this.currentEndIndex = endIndex;

      // Imposta i flag per l'infinite scroll
      this.hasMoreItemsUp = startIndex > 0;
      this.hasMoreItemsDown = endIndex < this.allCategories.length;

      console.log(`🎯 Loaded ${catalogCards.length} items around target. Can load up: ${this.hasMoreItemsUp}, Can load down: ${this.hasMoreItemsDown}`);

      this._cd.detectChanges();

      // Opzionale: Scroll automaticamente all'elemento target dopo un breve delay
      setTimeout(() => {
        this.scrollToTargetElement(targetIndex - startIndex);
      }, 100);

    } catch (error) {
      console.error('Error loading around target index:', error);
    } finally {
      this.isLoading = false;
    }
  }

  private scrollToTargetElement(relativeIndex: number) {
    try {
      // Trova l'elemento DOM corrispondente e scrolla verso di esso
      const targetSelector = `app-infinite-catalog-card:nth-child(${relativeIndex + 1})`;
      const targetElement = document.querySelector(targetSelector);

      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        console.log(`📍 Scrolled to target element at relative index ${relativeIndex}`);
      }
    } catch (error) {
      console.warn('Could not scroll to target element:', error);
    }
  }

  /**
   * Aggiorna il parametro URL con l'ID del primo prodotto visibile
   */
  private updateFirstVisibleProductInUrl() {
    const firstVisibleProductId = this.getFirstVisibleProductId();
    
    if (firstVisibleProductId && firstVisibleProductId !== this.firstVisibleProductId) {
      this.firstVisibleProductId = firstVisibleProductId;
      
      // Aggiorna l'URL senza ricaricare la pagina
      const currentParams = { ...this._activatedRoute.snapshot.queryParams };
      currentParams.firstVisibleProduct = firstVisibleProductId;
      
      const now = Date.now();
      // Controlla se è passato abbastanza tempo dall'ultimo aggiornamento
      if (now - this.lastUrlUpdateTime > this.URL_UPDATE_THROTTLE) {
        this._router.navigate([], {
          relativeTo: this._activatedRoute,
          queryParams: currentParams,
          queryParamsHandling: 'merge'
        });
        
        this.lastUrlUpdateTime = now;
        console.log(`📍 Updated URL with first visible product: ${firstVisibleProductId}`);
      } else {
        console.log(`⏳ Throttled URL update, ignoring this change`);
      }
    } else if (!firstVisibleProductId) {
      this.clearFirstVisibleProductFromUrl();
    }
  }

  /**
   * Ottiene l'ID del primo prodotto visibile nel viewport
   */
  private getFirstVisibleProductId(): string | null {
    try {
      const catalogCards = document.querySelectorAll('app-infinite-catalog-card[data-index]');
      const scrollContainer = document.querySelector('ion-content');
      
      if (!scrollContainer || catalogCards.length === 0) {
        return null;
      }

      const containerRect = scrollContainer.getBoundingClientRect();
      const containerTop = containerRect.top + 100; // Offset per la toolbar
      const containerBottom = containerRect.bottom;

      // Array per raccogliere tutti i prodotti visibili con la loro posizione
      const visibleProducts: {id: string, top: number, index: number}[] = [];

      for (let i = 0; i < catalogCards.length; i++) {
        const card = catalogCards[i] as HTMLElement;
        const cardRect = card.getBoundingClientRect();
        
        // Controlla se la card è visibile nel viewport (almeno parzialmente)
        if (cardRect.bottom > containerTop && cardRect.top < containerBottom) {
          const cardIndex = parseInt(card.getAttribute('data-index') || '0');
          const correspondingItem = this.loadedItems[cardIndex];
          
          if (correspondingItem && correspondingItem.type === 'PRODUCT') {
            visibleProducts.push({
              id: correspondingItem.id,
              top: cardRect.top,
              index: cardIndex
            });
          }
        }
      }

      // Ordina per posizione e prendi il primo (quello più in alto)
      if (visibleProducts.length > 0) {
        visibleProducts.sort((a, b) => a.top - b.top);
        const firstProduct = visibleProducts[0];
        console.log(`🔍 First visible product found: ${firstProduct.id} at index ${firstProduct.index}, top: ${firstProduct.top}`);
        return firstProduct.id;
      }
      
      return null;
    } catch (error) {
      console.warn('Error getting first visible product ID:', error);
      return null;
    }
  }

  /**
   * Posiziona la vista sul prodotto specificato dall'URL
   */
  private async navigateToFirstVisibleProduct(productId: string) {
    console.log(`🎯 Navigating to first visible product: ${productId}`);

    if (this.allCategories.length === 0) {
    }
    
    // Trova l'indice del prodotto nell'array completo
    const targetIndex = this.allCategories.findIndex(item => 
      item.id.toString() === productId && item.isProduct
    );
    
    if (targetIndex === -1) {
      console.warn(`⚠️ Product with ID ${productId} not found in categories`);
      return;
    }
    
    // Carica l'intorno del prodotto target
    await this.loadAroundTargetIndex(targetIndex);
    
    // Trova l'indice relativo del prodotto nei dati caricati
    const relativeIndex = this.loadedItems.findIndex(item => 
      item.id === productId && item.type === 'PRODUCT'
    );
    
    if (relativeIndex !== -1) {
      // Scrolla al prodotto dopo un breve delay per permettere il rendering
      setTimeout(() => {
        this.scrollToSpecificProduct(relativeIndex);
      }, 200);
    }
  }

  /**
   * Scrolla verso un prodotto specifico usando il suo indice relativo
   */
  private scrollToSpecificProduct(relativeIndex: number) {
    try {
      const targetSelector = `app-infinite-catalog-card[data-index="${relativeIndex}"]`;
      const targetElement = document.querySelector(targetSelector);
      
      if (targetElement) {
        targetElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        });
        console.log(`📍 Scrolled to product at relative index ${relativeIndex}`);
      } else {
        console.warn(`Element not found with selector: ${targetSelector}`);
      }
    } catch (error) {
      console.warn('Could not scroll to specific product:', error);
    }
  }

  private async loadRootCategories() {
    console.log('Loading root categories');

    try {
      // Ottieni tutte le categorie root dallo store
      let allCategories: Category[] = [];
      this._store.pipe(select('categories'), takeUntil(this.destroy$)).subscribe(res => {
        allCategories = [...res];
      }).unsubscribe();

      // Filtra solo le categorie root
      this.allCategories = allCategories.filter(category =>
        this.catalogService.testIsRoot(category.idApp)
      );

      console.log(`Found ${this.allCategories.length} root categories`);

      // Carica i dati iniziali
      await this.loadInitialData();

    } catch (error) {
      console.error('Error loading root categories:', error);
    }
  }

  /**
   * Rimuove il parametro firstVisibleProduct dall'URL se non ci sono prodotti visibili
   */
  private clearFirstVisibleProductFromUrl() {
    if (this.firstVisibleProductId) {
      this.firstVisibleProductId = null;
      
      const currentParams = { ...this._activatedRoute.snapshot.queryParams };
      delete currentParams.firstVisibleProduct;
      
      this._router.navigate([], {
        relativeTo: this._activatedRoute,
        queryParams: currentParams,
        queryParamsHandling: 'merge'
      });
      
      console.log(`🧹 Cleared first visible product from URL`);
    }
  }
}
