import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { throwError, timeout, firstValueFrom } from 'rxjs';
import { map, switchMap } from 'rxjs';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { Platform } from '@ionic/angular';
import { JWTTokenService } from './jwttoken.service';
import { BasicResponse } from './data/basic-response';
import { App } from '@capacitor/app'
import { Directory, Filesystem } from '@capacitor/filesystem';
import { inject } from '@angular/core';


export abstract class BasicService {

  appVersion = "0.0.0";

  _httpClient = inject(HttpClient);
  _route = inject(Router);
  _translate = inject(TranslateService);
  _jwtService = inject(JWTTokenService);
  _platform = inject(Platform);

  constructor() {
      this._platform.ready().then(() => {
        if(this._platform.is('capacitor'))
        {
            App.getInfo().then((info) => {
              this.appVersion = info.version;
            });
        }
      });
    }

    private getHeaderDictionary(skip:boolean = false, contentType: string | null) : any {
      const language = this._translate.currentLang ? this._translate.currentLang : 'it-IT';
      if(!!contentType){
        return {
          'Content-Type':  contentType,
          'skip': `${skip}`,
          'Accept': contentType,
          'Accept-Language': language,
          'App-Version': this.appVersion,
          'Access-Control-Allow-Origin': "*"
        };
      } else {
        return {
          'skip': `${skip}`,
          'Accept-Language': language,
          'App-Version': this.appVersion,
          'Access-Control-Allow-Origin': "*"
        };
      }

    }

    public getHeader(skip:boolean = false, contentType: string | null) : HttpHeaders {
      const header =
        new HttpHeaders(this.getHeaderDictionary(skip, contentType));
      return header;
    }

    async basicPost(input:any, method:string, skip:boolean = false): Promise<BasicResponse> {
    try {
      const response = await firstValueFrom(
        this._httpClient.post<any>(environment.apiUrl + method, input, {headers: this.getHeader(skip, 'application/json'), observe: 'response'})
          .pipe(
            map((data) => {
              if(typeof data.headers !== "undefined" && data.headers.get('DatacolAuthToken') != null)
              {
                // Se il servizio ha effettuato il rinnovo del JWT, devo aggiornare quello corrente.
                this._jwtService.setToken(data.headers.get('DatacolAuthToken'));
              }
              return data.body as BasicResponse
            })
          )
      );
      return response;
    } catch (err) {
      this.catchError(err as HttpErrorResponse);
      return null;
    }
  }

  async basicGet(method:string, skip:boolean = false): Promise<BasicResponse> {
    const fullUrl = environment.apiUrl + method;
    const headers = this.getHeader(skip, 'application/json');


    try {
      const response = await firstValueFrom(
        this._httpClient.get<any>(fullUrl, {headers: headers, observe: 'response'})
          .pipe(
            map((data) => {
              if(typeof data.headers !== "undefined" && data.headers.get('DatacolAuthToken') != null)
              {
                // Se il servizio ha effettuato il rinnovo del JWT, devo aggiornare quello corrente.
                this._jwtService.setToken(data.headers.get('DatacolAuthToken'));
              }
              return data.body as BasicResponse
            })
          )
      );
      return response;
    } catch (err) {
      const httpErr = err as HttpErrorResponse;
      console.error(`❌ BasicService.basicGet ERROR:`, httpErr);
      console.error(`   Status: ${httpErr.status}`);
      console.error(`   Message: ${httpErr.message}`);
      console.error(`   URL: ${httpErr.url}`);
      console.error(`   Error body:`, httpErr.error);
      this.catchError(httpErr);
      return null;
    }
  }

  async externalGet(url:string): Promise<any> {
    try {
      const response = await firstValueFrom(
        this._httpClient.get<any>(url, {headers: this.getHeader(true, null), observe: 'response'})
      );
      return response;
    } catch (err) {
      this.catchError(err as HttpErrorResponse);
      return null;
    }
  }

  async basicGetMedia(method:string, skip:boolean = false): Promise<string>{
    try{
      const response = await firstValueFrom(
        this._httpClient.get(environment.apiUrl + method,{headers: this.getHeader(skip, null), observe: 'response', responseType: 'blob'})
        .pipe(
          switchMap(async (data) => {
            if(typeof data.headers !== "undefined" && data.headers.get('DatacolAuthToken') != null)
            {
              // Se il servizio ha effettuato il rinnovo del JWT, devo aggiornare quello corrente.
              this._jwtService.setToken(data.headers.get('DatacolAuthToken'));
            }
            let fileName = method.substring(method.lastIndexOf('/')+1);
            if(fileName.indexOf('?') >=0 )
              fileName = fileName.substring(0, fileName.indexOf('?'));

            try {
              const saved = await Filesystem.writeFile({
                path: fileName,
                data: data.body,
                directory: Directory.Documents
              });

              if(!!saved)
              {
                //await this.zip.unzip(Directory.Data + '/' + fileName, Directory.Data, (progress)=> { return true});
                await Filesystem.deleteFile({path: Directory.Data + '/' + fileName});
              }
              return 'OK';
            } catch (fileErr) {
              console.log("writeFile | fileName ", fileErr);
              return 'ERROR';
            }
          }),
          timeout(1800000)
        )
      );
      return response;
    }catch(err) {
      this.catchError(err as HttpErrorResponse);
      return null;
    }
  }

  private async catchError(err: HttpErrorResponse) {
    console.error("catchError", JSON.stringify(err));

    try {
      if(err.status === 500)
      {
        let messaggio = "Sorry, something went wrong. We are working to resolve the problem";
        try {
          const translations = await firstValueFrom(this._translate.get('HTTP_SERVICE'));
          messaggio = translations["ERROR_500"] || messaggio;
        } catch (translateErr) {
          console.warn("Translation error for ERROR_500:", translateErr);
        }
        return throwError(() => new Error(messaggio));
      }
      else if(err.status === 504 || err.status === -1)
      {
        let messaggio = "Il servizio non è raggiungibile.";
        try {
          const translations = await firstValueFrom(this._translate.get('HTTP_SERVICE'));
          messaggio = translations["ERROR_504"] || messaggio;
        } catch (translateErr) {
          console.warn("Translation error for ERROR_504:", translateErr);
        }
        return throwError(() => new Error(messaggio));
      }
      else if(err.status === 400)
      {
        // non mostro Bad Request.
        return;
      }
      else if(err.status === 401)
      {
        this._jwtService.setToken(null);
        let messaggio = "Sessione Scaduta! ";
        try {
          const translations = await firstValueFrom(this._translate.get('HTTP_SERVICE'));
          messaggio = translations["ERROR_401"] || messaggio;
        } catch (translateErr) {
          console.warn("Translation error for ERROR_401:", translateErr);
        }
        this._route.navigate(['/login'], { queryParams: { previusUrl: this._route.routerState.snapshot.url}});
        return throwError(() => new Error(messaggio));
      }
      else if(err.name && err.name.match("TimeoutError")){
        let messaggio = "Timeout!";
        try {
          const translations = await firstValueFrom(this._translate.get('HTTP_SERVICE'));
          messaggio = translations["TIMEOUT"] || messaggio;
        } catch (translateErr) {
          console.warn("Translation error for TIMEOUT:", translateErr);
        }
        return throwError(() => new Error(messaggio));
      }

      return throwError(() => err);
    } catch (generalErr) {
      console.error("General error in catchError:", generalErr);
      return throwError(() => err);
    }
  }
}
