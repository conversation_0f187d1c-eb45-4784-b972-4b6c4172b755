# Sistema di Sincronizzazione v2

## Quick Start

### Installazione e Setup
```typescript
// 1. Inietta i servizi necessari nel tuo componente
constructor(
  private syncroV2Orchestrator: SyncroV2OrchestratorService,
  private syncroV2DbService: SyncroV2DbService,
  private imageOfflineService: ImageOfflineService
) {}

// 2. Inizializza il database (una volta all'avvio dell'app)
await this.syncroV2Orchestrator.initializeDatabase();

// 3. Esegui la sincronizzazione
const result = await this.syncroV2Orchestrator.syncComplete(catalogId, pageSize);
```

### Sincronizzazione Base
```typescript
// Sincronizzazione completa di un catalogo
async syncCatalog() {
  const catalogId = 1;
  const pageSize = 50;
  
  const result = await this.syncroV2Orchestrator.syncComplete(
    catalogId,
    pageSize,
    (progress) => {
      console.log(`Progresso: ${progress.categoriesProcessed} categorie processate`);
    }
  );
  
  if (result.success) {
    console.log('✅ Sincronizzazione completata');
  }
}
```

## Struttura dei File

```
src/app/service/syncro-v2/
├── syncro-v2.service.ts              # API calls al backend
├── syncro-v2-db.service.ts           # Operazioni database
├── syncro-v2-orchestrator.service.ts # Coordinamento sincronizzazione
├── image-offline.service.ts          # Gestione immagini offline
└── README.md                         # Questo file
```

## API Principali

### SyncroV2OrchestratorService
Il servizio principale per coordinare la sincronizzazione.

```typescript
// Sincronizzazione completa (categorie + categorie eliminate)
syncComplete(idCatalog: number, pageSize: number, onProgress?: (progress: SyncProgress) => void): Promise<SyncResult>

// Solo categorie
syncCatalogCategories(idCatalog: number, pageSize: number, onProgress?: (progress: SyncProgress) => void): Promise<SyncResult>

// Solo prodotti
syncCatalogProducts(idCatalog: number, pageSize: number, idRootCategory?: string, onProgress?: (progress: SyncProgress) => void): Promise<SyncResult>

// Solo categorie eliminate
syncDeletedCategories(idCatalog: number, fromTimestamp?: number, pageSize: number, onProgress?: (progress: SyncProgress) => void): Promise<SyncResult>
```

### SyncroV2Service
Gestisce le chiamate API.

```typescript
// Sincronizza categorie (paginato)
syncCategories(idCatalog: number, pageIn: number, pageSize: number): Promise<BasicResponse>

// Sincronizza prodotti (paginato)
syncProducts(idCatalog: number, pageIn: number, pageSize: number, idRootCategory?: number): Promise<BasicResponse>

// Sincronizza categorie eliminate (paginato)
syncDeletedCategories(idCatalog: number, pageIn: number, pageSize: number, fromTimestamp?: number): Promise<BasicResponse>

// Sincronizza tutti i prodotti (automatico)
syncAllProducts(idCatalog: number, pageSize: number, idRootCategory?: number, onPageCallback?: Function): Promise<BasicResponse[]>
```

### ImageOfflineService
Gestisce le immagini per l'uso offline.

```typescript
// Scarica e salva un'immagine
downloadAndSaveImage(imageUrl: string, idCatalog: number, entityType: 'PRODUCT' | 'CATEGORY', entityId: string, idRootCategory?: string): Promise<ImageMetadata | null>

// Risolve l'URL locale di un'immagine
resolveOfflineImageUrl(imageUrl: string): Promise<string | null>

// Ottiene i metadati di un'immagine
getImageMetadata(imageUrl: string): Promise<ImageMetadata | null>
```

## Esempi Pratici

### 1. Sincronizzazione con Progress Bar
```typescript
async syncWithProgress() {
  const catalogId = 1;
  let totalItems = 0;
  let processedItems = 0;
  
  const result = await this.syncroV2Orchestrator.syncComplete(
    catalogId,
    50,
    (progress: SyncProgress) => {
      if (progress.totalCount) {
        totalItems = progress.totalCount;
      }
      processedItems = progress.categoriesProcessed + progress.deletedCategoriesProcessed;
      
      const percentage = totalItems > 0 ? (processedItems / totalItems) * 100 : 0;
      this.updateProgressBar(percentage);
      
      // Aggiorna il testo di stato
      this.statusText = `Sincronizzazione in corso... ${processedItems}/${totalItems}`;
    }
  );
  
  this.statusText = result.success ? 'Sincronizzazione completata' : 'Errore durante la sincronizzazione';
}
```

### 2. Sincronizzazione Solo Prodotti di una Categoria
```typescript
async syncProductsByCategory(categoryId: string) {
  const catalogId = 1;
  const pageSize = 100;
  
  const result = await this.syncroV2Orchestrator.syncCatalogProducts(
    catalogId,
    pageSize,
    categoryId,
    (progress) => {
      console.log(`📦 Prodotti sincronizzati: ${progress.productsProcessed}`);
    }
  );
  
  console.log(`✅ Sincronizzati ${result.totalProductsProcessed} prodotti per la categoria ${categoryId}`);
}
```

### 3. Gestione Immagini Offline
```typescript
async loadProductImage(product: Product) {
  // Prova prima l'immagine offline
  const offlineUrl = await this.imageOfflineService.resolveOfflineImageUrl(product.image);
  
  if (offlineUrl) {
    // Immagine disponibile offline
    this.productImageSrc = offlineUrl;
    this.imageStatus = 'offline';
  } else {
    // Usa l'immagine online
    this.productImageSrc = product.image;
    this.imageStatus = 'online';
    
    // Scarica per il futuro (se online)
    if (await Utils.checkInternet()) {
      this.imageOfflineService.downloadAndSaveImage(
        product.image,
        this.catalogId,
        'PRODUCT',
        product.code
      );
    }
  }
}
```

### 4. Sincronizzazione Incrementale
```typescript
async syncIncremental() {
  const catalogId = 1;
  
  // Ottieni l'ultimo timestamp
  const lastSync = await this.syncroV2DbService.getLastSyncTimestamp(catalogId);
  
  if (lastSync) {
    // Sincronizza solo le modifiche dall'ultimo sync
    const result = await this.syncroV2Orchestrator.syncDeletedCategories(
      catalogId,
      lastSync,
      10
    );
    
    console.log(`🔄 Sincronizzazione incrementale: ${result.totalDeletedCategoriesProcessed} categorie rimosse`);
  } else {
    // Prima sincronizzazione, esegui sync completo
    console.log('🆕 Prima sincronizzazione, eseguo sync completo...');
    await this.syncComplete();
  }
}
```

## Configurazione

### Parametri Consigliati
```typescript
const CONFIG = {
  // Dimensioni pagina ottimali
  CATEGORIES_PAGE_SIZE: 50,    // Categorie: 50 per pagina
  PRODUCTS_PAGE_SIZE: 100,     // Prodotti: 100 per pagina
  DELETED_PAGE_SIZE: 10,       // Categorie eliminate: 10 per pagina
  
  // Timeout e retry
  API_TIMEOUT: 30000,          // 30 secondi
  MAX_RETRIES: 3,              // 3 tentativi
  RETRY_DELAY: 1000,           // 1 secondo tra i tentativi
  
  // Pulizia dati
  CLEANUP_DAYS: 30,            // Pulisci dati più vecchi di 30 giorni
  IMAGE_CLEANUP_DAYS: 7        // Pulisci immagini più vecchie di 7 giorni
};
```

### Ottimizzazione Performance
```typescript
// 1. Usa dimensioni pagina appropriate
const pageSize = this.isSlowConnection ? 25 : 100;

// 2. Limita i download simultanei di immagini
const maxConcurrentDownloads = 3;

// 3. Esegui pulizia periodica
setInterval(() => {
  this.cleanupOldData();
}, 24 * 60 * 60 * 1000); // Una volta al giorno
```

## Gestione Errori

### Pattern di Retry
```typescript
async syncWithRetry(operation: () => Promise<any>, maxRetries = 3) {
  let lastError;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      console.warn(`⚠️ Tentativo ${attempt}/${maxRetries} fallito:`, error.message);
      
      if (attempt < maxRetries) {
        // Attendi prima del prossimo tentativo (backoff esponenziale)
        await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempt - 1)));
      }
    }
  }
  
  throw new Error(`Operazione fallita dopo ${maxRetries} tentativi: ${lastError.message}`);
}
```

### Logging Strutturato
```typescript
private logSyncEvent(event: string, data: any) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    catalogId: this.catalogId,
    data
  };
  
  console.log(`🔄 [SYNC] ${event}:`, logEntry);
  
  // Opzionalmente, invia a servizio di logging
  // this.loggingService.log(logEntry);
}
```

## Testing

### Unit Test Example
```typescript
describe('SyncroV2Service', () => {
  let service: SyncroV2Service;
  
  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(SyncroV2Service);
  });
  
  it('should sync categories successfully', async () => {
    const mockResponse = {
      status: 'OK',
      content: [{ id: '1', name: 'Test Category' }],
      totalPages: 1,
      totalCount: 1
    };
    
    spyOn(service, 'basicGet').and.returnValue(Promise.resolve(mockResponse));
    
    const result = await service.syncCategories(1, 0, 10);
    
    expect(result.status).toBe('OK');
    expect(result.content.length).toBe(1);
  });
});
```

## Troubleshooting

### Problemi Comuni

**Q: La sincronizzazione si blocca**
A: Verifica la connessione internet e i log per errori API. Riduci la dimensione della pagina.

**Q: Le immagini non vengono scaricate**
A: Controlla i permessi del filesystem e la directory delle immagini in localStorage.

**Q: Database schema errors**
A: Esegui `updateDatabaseSchema()` per aggiornare lo schema.

**Q: Performance lente**
A: Riduci la dimensione delle pagine e limita i download simultanei.

### Debug Commands
```typescript
// Verifica stato database
await this.syncroV2DbService.getLastSyncTimestamp(catalogId);

// Verifica immagini offline
await this.imageOfflineService.getImageMetadata(imageUrl);

// Pulisci dati di test
await this.syncroV2Orchestrator.cleanupOldData(catalogId, 0);
```

---

Per documentazione completa, vedere: `docs/SYNCRO_V2_DOCUMENTATION.md`
