import { Customer } from "./customer";
type SimplifiedCustomer = Pick<Customer, 'uid'|'name'|'industrialSector'|'customerStatus'|'infoDataList'>
export type CustomerCardType = 'CARD' | 'LABEL'| 'PROSPECT';
export class CustomerCard extends (Customer as new () => SimplifiedCustomer) {
    type: CustomerCardType = 'CARD';

    constructor(name: string, type: CustomerCardType) {
        super();
        this.name = name;
        this.type = type;
    }
}
