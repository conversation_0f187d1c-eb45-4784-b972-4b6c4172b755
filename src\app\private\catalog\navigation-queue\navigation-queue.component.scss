.title {
    position: absolute;
    top: calc(0px + var(--ion-safe-area-top, 0));
    height: 44px;
    align-items: center;
    display: flex;
    color: white;
    z-index: 9999;
    text-transform: uppercase;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100vw - 280px);
    justify-content: flex-end;
    span.subcat:last-of-type span {
        color: var(--ion-dat-yellow);
    }
}