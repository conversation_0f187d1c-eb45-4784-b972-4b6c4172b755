import { TestBed } from '@angular/core/testing';
import { SyncroUnifiedService } from './syncro-unified.service';
import { SyncroV2Service } from '../syncro-v2/syncro-v2.service';
import { SyncroV2DbService } from '../syncro-v2/syncro-v2-db.service';
import { HybridDbService } from '../../shared/hybrid-db.service';

describe('SyncroUnifiedService', () => {
  let service: SyncroUnifiedService;
  let mockSyncroV2Service: jasmine.SpyObj<SyncroV2Service>;
  let mockSyncroV2DbService: jasmine.SpyObj<SyncroV2DbService>;
  let mockHybridDbService: jasmine.SpyObj<HybridDbService>;

  beforeEach(() => {
    const syncroV2ServiceSpy = jasmine.createSpyObj('SyncroV2Service', [
      'syncCategories',
      'getCatalogByLang',
      'syncAllCategories'
    ]);
    const syncroV2DbServiceSpy = jasmine.createSpyObj('SyncroV2DbService', [
      'updateDatabaseSchema',
      'markSyncStart',
      'saveCategoriesV2',
      'getLastSyncTimestamp'
    ]);
    const hybridDbServiceSpy = jasmine.createSpyObj('HybridDbService', [
      'clearTable',
      'getAll',
      'addRecord',
      'addRecords'
    ]);

    TestBed.configureTestingModule({
      providers: [
        SyncroUnifiedService,
        { provide: SyncroV2Service, useValue: syncroV2ServiceSpy },
        { provide: SyncroV2DbService, useValue: syncroV2DbServiceSpy },
        { provide: HybridDbService, useValue: hybridDbServiceSpy }
      ]
    });

    service = TestBed.inject(SyncroUnifiedService);
    mockSyncroV2Service = TestBed.inject(SyncroV2Service) as jasmine.SpyObj<SyncroV2Service>;
    mockSyncroV2DbService = TestBed.inject(SyncroV2DbService) as jasmine.SpyObj<SyncroV2DbService>;
    mockHybridDbService = TestBed.inject(HybridDbService) as jasmine.SpyObj<HybridDbService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should initialize database', async () => {
    mockSyncroV2DbService.updateDatabaseSchema.and.returnValue(Promise.resolve());

    await service.initializeDatabase();

    expect(mockSyncroV2DbService.updateDatabaseSchema).toHaveBeenCalled();
  });

  it('should clear tables for full sync', async () => {
    mockHybridDbService.clearTable.and.returnValue(Promise.resolve());

    await service.clearTablesForFullSync();

    expect(mockHybridDbService.clearTable).toHaveBeenCalledWith('categories');
    expect(mockHybridDbService.clearTable).toHaveBeenCalledWith('products');
    expect(mockHybridDbService.clearTable).toHaveBeenCalledWith('prospectTypes');
    expect(mockHybridDbService.clearTable).toHaveBeenCalledWith('discounts');
    expect(mockHybridDbService.clearTable).toHaveBeenCalledWith('favorites');
  });

  it('should get sync status', async () => {
    const catalogId = 1;
    const mockTimestamp = 1234567890;
    const mockCategories = [{ id: '1' }, { id: '2' }];
    const mockProducts = [{ code: 'P1' }, { code: 'P2' }];

    mockSyncroV2DbService.getLastSyncTimestamp.and.returnValue(Promise.resolve(mockTimestamp));
    mockHybridDbService.getAll.and.returnValues(
      Promise.resolve(mockCategories),
      Promise.resolve(mockProducts),
      Promise.resolve([]),
      Promise.resolve([]),
      Promise.resolve([])
    );

    const status = await service.getSyncStatus(catalogId);

    expect(status.lastSyncTimestamp).toBe(mockTimestamp);
    expect(status.categoriesCount).toBe(2);
    expect(status.productsCount).toBe(2);
    expect(status.prospectsCount).toBe(0);
    expect(status.discountsCount).toBe(0);
    expect(status.favoritesCount).toBe(0);
  });

  it('should handle sync complete with success', async () => {
    const salesOrganization = 'TEST';

    // Mock delle risposte
    mockSyncroV2DbService.updateDatabaseSchema.and.returnValue(Promise.resolve());
    mockSyncroV2Service.getCatalogByLang.and.returnValue(Promise.resolve({
      status: 'OK',
      content: { id: '1' }
    }));
    mockSyncroV2Service.syncAllCategories.and.returnValue(Promise.resolve([{
      status: 'OK',
      content: [{ id: '1', name: 'Category 1' }]
    }]));
    mockSyncroV2DbService.markSyncStart.and.returnValue(Promise.resolve());
    mockSyncroV2DbService.saveCategoriesV2.and.returnValue(Promise.resolve());
    mockHybridDbService.getAll.and.returnValue(Promise.resolve([{ id: '1' }]));

    // Mock delle chiamate API
    spyOn(service as any, 'productsbycategory').and.returnValue(Promise.resolve({
      status: 'OK',
      content: []
    }));
    spyOn(service as any, 'allprospectsbyuser').and.returnValue(Promise.resolve({
      status: 'OK',
      content: []
    }));
    spyOn(service as any, 'discountlist').and.returnValue(Promise.resolve({
      status: 'OK',
      content: []
    }));
    spyOn(service as any, 'getProducts').and.returnValue(Promise.resolve({
      status: 'OK',
      content: []
    }));

    const result = await service.syncComplete(salesOrganization);

    expect(result.success).toBe(true);
    expect(result.totalCategoriesProcessed).toBeGreaterThanOrEqual(0);
  });
});
