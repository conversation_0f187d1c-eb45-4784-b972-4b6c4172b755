import { Routes } from '@angular/router';
import { PublicAuthGuard } from './guards/public-auth.guards';

export const routes: Routes = [
  {
    path: 'private',
    canActivate: [PublicAuthGuard],
    canActivateChild: [PublicAuthGuard],
    loadComponent: () => import('./private/private.component').then(m => m.PrivateComponent),
    children: [
      { path: '', redirectTo: 'home', pathMatch: 'full' },
      {
        path: 'home',
        loadComponent: () => import('./private/home/<USER>').then(m => m.HomeComponent)
      },
      {
        path: 'settings',
        loadComponent: () => import('./private/settings/settings.component').then(m => m.SettingsComponent)
      },
      {
        path: 'customers',
        loadComponent: () => import('./private/customers/customers.component').then(m => m.CustomersComponent)
      },
      {
        path: 'syncro',
        loadComponent: () => import('./private/syncro/syncro.component').then(m => m.SyncroComponent)
      },
      {
        path: 'extras',
        loadComponent: () => import('./private/extras/extras.component').then(m => m.ExtrasComponent)
      },
      {
        path: 'catalog',
        children: [
          { path: 'product/:id', loadComponent: () => import('./private/catalog/product/product.component').then(m => m.ProductComponent) },
          { path: 'products-carousel/:id', loadComponent: () => import('./private/catalog/products-carousel/products-carousel.component').then(m => m.ProductsCarouselComponent) },
          { path: '', loadComponent: () => import('./private/catalog/catalog.component').then(m => m.CatalogComponent) },
          { path: ':rootId', loadComponent: () => import('./private/catalog/catalog.component').then(m => m.CatalogComponent) },
        ]
      },
      {
        path: 'carts',
        loadComponent: () => import('./private/carts/carts.component').then(m => m.CartsComponent),
        children: [
          { path: '', loadComponent: () => import('./private/carts/carts.component').then(m => m.CartsComponent) },
          { path: 'cart', loadComponent: () => import('./private/carts/cart/cart.component').then(m => m.CartComponent) }
        ]
      },
    ]
  },
  {
    path: 'login',
    loadComponent: () => import('./login/login.component').then(m => m.LoginComponent),
  },
  {
    path: '',
    redirectTo: 'private',
    pathMatch: 'full',
  },
];