// Ionic Variables and Theming. For more info, please see:
// http://ionicframework.com/docs/theming/

/** Ionic CSS Variables **/

@font-face {
  font-family: 'Roboto';
  src:  url('../assets/fonts/Roboto/Roboto-Regular.ttf') format('truetype');
  font-style: normal;
}

:root {
  --ion-dat-gray: #EDEDED;
  --ion-dat-red: #E10231;
  --ion-dat-orange: #FF7D23;
  --ion-dat-green: #008EA7;
  --ion-dat-white: #FFF;
  --ion-dat-middle-gray: #C2C3C7;
  --ion-dat-selection-gray: #F0F0F0;
  --ion-dat-dark-gray: #707070;
  --ion-dat-black: #2F2C2C;
  --ion-dat-success: #51BC49;
  --ion-dat-text: #2B2E34;
  --ion-dat-yellow: #FAC243;
  --ion-dat-popover: #2f2c2cda;

  // Variabili Ionic spostate in variables.scss

  // Angular Material custom variables for light theme
  --dialog-accordion-bg: #ffffff;
  --dialog-accordion-color: #2B2E34;
  --icon-color: #707070;

}