import { Component, inject } from '@angular/core';
import { CatalogCardComponent } from '../catalog-card.component';
import { IonicModule, Platform } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { caretDownOutline, logoEuro, shareSocialOutline, star, starOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
import { IonIcon } from '@ionic/angular/standalone';

@Component({
    selector: 'app-large-catalog-card',
    templateUrl: './large-catalog-card.component.html',
    styleUrls: ['./large-catalog-card.component.scss'],
    standalone: true,
    imports: [
      IonIcon,
      TranslateModule,
      CommonModule,
      SafePipe
    ]
})
export class LargeCatalogCardComponent extends CatalogCardComponent {

  protected catalogService = inject(CatalogService);
  constructor() {
    super();
    addIcons({ caretDownOutline, logoEuro, shareSocialOutline, star, starOutline });
  }
}
