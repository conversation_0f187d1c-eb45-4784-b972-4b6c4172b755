import { Component, CUSTOM_ELEMENTS_SCHEMA, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule, Location } from '@angular/common';
import { IonicModule, LoadingController } from '@ionic/angular';
import { Store, select } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { Category } from 'src/app/service/data/category';
import { Selection } from 'src/app/shared/selection';
import Utils from 'src/app/shared/utils';
import { addNavHistory, removeLastNavHistory } from 'src/app/store/actions/nav-history.actions';
import { addSelection, resetNavStack, reverseSelection } from 'src/app/store/actions/nav-stack.actions';
import { Favorite } from 'src/app/service/data/favorite';
import { Document, Product } from 'src/app/service/data/product';
import { animate, style, transition, trigger } from '@angular/animations';
import { ProductComponent } from '../product/product.component';
import { Subject } from 'rxjs';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { SwiperContainer } from 'swiper/element';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NavigationPanelComponent } from '../navigation-panel/navigation-panel.component';
import { RightBarComponent } from '../product/right-bar/right-bar.component';
import { NavigationQueueComponent } from '../navigation-queue/navigation-queue.component';
import { SmallCatalogCardComponent } from '../catalog-card/small-catalog-card/small-catalog-card.component';
import { MediumCatalogCardComponent } from '../catalog-card/medium-catalog-card/medium-catalog-card.component';
import { LargeCatalogCardComponent } from '../catalog-card/large-catalog-card/large-catalog-card.component';
import { SearchComponent } from '../../search/search.component';
import { ArticleWithAttributesComponent } from '../product/article-with-attributes/article-with-attributes.component';
import { ArticleComponent } from '../product/article/article.component';
import { DownloadComponent } from '../product/download/download.component';
import { PricesComponent } from '../product/prices/prices.component';
import { VideoplayerComponent } from '../product/videoplayer/videoplayer.component';
import { ImageViewerComponent } from '../product/image-viewer/image-viewer.component';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
@Component({
  selector: 'app-products-carousel',
  templateUrl: './products-carousel.component.html',
  styleUrls: ['./products-carousel.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  animations: [
    trigger('datacolPopup', [
      transition('void => *', [
        style({ opacity: 0 }),
        animate(200, style({ opacity: 1 }))
      ]),
      transition('* => void', [
        animate(200, style({ opacity: 0 }))
      ])
    ])
  ],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    ProductComponent,
    RightBarComponent,
    NavigationPanelComponent,
    NavigationQueueComponent,
    SearchComponent,
    ArticleComponent,
    PricesComponent,
    ArticleWithAttributesComponent,
    DownloadComponent,
    VideoplayerComponent,
    ImageViewerComponent
  ]
})
export class ProductsCarouselComponent {
  @ViewChild('carslides', { static: false }) carslides: SwiperContainer;
  @ViewChild('productComponent') productComponent: ProductComponent;
  navigationType = (this._lsService.get('catalog_navigation_type') || 'P') as 'P' | 'I';
  products: any[] = [];
  pages$: Subject<CatalogCard[]> = new Subject<CatalogCard[]>();
  isFavorite$: Subject<boolean> = new Subject<boolean>();
  pages: CatalogCard[] = [];
  current: CatalogCard | null = null;
  customerUid: string | null = '';
  customerName: string | null = '';
  isProspect: boolean = false;
  currentIndex: number = 0;
  showNavigator: boolean = false;
  isInfoShown: boolean = false;
  isPricesShown: boolean = false;
  isFilterShown: boolean = false;
  isArticleShown: boolean = false;
  isArticleWithAttributesShown: boolean = false;
  isDownloadShown: boolean = false;
  isVideoPlayerShown: boolean = false;
  isImageViewerShown: boolean = false;
  videoUrl: string | null = null;
  documentsForDownload: Document[] = [];
  articleCode: string = '';
  currentArticle: Product | null = null;
  imageToShow: string | null = null;
  private dcProperty = [];
  // private favorites = [];
  private isFirst = true;

  constructor(private _activatedRoute: ActivatedRoute, private _router: Router, public _loadingController: LoadingController,
    private _translate: TranslateService, private _store: Store<any>, private _location: Location,
    private _lsService: LocalStorageService, private catalogService: CatalogService) {
  }

  async ngOnInit() {
    console.log('ProductsCarouselComponent ngOnInit');
    var idDatacolCategory: string | null;

    this._activatedRoute
      .queryParams
      .subscribe(params => {
        this.current = this.catalogService.currentCategoryData;
        idDatacolCategory = params['idRoot'];
        this.customerUid = this.catalogService.userUid;
        this.customerName = this.catalogService.customerName;
        this.isProspect = this.catalogService.isProspect;

        let catalog = [];
        this._store.pipe(select('catalogByProduct')).subscribe(res => catalog = [...res]).unsubscribe();
        const categories: { rootId: string, items: Category[] } = catalog.find(
          (item) => !!idDatacolCategory ? item.rootId.toString() === idDatacolCategory.toString() : false);
        if (!!categories && !!categories.items) {
          this.products = categories.items.filter(item => Utils.objToJson(item.isProduct)).map((item: Category) => {
            return new CatalogCard(item.id, idDatacolCategory,
              item.name,
              item.image,
              item.isProduct,
              'CATEGORY', !!item.subcategories ? Utils.objToJson(item.subcategories) : [],
              this.isFavorite(item.id.toString()),
              item.description,
              this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N',
              null,
              this.dcProperty.filter(f => f.idSubCategory.toString() === item.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : ''
            );
          });

        }

        if (!!this.products && this.products.length > 0) {
          const currentId = !!this.current ? this.current.id.toString() : idDatacolCategory.toString();
          const index = this.products.findIndex((item) => item.id.toString() === currentId);
          this.pages = this.createEmptyPages(this.products.length);
          if (!!this.current) {
            this.pages[index] = this.current;
          } else {
            const list = this.products.filter((prod: CatalogCard) => prod.id.toString() === currentId);
            if (list.length > 0)
              this.pages[index] = list[0];
          }
          this.currentIndex = index;
        }
      });
  }

  slideChanges(event) {
    console.log(event);
  }

  markFavorite(isFavorite: boolean) {
    this.isFavorite$.next(isFavorite);
  }

  createEmptyPages(index: number): (CatalogCard | null)[] {
    const arr: (CatalogCard | null)[] = [];

    for (let i = 0; i < index; i++) {
      arr.push(null);
    }

    return arr;
  }

  private isFavorite(idCategory: string) {
    let favorites: Favorite[] = [];
    this._store.pipe(select('favorites')).subscribe(res => favorites = [...res]).unsubscribe();
    if (!!this.customerUid)
      favorites = favorites.filter((favorite: Favorite) => favorite.customerUid.toString() === this.customerUid.toString()
        && favorite.idCategory.toString() === idCategory);
    const found = favorites.length === 1;
    return found;
  }


  async goToCategory(event) {
    console.log(this.currentIndex);
    const params = {
      queryParams: {
        customerUid: this.customerUid,
        customerName: this.customerName,
        isProspect: this.isProspect,
        showFavorites: false,
        clickedNavarId: event.idCategory,
        turnAround: true,
        comeFromDC: (this.currentIndex >= 0) ? this.products[this.currentIndex].id : null
      }
    };
    this._router.navigate(['/private/catalog'], params);
  }

  async navToPage(event) {
    this._router.navigate(['/private/catalog'], {
      queryParams: {
        customerUid: this.customerUid,
        customerName: this.customerName,
        isProspect: this.isProspect,
        showFavorites: false, // usando questa navigazione si esce dai favoriti
        clickedNavarId: null,
        turnAround: false,
        navToPage: JSON.stringify(event)
      }
    });
  }

  openInfo() {
    this.isInfoShown = !this.isInfoShown;
  }

  openPrices() {
    this.isPricesShown = true;
  }

  hidePrices() {
    this.isPricesShown = false;
  }

  showArticle(product) {
    this.isArticleShown = true;
    this.currentArticle = product;
  }

  addOneToCart(product) {
    this.productComponent.addOneToCart(product);
  }

  hideArticle() {
    this.isArticleShown = false;
    this.currentArticle = null;
  }

  openArticleWithAttributes(product) {
    this.isArticleWithAttributesShown = true;
    this.currentArticle = product;
  }

  hideArticleWithAttributes() {
    this.isArticleWithAttributesShown = false;
    this.currentArticle = null;
  }

  openPdf(product: Product) {
    this.documentsForDownload = product.documents;
    this.articleCode = product.code;
    this.isDownloadShown = true;
  }

  hideDownload() {
    this.isDownloadShown = false;
    this.documentsForDownload = [];
    this.articleCode = '';
  }

  openVideo(product: Product) {
    // https://www.youtube-nocookie.com/embed/bESGLojNYSo
    this.videoUrl = 'https://www.youtube-nocookie.com/embed/' + product.mediaVideoURL.substring(product.mediaVideoURL.lastIndexOf('/') + 1);
    this.isVideoPlayerShown = true;
  }

  hideVideo() {
    this.isVideoPlayerShown = false;
  }

  openImageViewer(imageUrl) {
    if (true || !!imageUrl) {
      this.imageToShow = imageUrl;
      this.isImageViewerShown = true;
    }
  }

  hideImageViewer() {
    this.isImageViewerShown = false;
    this.imageToShow = null;
  }

  openSearch() {
    this.isFilterShown = true;
  }

  async closeProductSearch(selectedProduct: Product) {
    if (!!selectedProduct) {
      const idSubCategory = selectedProduct.idSubCategory;
      this.isFilterShown = false;
      if (!!idSubCategory) {
        const loading = await this._loadingController.create({
          message: this._translate.instant('GENERICS.WAIT'),
        });
        loading.present();
        this._store.dispatch(resetNavStack());
        const lastCategory: Category = await this.getProductFromHierarchy(idSubCategory).then((lastCategory) => lastCategory);
        let navStack = [];
        this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
        navStack = navStack.map((roba) => (roba.item as Selection).id);
        const isFavorite = this.isProspect ? false : this.isFavorite(lastCategory.id.toString());
        this.dcProperty.length === 0 && this._store.pipe(select('dcproperty')).subscribe(res => this.dcProperty = [...res]).unsubscribe();
        const datacolCategory = new CatalogCard(lastCategory.id, navStack[0], lastCategory.name, lastCategory.image, lastCategory.isProduct,
          'CATEGORY', Utils.objToJson(lastCategory.subcategories), isFavorite, lastCategory.description,
          (this.dcProperty.filter(f => f.idSubCategory.toString() === lastCategory.id.toString() && f.focus == 'S').length > 0 ? 'S' : 'N'),
          null,
          (this.dcProperty.filter(f => f.idSubCategory.toString() === lastCategory.id.toString() && f.divisionStatusCode == 'Z3').length > 0 ? 'Z3' : '')
        );
        this._store.dispatch(addNavHistory({ item: { type: 'PRODUCT', id: lastCategory.id.toString(), path: navStack, datacolCategory: { category: datacolCategory, isFavorite: isFavorite } } }));

        this._router.navigate([`/private/catalog/products-carousel/${idSubCategory}`], {
          queryParams: {
            idRootCategory: navStack[0],
            current: JSON.stringify(datacolCategory),
            datacolCategory: JSON.stringify(lastCategory),
            customerUid: this.customerUid,
            customerName: this.customerName,
            isFavorite: isFavorite,
            isProspect: this.isProspect,
          }
        }).finally(() => {
          loading.dismiss();
        });
      }
    } else {
      this.isFilterShown = false;
    }
  }

  private async getProductFromHierarchy(id: string): Promise<Category> {
    let rootCategories = [];
    this._store.pipe(select('categories')).subscribe(res => rootCategories = res).unsubscribe();
    const category = this.findProductInCategory(rootCategories, id);
    this._store.dispatch(reverseSelection());
    return category;
  }

  private findProductInCategory(categories: Category[], idSubCategory: string): Category | null {
    let navStack = [];
    this._store.pipe(select('navStack')).subscribe(res => navStack = res).unsubscribe();
    navStack = navStack.map((roba) => (roba.item as Selection).id);
    if (categories && categories.length > 0) {
      const list = categories.filter((category: Category) => category.id.toString() === idSubCategory);
      if (!!list && list.length > 0) {
        if (!list[0].isProduct)
          this._store.dispatch(addSelection({ item: { name: list[0].name, id: list[0].id.toString(), isProduct: list[0].isProduct, idRootCategory: navStack[0] } }));
        return list[0];
      }
      else {
        let found = null;
        categories.forEach(async (category: Category) => {
          if (!!found)
            return;
          else {
            const subcategory = !!category.subcategories ? Utils.objToJson(category.subcategories) : [];
            if (subcategory.length > 0) {
              found = this.findProductInCategory(subcategory, idSubCategory);
              if (found && !category.isProduct)
                this._store.dispatch(addSelection({ item: { name: category.name, id: category.id.toString(), isProduct: category.isProduct, idRootCategory: navStack[0] } }));
            }
            else
              found = null;
          }
        });
        return found;
      }
    } else {
      return null;
    }
  }

  productGoComponentGoBack() {
    let navStackSize = 0;
    const navigationQueue$ = this._store.pipe(select('navStack'));
    navigationQueue$.subscribe(res => navStackSize = res.length).unsubscribe();
    if (navStackSize === 0) {
      let routingStack = [];
      this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
      let condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      while (condition) {
        routingStack.pop();
        condition = routingStack.length > 0 && routingStack[routingStack.length - 1].component.includes('catalog');
      }
      if (routingStack.length > 0)
        this._router.navigate([routingStack[0].component]);
      else
        this._router.navigate(['/private/home']);
    } else {
      let navHistory: { item: { type: 'CATEGORY' | 'PRODUCT', id: string, path: string[], datacolCategory: null | { category: CatalogCard, isFavorite: boolean } } }[] = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
      // rimuovo il corrente che non serve
      if (navHistory.length > 0 && navHistory[navHistory.length - 1].item.type === 'PRODUCT' && navHistory[navHistory.length - 1].item.id.toString() === this.current.id.toString())
        navHistory = navHistory.slice(0, -1);
      if (navHistory.length === 0) {
        this._router.navigate(['/private/home']);
      }
      else {
        if (navHistory.length > 0 && navHistory[navHistory.length - 1].item.type === 'PRODUCT') {
          const lastHistory = navHistory[navHistory.length - 1];
          this._store.dispatch(removeLastNavHistory());
          this._store.dispatch(resetNavStack());
          this.setNewHierarchy(lastHistory.item.path);

          this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], {
            queryParams: {
              idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
              current: JSON.stringify(lastHistory.item.datacolCategory.category),
              customerUid: this.customerUid,
              customerName: this.customerName,
              isFavorite: lastHistory.item.datacolCategory.isFavorite,
              isProspect: this.isProspect,
            }
          });

        } else if (navHistory.length > 0) {
          this._store.dispatch(removeLastNavHistory());
          const lastNavHistory = navHistory[navHistory.length - 1].item;
          this.goToCategory({ idCategory: lastNavHistory.id });
        } else {
          this._location.back();
        }
      }
    }
  }
  private setNewHierarchy(navHistoryPath: string[]) {
    let categories = [];
    this._store.pipe(select('categories')).subscribe(res => categories = res).unsubscribe();
    navHistoryPath.forEach(element => {
      const filtered = categories.filter(item => item.id.toString() === element.toString());
      if (filtered.length > 0 && !!filtered[0].name && !!filtered[0].id && !!filtered[0].isProduct && !!navHistoryPath && !!navHistoryPath[0])
        this._store.dispatch(addSelection({ item: { name: filtered[0].name, id: filtered[0].id.toString(), isProduct: filtered[0].isProduct, idRootCategory: navHistoryPath[0] } }));
      if (filtered.length > 0 && !!filtered[0].subcategories)
        categories = Utils.objToJson(filtered[0].subcategories);
    });
  }
}
