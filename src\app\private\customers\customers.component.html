<div #title id="title" class="title">
    {{ 'CUSTOMER_LIST.TITLE' | translate }}
  </div>
<div class="container">
    <div *ngIf="customers.length == 0">
        {{ 'CUSTOMER_LIST.EMPTY' | translate }}
    </div>

    <div id="cards" class="column cards col-{{columns}}">
        <swiper-container #slides [options]="slideOpts" (ionSlideDidChange)="slideChanges(slides)">
            <swiper-slide *ngFor="let item of [].constructor(totalPages); let i = index">
                <div class="slider-grid">
                    <app-customer-card [customer]="customer" [selectedCustomerUid]="selectedCustomerUid" *ngFor="let customer of getPaginatedCardForPage(i)" (openDetail)="openDetail($event)"></app-customer-card>
                </div>
            </swiper-slide>
        </swiper-container>
        <div class="pagination">
            <div class="letter" (click)="goToLetter('#')"><span [ngClass]="{'current': currentLetter === '#' }">#</span></div>
            <div class="letter" (click)="goToLetter(item)" *ngFor="let item of getAlphabet()"><span [ngClass]="{'empty': isDisabled(item), 'current': currentLetter === item  }">{{item}}</span></div>
        </div>
    </div>
    <div class="overlay" *ngIf="detailIsOpen"></div>
    <div *ngIf="detailIsOpen" #detail class="detail" [@flyInOut]>
        <app-customer-detail [customerUid]="selectedCustomerUid" (closeDetail)="closeDetail($event)"></app-customer-detail>
    </div>
</div>
<app-prospect-types *ngIf="isProspectTypesShown" (closeProspectTypes)="closeProspectTypes()"></app-prospect-types>
<app-search *ngIf="isFilterShown" [viewType]="'customers'" [simpleView]="true" (closeSearch)="closeCustomerSearch($event)"></app-search>
