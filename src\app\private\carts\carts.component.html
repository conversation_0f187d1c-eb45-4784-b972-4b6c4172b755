<div #title id="title" class="title">
  {{ 'CARTS.TITLE' | translate }}
</div>
<div class="container">
  <h1>{{ 'CARTS.CARTS_LIST' | translate }}</h1>
  <div class="counter-container">
    <div class="cart-counter">{{carts.length}}</div>
  </div>
  <div class="toolbar">
    <div class="button-bar">
      <ion-button *ngIf="carts.length > 0" (click)="transferCarts()" 
        [disabled]="sending" class="tb-btn"
        [ngClass]="{'sending': sending}">
        {{ 'CARTS.TRANSFER_ALL_CARTS' | translate }}
      </ion-button>
      <ion-button *ngIf="carts.length == 0" class="tb-btn transfered" disabled="true" >
        {{ 'CARTS.CARTS_TRANSFERED' | translate }}
      </ion-button>
    </div>
  </div>
  <ion-list *ngIf="carts.length > 0">
    <ion-item class="list-header">
      <div class="row">
        <div class="cart-number"></div>
        <div class="cart"></div>
        <div class="order-row-number">{{ 'CARTS.ROWS' | translate }}</div>
        <div class="customer">{{ 'CARTS.CUSTOMER' | translate }}</div>
      </div>
    </ion-item>
    <ion-item-sliding *ngFor="let item of carts | cartItem">
      <ion-item>
        <div class="row" (click)="goToCart($event,item.id, item.idCustomer, item.name)">
          <div class="cart-number">{{item.id}}</div>
          <div class="cart"><ion-icon name="cart-outline" ></ion-icon></div>
          <div class="order-row-number">{{item.quantity}}</div>
          <div class="customer">
            <div class="code" >{{item.idCustomer}}</div>
            <div class="business-name" [innerHTML]="item.name"></div>
          </div>
          <div class="item-sliding-animation">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </div>
        </div>
      </ion-item>
      <ion-item-options side="end">
        <ion-item-option (click)="transferCart(item.id)" [disabled]="sending">{{ 'CARTS.TRANSFER' | translate }}</ion-item-option>
      </ion-item-options>
    </ion-item-sliding>
  </ion-list>
</div>