# See http://help.github.com/ignore-files/ for more about ignoring files.

# Compiled output
/dist
/www
/tmp
/out-tsc
/bazel-out

# Node
/node_modules
npm-debug.log
yarn-error.log

# IDEs and editors
.idea/
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# Visual Studio Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.history/*

# Miscellaneous
/.angular/cache
.sass-cache/
/connect.lock
/coverage
/libpeerconnection.log
testem.log
/typings

# System files
.DS_Store
Thumbs.db

/ios/App/App/GoogleService-Info.plist
/android/app/google-services.json
capacitor.config.json
src/assets/env.json
src/environments/app.configurationn.json
src/environments/build-target.json

android/app/src/main/assets/public
ios/App/Pods
public/
ios/App/Podfile.lock
ios/App/App.xcodeproj/xcuserdata/rmasenelli.xcuserdatad/xcschemes/xcschememanagement.plist
ios/App/App.xcworkspace/xcuserdata/rmasenelli.xcuserdatad/UserInterfaceState.xcuserstate
ios/App/Podfile.lock
android/local.properties
android/.gradle
android/.idea
android/capacitor-cordova-android-plugins
android/app/build
android/.gradle
/release
.DS_Store
src/.DS_Store
ios/DerivedData
android/app/release/app-release.aab


devextreme-license.ts