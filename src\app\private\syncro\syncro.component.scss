.title {
  position: absolute;
  top: calc(0px + var(--ion-safe-area-top, 0));
  height: 44px;
  align-items: center;
  display: flex;
  color: white;
  z-index: 9999;
  text-transform: uppercase;
  font-weight: bolder;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: calc(100vw - 280px);
  justify-content: flex-end;
}
.container {
    text-align: center;
    padding: 3em;
    line-height: 40px;
    min-height: 85vh;
    .lb-1 {
        font-weight: bold;
        font-size: 18px;
    }
    .lb-2 {
        font-size: 16px;
    }
    .progress-bar-block {
        width: 90%;
        margin: auto;
        margin-top: 3em;
        ion-button {
            float: right;
            margin-top: 30px;
        }
        .percent {
            color: var(--ion-dat-middle-gray);
            float: left;
        }
    }
    .progress-bar {
        width: 100%;
        height: 45px;
        -moz-transform: skew(335deg);
        -webkit-transform: skew(335deg);
        -ms-transform: skew(335deg);
        -o-transform: skew(335deg);
        transform: skew(335deg);
        background-color: var(--ion-dat-gray);
    }
    
    .progress-bar-fill {
        background-color: var(--ion-dat-red);
        border: 1px solid var(--ion-dat-red);
        height: 100%; /* This is important */
    }
}

.lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 10px;
    height: 10px;
  }
  .lds-ellipsis div {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--ion-dat-middle-gray);
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
  }
  .lds-ellipsis div:nth-child(1) {
    left: 8px;
    animation: lds-ellipsis1 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(2) {
    left: 8px;
    animation: lds-ellipsis2 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(3) {
    left: 32px;
    animation: lds-ellipsis2 0.6s infinite;
  }
  .lds-ellipsis div:nth-child(4) {
    left: 56px;
    animation: lds-ellipsis3 0.6s infinite;
  }
  @keyframes lds-ellipsis1 {
    0% {
      transform: scale(0);
    }
    100% {
      transform: scale(1);
    }
  }
  @keyframes lds-ellipsis3 {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(0);
    }
  }
  @keyframes lds-ellipsis2 {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(24px, 0);
    }
  }  