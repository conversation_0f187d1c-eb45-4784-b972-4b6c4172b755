import { Pipe, PipeTransform } from '@angular/core';
import { Cart } from '../service/data/cart';
import Utils from './utils';

@Pipe({
    name: 'inCart',
    standalone: true,
    pure: false
})
export class InCartPipe implements PipeTransform {
  constructor() { }
  transform(cart: Cart, code: string) {
    if(!!cart && !!cart.products)
    {
      return Utils.objToJson(cart.products).filter(item => item.idProduct === code).length;
    }
    else 
      return 0;
  }
}