.container {
    margin: 30px;
    text-wrap: nowrap;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.row {
    margin-top: 15px;
    display: flex;
    align-items: center;
}

.title {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    display: block;
}

.multiplier {
    width: 62px;
    height: 40px;
    text-align: right;
    border: 1px solid gray;
    border-radius: 5px;
    padding: 8px;
}

.item-btn{
    width: 40px;
    height: 40px;
    --padding-start: 0;
    --padding-end: 0;
    --border-radius: 5px;
    --background: var(--ion-dat-black);
}

