/**
 * Esempi di utilizzo del Sistema di Sincronizzazione v2
 * 
 * Questo file contiene esempi pratici di come utilizzare il nuovo sistema
 * di sincronizzazione v2 nell'applicazione Datacol.
 */

import { Component, OnInit } from '@angular/core';
import { SyncroV2OrchestratorService, SyncProgress, SyncResult } from '../syncro-v2-orchestrator.service';
import { SyncroV2DbService } from '../syncro-v2-db.service';
import { ImageOfflineService } from '../image-offline.service';
import { LoadingController, ToastController } from '@ionic/angular';

@Component({
  selector: 'app-syncro-v2-example',
  template: `
    <ion-content>
      <ion-card>
        <ion-card-header>
          <ion-card-title>Sincronizzazione v2</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-progress-bar [value]="syncProgress" color="primary"></ion-progress-bar>
          <p>{{ syncStatus }}</p>
          
          <ion-button (click)="syncCompleteExample()" [disabled]="isSyncing">
            Sincronizzazione Completa
          </ion-button>
          
          <ion-button (click)="syncProductsOnlyExample()" [disabled]="isSyncing">
            Solo Prodotti
          </ion-button>
          
          <ion-button (click)="syncIncrementalExample()" [disabled]="isSyncing">
            Sincronizzazione Incrementale
          </ion-button>
        </ion-card-content>
      </ion-card>
    </ion-content>
  `
})
export class SyncroV2ExampleComponent implements OnInit {

  syncProgress = 0;
  syncStatus = 'Pronto per la sincronizzazione';
  isSyncing = false;
  catalogId = 1; // ID del catalogo da configurare

  constructor(
    private syncroV2Orchestrator: SyncroV2OrchestratorService,
    private syncroV2DbService: SyncroV2DbService,
    private imageOfflineService: ImageOfflineService,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {}

  async ngOnInit() {
    // Inizializza il database all'avvio del componente
    try {
      await this.syncroV2Orchestrator.initializeDatabase();
      console.log('✅ Database v2 inizializzato');
    } catch (error) {
      console.error('❌ Errore inizializzazione database:', error);
    }
  }

  /**
   * Esempio 1: Sincronizzazione Completa
   * Sincronizza categorie e categorie eliminate con progress tracking
   */
  async syncCompleteExample() {
    const loading = await this.loadingController.create({
      message: 'Sincronizzazione in corso...',
      spinner: 'crescent'
    });
    await loading.present();

    this.isSyncing = true;
    this.syncProgress = 0;
    this.syncStatus = 'Avvio sincronizzazione completa...';

    try {
      const pageSize = 50;
      let totalItems = 0;
      let processedItems = 0;

      const result: SyncResult = await this.syncroV2Orchestrator.syncComplete(
        this.catalogId,
        pageSize,
        (progress: SyncProgress) => {
          // Aggiorna il progresso
          if (progress.totalCount && totalItems === 0) {
            totalItems = progress.totalCount;
          }

          processedItems = progress.categoriesProcessed + progress.deletedCategoriesProcessed;
          
          if (totalItems > 0) {
            this.syncProgress = processedItems / totalItems;
          }

          this.syncStatus = `Pagina ${progress.currentPage}/${progress.totalPages || '?'} - ` +
                           `Categorie: ${progress.categoriesProcessed}, ` +
                           `Eliminate: ${progress.deletedCategoriesProcessed}`;

          console.log('📊 Progresso sincronizzazione:', progress);
        }
      );

      await loading.dismiss();

      if (result.success) {
        this.syncStatus = `✅ Sincronizzazione completata in ${(result.duration / 1000).toFixed(1)}s`;
        this.syncProgress = 1;
        
        await this.showToast(
          `Sincronizzazione completata! ` +
          `Categorie: ${result.totalCategoriesProcessed}, ` +
          `Eliminate: ${result.totalDeletedCategoriesProcessed}`,
          'success'
        );
      } else {
        this.syncStatus = `❌ Errore: ${result.error}`;
        await this.showToast(`Errore durante la sincronizzazione: ${result.error}`, 'danger');
      }

    } catch (error) {
      await loading.dismiss();
      this.syncStatus = `❌ Errore critico: ${error.message}`;
      await this.showToast(`Errore critico: ${error.message}`, 'danger');
      console.error('❌ Errore sincronizzazione completa:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Esempio 2: Sincronizzazione Solo Prodotti
   * Sincronizza solo i prodotti di una categoria specifica
   */
  async syncProductsOnlyExample() {
    this.isSyncing = true;
    this.syncProgress = 0;
    this.syncStatus = 'Sincronizzazione prodotti...';

    try {
      const idRootCategory = '1002196'; // Categoria specifica
      const pageSize = 100;

      const result: SyncResult = await this.syncroV2Orchestrator.syncCatalogProducts(
        this.catalogId,
        pageSize,
        idRootCategory,
        (progress: SyncProgress) => {
          if (progress.totalCount) {
            this.syncProgress = (progress.productsProcessed || 0) / progress.totalCount;
          }
          
          this.syncStatus = `Prodotti sincronizzati: ${progress.productsProcessed || 0}`;
          console.log('📦 Progresso prodotti:', progress);
        }
      );

      if (result.success) {
        this.syncStatus = `✅ ${result.totalProductsProcessed} prodotti sincronizzati`;
        this.syncProgress = 1;
        
        await this.showToast(
          `${result.totalProductsProcessed} prodotti sincronizzati per la categoria ${idRootCategory}`,
          'success'
        );
      } else {
        this.syncStatus = `❌ Errore prodotti: ${result.error}`;
        await this.showToast(`Errore sincronizzazione prodotti: ${result.error}`, 'danger');
      }

    } catch (error) {
      this.syncStatus = `❌ Errore critico prodotti: ${error.message}`;
      await this.showToast(`Errore critico prodotti: ${error.message}`, 'danger');
      console.error('❌ Errore sincronizzazione prodotti:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Esempio 3: Sincronizzazione Incrementale
   * Sincronizza solo le modifiche dall'ultima sincronizzazione
   */
  async syncIncrementalExample() {
    this.isSyncing = true;
    this.syncProgress = 0;
    this.syncStatus = 'Controllo ultima sincronizzazione...';

    try {
      // Ottieni l'ultimo timestamp di sincronizzazione
      const lastTimestamp = await this.syncroV2DbService.getLastSyncTimestamp(this.catalogId);
      
      if (!lastTimestamp) {
        this.syncStatus = '⚠️ Nessuna sincronizzazione precedente trovata';
        await this.showToast(
          'Nessuna sincronizzazione precedente. Esegui prima una sincronizzazione completa.',
          'warning'
        );
        this.isSyncing = false;
        return;
      }

      this.syncStatus = `Sincronizzazione incrementale dal ${new Date(lastTimestamp).toLocaleString()}...`;

      const result: SyncResult = await this.syncroV2Orchestrator.syncDeletedCategories(
        this.catalogId,
        lastTimestamp,
        10,
        (progress: SyncProgress) => {
          this.syncProgress = progress.currentPage / (progress.totalPages || 1);
          this.syncStatus = `Categorie eliminate controllate: ${progress.deletedCategoriesProcessed}`;
        }
      );

      if (result.success) {
        this.syncStatus = `✅ Sincronizzazione incrementale completata`;
        this.syncProgress = 1;
        
        const message = result.totalDeletedCategoriesProcessed > 0 
          ? `${result.totalDeletedCategoriesProcessed} categorie rimosse`
          : 'Nessuna modifica trovata';
          
        await this.showToast(message, 'success');
      } else {
        this.syncStatus = `❌ Errore incrementale: ${result.error}`;
        await this.showToast(`Errore sincronizzazione incrementale: ${result.error}`, 'danger');
      }

    } catch (error) {
      this.syncStatus = `❌ Errore critico incrementale: ${error.message}`;
      await this.showToast(`Errore critico incrementale: ${error.message}`, 'danger');
      console.error('❌ Errore sincronizzazione incrementale:', error);
    } finally {
      this.isSyncing = false;
    }
  }

  /**
   * Esempio 4: Gestione Immagini Offline
   * Mostra come gestire le immagini per l'uso offline
   */
  async handleOfflineImageExample(imageUrl: string, productCode: string) {
    try {
      // Controlla se l'immagine è già disponibile offline
      const offlineUrl = await this.imageOfflineService.resolveOfflineImageUrl(imageUrl);
      
      if (offlineUrl) {
        console.log('📷 Immagine disponibile offline:', offlineUrl);
        return offlineUrl;
      }

      // Se non è disponibile offline, scaricala
      console.log('📥 Scaricamento immagine per uso offline...');
      const metadata = await this.imageOfflineService.downloadAndSaveImage(
        imageUrl,
        this.catalogId,
        'PRODUCT',
        productCode
      );

      if (metadata) {
        console.log('✅ Immagine salvata offline:', metadata.fileName);
        return await this.imageOfflineService.resolveOfflineImageUrl(imageUrl);
      } else {
        console.warn('⚠️ Impossibile scaricare l\'immagine');
        return imageUrl; // Fallback all'URL originale
      }

    } catch (error) {
      console.error('❌ Errore gestione immagine offline:', error);
      return imageUrl; // Fallback all'URL originale
    }
  }

  /**
   * Esempio 5: Pulizia Dati Obsoleti
   * Pulisce i dati di sincronizzazione più vecchi di X giorni
   */
  async cleanupOldDataExample() {
    try {
      const daysOld = 30; // Pulisci dati più vecchi di 30 giorni
      
      this.syncStatus = 'Pulizia dati obsoleti...';
      
      await this.syncroV2Orchestrator.cleanupOldData(this.catalogId, daysOld);
      
      // Pulisci anche le immagini obsolete
      const cutoffTimestamp = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
      await this.imageOfflineService.cleanupOldImages(cutoffTimestamp, this.catalogId);
      
      this.syncStatus = '✅ Pulizia completata';
      await this.showToast(`Dati più vecchi di ${daysOld} giorni rimossi`, 'success');
      
    } catch (error) {
      this.syncStatus = `❌ Errore pulizia: ${error.message}`;
      await this.showToast(`Errore durante la pulizia: ${error.message}`, 'danger');
      console.error('❌ Errore pulizia dati:', error);
    }
  }

  /**
   * Utility per mostrare toast messages
   */
  private async showToast(message: string, color: 'success' | 'danger' | 'warning' = 'success') {
    const toast = await this.toastController.create({
      message,
      duration: 3000,
      color,
      position: 'bottom'
    });
    await toast.present();
  }

  /**
   * Esempio 6: Monitoraggio Stato Sincronizzazione
   * Mostra come monitorare lo stato della sincronizzazione
   */
  async checkSyncStatusExample() {
    try {
      const lastTimestamp = await this.syncroV2DbService.getLastSyncTimestamp(this.catalogId);
      
      if (lastTimestamp) {
        const lastSyncDate = new Date(lastTimestamp);
        const now = new Date();
        const hoursSinceLastSync = (now.getTime() - lastSyncDate.getTime()) / (1000 * 60 * 60);
        
        console.log(`📊 Ultima sincronizzazione: ${lastSyncDate.toLocaleString()}`);
        console.log(`⏰ Ore dall'ultima sincronizzazione: ${hoursSinceLastSync.toFixed(1)}`);
        
        if (hoursSinceLastSync > 24) {
          await this.showToast(
            'La sincronizzazione è datata. Considera di eseguire un aggiornamento.',
            'warning'
          );
        }
      } else {
        console.log('❓ Nessuna sincronizzazione precedente trovata');
        await this.showToast(
          'Nessuna sincronizzazione trovata. Esegui la prima sincronizzazione.',
          'warning'
        );
      }
    } catch (error) {
      console.error('❌ Errore controllo stato sincronizzazione:', error);
    }
  }
}
