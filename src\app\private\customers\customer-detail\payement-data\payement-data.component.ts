import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Customer } from 'src/app/service/data/customer';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-payement-data',
    templateUrl: './payement-data.component.html',
    styleUrls: ['./payement-data.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class PayementDataComponent {
  @Input() customer: Customer = null;

  constructor() { }
}
