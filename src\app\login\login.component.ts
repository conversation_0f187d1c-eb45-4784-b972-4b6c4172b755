import { Component, ViewChild } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { <PERSON>ert<PERSON>ontroller, LoadingController, Platform, ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BasicResponse } from '../service/data/basic-response';
import { LoginRequest } from '../service/data/login-request';
import { LoginResponse } from '../service/data/login-response';
import { UserSetting } from '../service/data/user-settings';
import { JWTTokenService } from '../service/jwttoken.service';
import { LoginService } from '../service/login/login.service';
import { StartDbService } from '../service/start-db/start-db.service';
import { HybridDbService } from '../shared/hybrid-db.service';
import { Observable } from 'rxjs';
import Utils from '../shared/utils';
import { NativeBiometric } from "capacitor-native-biometric";
import { SplashScreen } from '@capacitor/splash-screen';
import { App } from '@capacitor/app';
import { Device } from '@capacitor/device';
import { CommonModule } from '@angular/common';
import { IonButton, IonCol, IonGrid, IonRow, IonInput, IonItem, IonNote, IonIcon } from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { person, lockClosed } from 'ionicons/icons';
import { Capacitor } from '@capacitor/core';

@Component({
    selector: 'app-login',
    templateUrl: './login.component.html',
    styleUrls: ['./login.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      IonGrid,
      IonRow,
      IonCol,
      IonButton,
      IonInput,
      IonNote,
      IonIcon
    ]
})
export class LoginComponent {
  secureKey =  '6wECWUVzuNOWP3rSqCpdMpxVKJ9AKdzX';
  secureIV = 'TLTF4KtFYb8N9LU7';
  public appIsOnline$: Observable<boolean> = undefined;
  @ViewChild("formLogin", { static: false }) formLogin: IonInput;
  @ViewChild("formPassword", { static: false }) formPassword: IonInput;
  loginFormGroup: UntypedFormGroup;
  formLoginValue: string = '';
  formPasswordValue: string = '';
  appVersion: string = "0.0.0";
  formIsInvalid: boolean = false;
  customError: string = null;
  credetialDomain: string = 'www.datacol.com';

  constructor(
    private _platform: Platform,
    private _translate: TranslateService, 
    private _jwtService: JWTTokenService,
    private _service: LoginService,
    private _startDb: StartDbService, 
    private _dbService: HybridDbService,
    private _loadingController: LoadingController, 
    private _toastController: ToastController,
    private _alertController: AlertController,
    private _router: Router, 
    private formBuilder: UntypedFormBuilder)
  {
    addIcons({ person, lockClosed });
    this.loginFormGroup = this.formBuilder.group({
      'formLogin': ['', [Validators.required, Validators.pattern('^[\\w-\\.]+@datacol.com$')]],
      'formPassword': ['', [Validators.required]]
    });
    this._platform.ready().then(() => {
      if (Capacitor.isNativePlatform()) {
        App.getInfo().then((info) => {
          this.appVersion = info.version;
        })
      }
    });
    const browserLang = this._translate.getBrowserLang();
    this._translate.use(browserLang.match(/en|it|fr|es|pl|de/) ? browserLang : 'it').subscribe(() => {
      this._translate.reloadLang(browserLang.match(/en|it|fr|es|pl|de/) ? browserLang : 'it');
    });
  }

  async ngAfterViewInit(){
    await SplashScreen.hide();
    this.performBiometricVerification();
  }

  async performBiometricVerification(){
    if (this._platform.is('capacitor'))
    {
      const result = await NativeBiometric.isAvailable({useFallback:true});
      if(!result.isAvailable && !!localStorage.getItem('tmpusn')){
        this.formLoginValue = localStorage.getItem('tmpusn'),
        this.loginFormGroup = this.formBuilder.group({
          'formLogin': localStorage.getItem('tmpusn'),
          'formPassword': ''
        });
        return;
      }

      const credentials = await NativeBiometric.getCredentials({
        server: this.credetialDomain,
      })

      if(!!credentials)
      {
        // result.biometryType:
        // TOUCH_ID	TouchID is available (iOS)
        // FACE_ID	FaceID is available (iOS)
        // FINGERPRINT	Fingerprint is available (Android)
        // FACE_AUTHENTICATION	Face Authentication is available (Android)
        // IRIS_AUTHENTICATION

        const verified = await NativeBiometric.verifyIdentity({
          reason: this._translate.instant('LOGIN.CREDENTIAL_REASON'),
          title: this._translate.instant('LOGIN.CREDENTIAL_TITLE'),
          subtitle: this._translate.instant('LOGIN.CREDENTIAL_SUBTITLE'),
          description: this._translate.instant('LOGIN.CREDENTIAL_DESCRIPTION'),
          useFallback: true
        })
          .then(() => true)
          .catch(() => false);
        if(!verified) return;
        else {
          this.login(credentials);
        }
      }
    } else {
      if(!!localStorage.getItem('tmpusn'))
      {
        this.formLoginValue = localStorage.getItem('tmpusn');
        this.loginFormGroup = this.formBuilder.group({
          'formLogin': localStorage.getItem('tmpusn'),
          'formPassword': ''
        });
      }
      return;
    }
  }

  onChange(event){
    this.formIsInvalid = this.loginFormGroup.invalid;
    this.customError = null;
  }

  async login(credentials){
    if(!!credentials || this.specificControl())
    {
      const loading = await this._loadingController.create({
        message: this._translate.instant('GENERICS.WAIT')
      });
      loading.present();

      // checking internet connection
      if (Utils.isActiveConnection()) {
        this.onlineLogin(credentials, loading);
      }
      else {
        this.offlineLogin(credentials, loading);
      }
    }
  }

  private async onlineLogin(credentials, loading) {
    const input: LoginRequest = {
      username: !!credentials ? credentials.username : this.formLoginValue.toLocaleLowerCase(),
      password: !!credentials ? credentials.password : this.formPasswordValue,
      uuid: (this._platform.is('capacitor') ? await Device.getId().toString() : 'DEV-UUID-FAKE'),
      manufacturer:  (this._platform.is('capacitor') ? await Device.getInfo().then(info => info.manufacturer) : 'MANUFACTURER-FAKE'),
      model: (this._platform.is('capacitor') ? await Device.getInfo().then(info => info.model) : 'DEV-MODEL-FAKE'),
      operatingSystem: (this._platform.is('capacitor') ? await Device.getInfo().then(info => info.platform) : 'DEV-OS-FAKE'),
      operatingSystemVersion: (this._platform.is('capacitor') ? await Device.getInfo().then(info => info.osVersion) : 'XX.XX'),
      appVersion: (this.appVersion === '0.0.0') ? '1.2.0' : this.appVersion
    }
    var _this = this;
    await this._service.login(input).then(async (data:BasicResponse) => {
        if(!!data && data.status === 'OK')
        {

          if(!credentials && this._platform.is('capacitor'))
          {
            NativeBiometric.setCredentials({
              username: input.username,
              password: input.password,
              server: this.credetialDomain,
            });
            if(!!input.username)
              localStorage.setItem('tmpusn', input.username);
            else
              localStorage.removeItem('tmpusn');
          }

          this.customError = null;
          const user: LoginResponse = data.content as LoginResponse;
          localStorage.setItem("agentName", user.name);
          localStorage.setItem("agentCode", user.agentCode);
          localStorage.setItem("catalogName", user.catalogName);
          localStorage.setItem("infoDataList_agent", JSON.stringify(user.infoDataList));
          this._jwtService.setToken(user.token)
          if (!!user.language && user.language.isocode !== "") {// Se non ne ha trovata nessuna, tiene quella impostata in login
            this._translate.use(user.language.isocode);
          }
          localStorage.setItem('language', this._translate.currentLang);
          let lastUpdateInstant = null;
          if(this._platform.is('capacitor')) {
            const resp = await this._startDb.createDb();
            const username: string = this._jwtService.getUsername();
            this.storeCredentials(username.toLocaleLowerCase(), user.name);
            const conditions = [{key: 'username', value: username}, {key: 'type', value: 'lastUpdate'}];
            localStorage.setItem("isFirstUpdate", "false");
            this._dbService.getRecordsByANDCondition('userSettings', conditions).then((data:UserSetting[]) => {
              if(data === null || data.length === 0){
                localStorage.setItem("isFirstUpdate", "true");
                _this._router.navigate(['/private/syncro']);
              } else {
                const lastUpdateSettings = data.filter(item => item.type === 'lastUpdate');
                if(!!lastUpdateSettings && lastUpdateSettings.length === 0)
                  lastUpdateInstant = data[0].value
              }
            });
          }

          if(!!lastUpdateInstant && this.getHoursDiff(new Date(lastUpdateInstant), new Date()) >= 48) {
            const confirm = await this._alertController.create({
              header: this._translate.instant('LOGIN.SYNCRO_CHECK'),
              cssClass: 'custom-alert',
              buttons: [
                {
                  text: this._translate.instant('LOGIN.SYNCRO_YES'),
                  role: 'confirm',
                  handler: () => {
                    this._router.navigate(['/private/syncro']);
                  },
                },
                {
                  text: this._translate.instant('LOGIN.SYNCRO_NO'),
                  role: 'cancel',
                  handler: () => {
                    _this._router.navigate(['/private/home']);
                  },
                },
              ],
            });

            await confirm.present();
          }else
          {
            _this._router.navigate(['/private/home']);
          }
        } else {
          localStorage.removeItem("agentName");
          this._jwtService.setToken(null);
          if(!!data && !!data.error && !! !!data.error.details &&
            ((data.error.details.toString().toLocaleLowerCase()).indexOf("connection reset") >= 0 ||
            (data.error.details.toString().toLocaleLowerCase()).indexOf("ldap login failed") >= 0 ||
            (data.error.code.toString().toLocaleLowerCase()).indexOf("ServiceUnavailable".toLocaleLowerCase()) >= 0))
          {
            await this.offlineLogin(credentials, loading);
          }
          else if(!!data && !!data.error && data.error.code === "400")
            this.customError = this._translate.instant('LOGIN.LOGIN_FAILED');
          else
            this.customError = this._translate.instant('LOGIN.LOGIN_ERROR');
        }
    }).finally(() => {
      loading.dismiss();
    });
  }

  private async offlineLogin(credentials, loading){
    let _this = this;
    if(this._platform.is('capacitor')) {
      const username: string =  !!credentials ? credentials.username : this.formLoginValue.toLocaleLowerCase();

      this._startDb.createDb();
      const ret: any[] = await this._dbService.getDistinct('userSettings', ['username']);
      const checkList = ret.map( (item:UserSetting)=>{
        return item as unknown as UserSetting
      });
      if(ret !== null && ret.length > 0 && checkList.filter((item: UserSetting) => item.username !== username).length > 0)
      {
        this._jwtService.setToken(null);
        localStorage.removeItem("agentName");
        this.customError = "App in utilizzo da parte di un altro agente. ";
      } else {
        const conditions = [{key: 'username', value: username}];
        const settingsList: UserSetting[] = await this._dbService.getRecordsByANDCondition('userSettings', conditions);
        if(settingsList === null || settingsList.length === 0)  // al primo accesso è obbligatorio essere collegati in rete
        {
          this._jwtService.setToken(null);
          localStorage.removeItem("agentName");
          this.presentNetworkError();
          await loading.dismiss();
          return new Promise((resolve) => { resolve(null); });
        } else {
          const credential = settingsList.filter(item => item.type === 'credential')[0];
          const agentName = settingsList.filter(item => item.type === 'agentName')[0];
          const tmpPsw = !!credentials ? credentials.password : this.formPasswordValue;
          return Utils.decryptPassword(credential.value, this.secureKey, this.secureIV)
          .then(async res => {
            if(res === tmpPsw) {
              _this._jwtService.setToken("offline");
              localStorage.setItem("offline_username", this.formLoginValue.toLowerCase());
              localStorage.setItem("agentName", agentName.value);
              const tomorrow = new Date()
              tomorrow.setDate(new Date().getDate() + 1)
              localStorage.setItem("offline_exp", tomorrow.toString());
              // Verifico se l'accesso è stato fatto da più di 48 ore, se è così, deve farlo online per forza
              const lastUpdateInstant = settingsList.filter(item => item.type === 'lastUpdate')[0].value;
              if(!Utils.isActiveConnection() && !!lastUpdateInstant && this.getHoursDiff(new Date(lastUpdateInstant), new Date()) >= 48) {
                _this.customError = _this._translate.instant('LOGIN.MUST_ONLINE');
              } else if (!Utils.isActiveConnection()){
                _this._router.navigate(['/private/home']);
              } else if (Utils.isActiveConnection() && !!lastUpdateInstant && this.getHoursDiff(new Date(lastUpdateInstant), new Date()) >= 48){
                // Arrivo qui solo se hybris non ha funzionato ma è troppo che l'utente non accede
                _this.customError = _this._translate.instant('LOGIN.LOGIN_FAILED');
              } else if (Utils.isActiveConnection()) {
                // Arrivo qui solo se hybris non ha funzionato e ma il check offline è OK
                _this._router.navigate(['/private/home']);
              } else {
                _this.customError = _this._translate.instant('LOGIN.LOGIN_FAILED');
              }
            } else {
              _this.customError = _this._translate.instant('LOGIN.LOGIN_FAILED');
            }
            await loading.dismiss();
            return new Promise((resolve) => { resolve(null); });
          })
          .catch((error: any) => console.error(error));
        }
      }
    }
  }

  private getHoursDiff(startDate, endDate) {
    const msInHour = 1000 * 60 * 60;

    return Math.round(Math.abs(endDate - startDate) / msInHour);
  }

  // TODO: Rimuovere
  async presentToast() {
    const toast = await this._toastController.create({
      message: 'DA IMPLEMENTARE',
      cssClass: 'toast-custom-class',
      buttons: [
        {
          text: 'OK',
          role: 'cancel'
        }
      ]
    });
    toast.present();
  }

  async presentNetworkError() {
    Utils.showSnack(this._toastController, this._translate.instant('GENERICS.NO_NETWORK_LOGIN'));
  }

  private specificControl() {
    return this.loginFormGroup.valid;
  }

  private async storeCredentials(username: string, agentName: string) {
    const now = new Date().getTime();
    await Utils.encryptPassword(this.formPasswordValue, this.secureKey, this.secureIV)
    .then(res => {
      this._dbService.replaceIntoRecord('userSettings', ['username', 'type', 'value', 'instant'], [username, 'credential', res, now.toString()]);
      this._dbService.replaceIntoRecord('userSettings', ['username', 'type', 'value', 'instant'], [username, 'agentName', agentName, now.toString()]);
    })
    .catch((error: any) => console.error(error));
  }
}
