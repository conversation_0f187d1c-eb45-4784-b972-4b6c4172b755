<div class="right-bar">
  <div class="cell">
    <button (click)="showHideNavigator()" class="visible">
      <object type="image/svg+xml" data="../../assets/svg/burger.svg">
        <img src="../../assets/svg/burger.svg" />
      </object>
    </button>
  </div>
  <div class="cell">
    <button (click)="info()">
      <object type="image/svg+xml"
        [attr.data]="(isInfoShown ? '../../assets/svg/red-info.svg' : '../../assets/svg/info.svg') | safe">
        <img [attr.src]="isInfoShown ? '../../assets/svg/red-info.svg' : '../../assets/svg/info.svg'" #svg />
      </object>
    </button>
  </div>
  <div class="cell">
    <button (click)="prices()">
      <object type="image/svg+xml" data="../../assets/svg/euro.svg">
        <img src="../../assets/svg/euro.svg" />
      </object>
    </button>
  </div>
  @if(!isProspect) {
  <div class="cell">
    <button (click)="favorite($event)">
      @if(!isFavorite) {
      <object type="image/svg+xml" data="../../assets/svg/favorite.svg">
        <img src="../../assets/svg/favorite.svg" />
      </object>
    } @else {
      <object type="image/svg+xml" data="../../assets/svg/golden-favorite.svg">
        <img src="../../assets/svg/golden-favorite.svg" />
      </object>
      }
    </button>
  </div>
  }
  <div class="cell" (click)="share($event)">
    <button>
      <object type="image/svg+xml" data="../../assets/svg/gray-share.svg">
        <img src="../../assets/svg/gray-share.svg" />
      </object>
    </button>
  </div>
</div>