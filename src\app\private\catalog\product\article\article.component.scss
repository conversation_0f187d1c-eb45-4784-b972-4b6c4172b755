.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        height: 90vh;
        max-height: 600px;
        width: 90vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            .close {
                background-color: transparent;
                justify-content: end;
            }
        }
        .row {
            display: flex;
            flex-direction: row;
            height: 100%;
            margin-top: -77px;
            .col {
                width: 50%;
                padding: 3em;
                &:first-child {
                    border-right: 3px solid var(--ion-dat-gray);
                    justify-content: center;
                    display: flex;
                    align-items: center;
                    .product-image {
                        pinch-zoom {
                            background-color: #FFF !important;
                            img {
                                width: 98%;
                                height: 98%;
                            }
                        }
                    }
                }
                &:last-child {
                    display: flex;
                    flex-direction: column;
                    .code {
                        color: var(--ion-dat-red);
                        font-size: 40px;
                        font-weight: bolder;
                        margin-bottom: 10px;
                    }
                    .name {
                        color: var(--ion-dat-black);
                        font-size: 20px;
                        font-weight: bolder;
                        margin-bottom: 10px;
                    }
                    .classification {
                        margin-top: 30px;
                        height: 100%;
                        overflow: scroll;
                        .item {
                            display: flex;
                            flex-direction: row;
                            margin-bottom: 15px;
                            .feature-name {
                                color: var(--ion-dat-dark-gray);
                                padding-right: 3px;
                                &:after {
                                    content: ":";
                                }
                            }
                            .feature-value span {
                                font-weight: bold;
                                &:after {
                                    content: " ";
                                }
                            }
                        }
                    }
                    .buttons {
                        display: flex;
                        justify-content: end;
                        button {
                            background-color: transparent;
                            margin-left: 30px;
                            object {
                                width: 40px;
                                height: 40px;
                                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
                            }
                        }
                    }
                }
            }
        }
    }
}

::ng-deep .swiper-pagination.swiper-pagination-bullets
{
  bottom: 19px !important;
}

swiper-container {
  height: 370px;
}

swiper-slide {
  display: flex;
  flex-direction: column;
  height: 300px;
}

.product-quantity-in-cart, .product-min-quantity 
{
    margin-left: 7px;
    font-size: 15px;
    width: 30px;
    text-align: center;
    font-weight: bold;
    position: absolute;
    margin-top: -6px;
    color: black !important;
}
.product-quantity-in-cart
{
    color: var(--ion-dat-red) !important;
}
svg {
    width: 40px;
    height: 40px;
    &.inCart{
        path, ellipse {
            fill: #008EA7 !important;
        }
    }
}