import { ChangeDetectorRef, Component } from '@angular/core';
import { AlertController, IonicModule, LoadingController, Platform, ToastController } from '@ionic/angular';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, fromEvent, map, merge, Observable, of, Subject } from 'rxjs';
import { pairwise } from 'rxjs';
import { BasicResponse } from 'src/app/service/data/basic-response';
import { Category } from 'src/app/service/data/category';
import { Customer } from 'src/app/service/data/customer';
import { Product } from 'src/app/service/data/product';
import { UserSetting } from 'src/app/service/data/user-settings';
import { JWTTokenService } from 'src/app/service/jwttoken.service';
import { SyncroService } from 'src/app/service/syncro/syncro.service';
import { SyncroV2OrchestratorService, SyncProgress, SyncResult } from 'src/app/service/syncro-v2/syncro-v2-orchestrator.service';
import { SyncroUnifiedService, UnifiedSyncProgress, UnifiedSyncResult } from 'src/app/service/syncro-unified/syncro-unified.service';
import { DbTestService } from 'src/app/shared/db-test.service';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { ProspectType } from 'src/app/service/data/prospect-type';
import Utils from 'src/app/shared/utils';
import { Discount } from 'src/app/service/data/discount';
import Navigations from 'src/app/shared/navigations';
import { select, Store } from '@ngrx/store';
import { setDCProperty } from 'src/app/store/actions/dcproperty.actions';
import { Router } from '@angular/router';
import { setCatalogByProduct } from 'src/app/store/actions/catalog-by-product.actions';
import { CategoryService } from 'src/app/service/category/category.service';
import { setFavorites } from 'src/app/store/actions/favorites.actions';
import { Subcategory } from 'src/app/service/data/subcategory';
import { setSubcategories } from 'src/app/store/actions/subcategories.actions';
//import { BackgroundMode } from '@awesome-cordova-plugins/background-mode/ngx';
import { Favorite } from 'src/app/service/data/favorite';
import { ImageOfflineService } from 'src/app/service/syncro-v2/image-offline.service';
import { setProducts } from 'src/app/store/actions/products.actions';
import { setCategories } from 'src/app/store/actions/categories.actions';
import { Directory } from '@capacitor/filesystem';
import { Filesystem } from '@capacitor/filesystem';
import { App } from '@capacitor/app';
import { Network } from '@capacitor/network';
import { ConnectionStatus } from '@capacitor/network';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
@Component({
    selector: 'app-syncro',
    templateUrl: './syncro.component.html',
    styleUrls: ['./syncro.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class SyncroComponent {
  public appIsOnline$: BehaviorSubject<ConnectionStatus> = new BehaviorSubject<ConnectionStatus>(null);
  checkInternet = false;
  recommendWifi = false;
  lastUpdateDate = '';
  startSyncInstant = null;
  buttonText = this._translate.instant('SYNCRO.START');
  inProgress = false;
  seconds = 0;
  maxValue = 0;
  progress = 0;
  updateIsRun$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  isFinished: boolean = false;
  syncImage = true;
  instantChecked = false;

  constructor(
    private _router: Router, private _platform: Platform,
    private _translate: TranslateService, private _syService: SyncroService, private _categoryService: CategoryService,
    private _toastController: ToastController, private _dbService: HybridDbService, private _alertController: AlertController,
    private _loadingController: LoadingController,
    private _jwtService: JWTTokenService, private cdref: ChangeDetectorRef,
    private _store: Store<any>, private _syncroV2Orchestrator: SyncroV2OrchestratorService,
    private _syncroUnifiedService: SyncroUnifiedService, private _dbTestService: DbTestService,
    private _imageOfflineService: ImageOfflineService) {
      this.updateIsRun$.pipe(pairwise()).subscribe(async ([previous, current]) => {
        if (previous && !current) {
            console.log("ABBIAMO FINITO", this.fmtDate(new Date().getTime()) );
            localStorage.setItem("isFirstUpdate", "false");
            //this._backgroundMode.disable();
            
            this.isFinished = true;
            this.seconds = 1;
            this.progress = 100;
            this.timer();
            const confirm = await this._alertController.create({
              message: this._translate.instant('SYNCRO.FINISHED_BODY'),
              buttons: [
                {
                  text: this._translate.instant('SYNCRO.FINISHED_CLOSE'),
                  role: 'cancel',
                  handler: () => {
                  },
                },
                {
                  text: this._translate.instant('SYNCRO.FINISHED_GO_TO_HOME'),
                  role: 'confirm',
                  handler: () => {
                    this._router.navigate(['/private/home']);
                  },
                }
              ],
            });
            await confirm.present();
        }
      });
  }

  async ngOnInit(){
    this._platform.ready().then(async () => {
      if (this._platform.is('capacitor')) {
        const status = await Network.getStatus();
        this.checkInternet = status.connected;
      } else
      this.checkInternet = true;
    });

    this.checkInternetFunc().subscribe((status: ConnectionStatus) => {
      this.checkInternet = status.connected;

      // checking internet connection
      if (this.checkInternet) {
        // show success alert if internet is working
        if(status.connectionType !== "wifi")
        {
          this.recommendWifi = true;
        }
      }
      else {
        // show danger alert if net internet not working
        this.recommendWifi = false;
      }
      this.cdref.detectChanges();
    });
  }

  async ngAfterViewInit() {
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();
    console.log('ngAfterViewInit startSyncInstant', this.startSyncInstant);
    const username: string = this._jwtService.getUsername();
    const conditions = [{key: 'username', value: username}, {key: 'type', value: 'lastUpdate'}];
    this._dbService.getRecordsByANDCondition('userSettings', conditions).then((data:any[]) => {
      this.instantChecked = true;
      this.lastUpdateDate = this.fmtDate(data[0].value);
      this.startSyncInstant = data[0].value;
      console.log('ngAfterViewInit startSyncInstant', this.startSyncInstant);
      loading.dismiss();
    }).catch(() => {
      this.lastUpdateDate = '';
      this.startSyncInstant = null;
      loading.dismiss();
    });
  }

  ngAfterContentChecked() {
    this.cdref.detectChanges();
  }

  syncroGoComponentGoBack() {
    let routingStack = [];
    this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
    if(!!routingStack)
    {
      const lastRouting = routingStack[routingStack.length - 2];
      let navHistory = [];
      this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
      const lastHistory = navHistory[navHistory.length - 1];
      let currentCustomer = null;
      this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();

      if(lastRouting.component.includes('catalog') && !lastRouting.component.includes('product'))
      {
        this._router.navigate(['/private/catalog'], { queryParams: {
          customerUid: currentCustomer.uid,
          customerName: currentCustomer.name,
          isProspect: lastRouting.component.includes('isProspect=true'),
          showFavorites: false,
          clickedNavarId: !!lastHistory ? lastHistory.item.id : null,
          turnAround: true
        }});
      } else if (lastRouting.component.includes('product')) {
        this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], { queryParams: {
          idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
          current: JSON.stringify(lastHistory.item.datacolCategory.category),
          customerUid: currentCustomer.uid,
          customerName: currentCustomer.name,
          isFavorite: lastHistory.item.isFavorite,
          isProspect: lastRouting.component.includes('isProspect=true'),
        }});
      }
      else if(!!lastRouting.component)
        this._router.navigate([lastRouting.component]);
    }
  }

  /**
   * Ottiene i preferiti correnti dal database locale
   */
  private async getCurrentFavorites(): Promise<Favorite[]> {
    try {
      const favorites = await this._dbService.getAll(['favorites']);
      return favorites.map((item: any) => item as Favorite);
    } catch (error) {
      console.error('Errore nel recupero dei preferiti:', error);
      return [];
    }
  }

  fmtProgress(progress) {
    return progress.toFixed(0);
  }

  startSync() {
    const now = new Date().getTime();
    console.log("### startSync ### inizio:", this.fmtDate(now));
    //this._backgroundMode.setDefaults({ silent: true });
    //this._backgroundMode.enable();
    const timer = 3 * 60;
    let interval = setInterval(()=>{
      //this._backgroundMode.wakeUp();
      if(!this.inProgress)
        clearInterval(interval);
    }, timer);
    this.invokeApi();
    this.inProgress = !this.inProgress;
  }

  private async invokeApi(){
    // Inizializza la progress bar per la sincronizzazione unificata
    this.maxValue = 100; // Usiamo percentuali da 0 a 100
    this.seconds = 100;  // Inizia da 100 e scende a 0
    this.progress = 0;   // Progresso da 0 a 100
    this.updateIsRun$.next(true);

    try{
      const catalog = localStorage.getItem("catalogName");
      if(!catalog)
      {
        Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('SYNCRO.CATALOG_ERROR'));
        return;
      }

      // Test del database prima di iniziare
      console.log('🔍 Verifica stato database...');
      this.updateProgressBar(1, 'Verifica database');
      const dbReport = await this._dbTestService.showDatabaseReport();

      console.log("startSyncInstant", this.startSyncInstant);

      // Se è la prima sincronizzazione, pulisci le tabelle
      if(!this.startSyncInstant)
      {
        this.updateProgressBar(2, 'Pulizia tabelle');
        try {
          await this._syncroUnifiedService.clearTablesForFullSync();
          // Pulisci anche le tabelle non gestite dal servizio unificato
          this._dbService.clearTable('subcategories');
          this._dbService.clearTable('carts');
          this.updateProgressBar(3, 'Tabelle pulite');
        } catch (error) {
          console.warn('⚠️ Errore durante la pulizia delle tabelle, continuando comunque...', error);
          // Mostra un avviso all'utente ma continua
          Utils.showSnackWithColor('toast-warning-class', this._toastController,
            'Avviso: Alcuni dati potrebbero non essere aggiornati correttamente. La sincronizzazione continuerà.');
        }
      }

      const salesOrganization = catalog.length >= 6 ? catalog.substring(2, 6) : null;

      console.log("Catalog name: ", catalog);
      console.log("salesOrganization: ", salesOrganization);

      // Invia i preferiti se non è la prima sincronizzazione
      if(!!this.startSyncInstant)
      {
        this.updateProgressBar(4, 'Invio preferiti');
        try {
          const favorites = await this.getCurrentFavorites();
          if (favorites.length > 0) {
            await this._syncroUnifiedService.sendFavorites(favorites);
            console.log('✅ Preferiti inviati al server');
          }
        } catch (e) {
          console.log('Errore nella sendFavorites: ', e);
          console.log('Proseguo con le altre sync');
        }
      }

      // I clienti vengono ora sincronizzati dal servizio unificato

      // Esegui la sincronizzazione unificata
      console.log('🚀 Avvio sincronizzazione unificata...');

      try {
        const result: UnifiedSyncResult = await this._syncroUnifiedService.syncComplete(
          salesOrganization,
          100, // pageSize
          this.startSyncInstant,
          (progress: UnifiedSyncProgress) => {
            console.log(`📊 ${progress.currentStep}: Categorie: ${progress.categoriesProcessed}, Prodotti: ${progress.productsProcessed}, Prospect: ${progress.prospectsProcessed}, Sconti: ${progress.discountsProcessed}, Preferiti: ${progress.favoritesProcessed}, Clienti: ${progress.customersProcessed}`);

            // Calcola la percentuale di progresso basata sui passi completati
            this.updateSyncProgress(progress);
          }
        );

        if (!result.success) {
          console.error('❌ Sincronizzazione unificata fallita:', result.error);
          Utils.showSnackWithColor('toast-warning-class', this._toastController,
            'Sincronizzazione parzialmente completata. Alcuni dati potrebbero non essere aggiornati.');
        } else {
          console.log(`✅ Sincronizzazione unificata completata in ${result.duration}ms`);
          console.log(`📊 Totali: Categorie: ${result.totalCategoriesProcessed}, Prodotti: ${result.totalProductsProcessed}, Prospect: ${result.totalProspectsProcessed}, Sconti: ${result.totalDiscountsProcessed}, Preferiti: ${result.totalFavoritesProcessed}, Clienti: ${result.totalCustomersProcessed}`);
        }
      } catch (syncError) {
        console.error('❌ Errore durante la sincronizzazione unificata:', syncError);

        // Se l'errore è relativo alla versione del database, mostra un messaggio specifico
        if (syncError.message && syncError.message.includes('Database version error')) {
          Utils.showSnackWithColor('toast-warning-class', this._toastController,
            'Problema con il database locale. Ricarica la pagina e riprova la sincronizzazione.');
        } else {
          Utils.showSnackWithColor('toast-warning-class', this._toastController,
            'Errore durante la sincronizzazione. Alcuni dati potrebbero non essere aggiornati.');
        }
        // Continua comunque con le operazioni post-sincronizzazione
      }

      // Aggiorna progresso per le operazioni post-sincronizzazione
      this.updateProgressBar(95, 'Elaborazione dati');

      // Continua con le operazioni post-sincronizzazione esistenti
      let tmpCategories : Category[] = [];
      await this.getAllCategory().then((items) => tmpCategories = items);
      let navigations = new Navigations(this._store);
      navigations.setNavigationTree(tmpCategories);
      navigations = undefined;
      const categories = tmpCategories
        .filter((item:Category) => item.id !== null && item.id !== undefined && item.id !== '')
        .map((item:Category) => item.id);

      console.log(`📊 Sync Images: ${categories.length} categorie valide su ${tmpCategories.length} totali`);
      if (categories.length !== tmpCategories.length) {
        const invalidCategories = tmpCategories.filter((item:Category) => !item.id || item.id === '');
        console.warn('⚠️ Categorie con ID non valido:', invalidCategories);
      }
      tmpCategories = undefined;

      // Sincronizza le subcategorie (manteniamo il metodo esistente)
      await this.syncSubcategory(categories);

      // Preparo la vista piatta
      this.setCatalogByProduct();

      // Dopo lo scarico prodotti, devo settare le categorie focus per velocizzare il caricamento a schermo
      this.setSubcategories();
      this.setProducts();
      this.setFocusAndDivisionStatusCode();

      // Sincronizza le immagini (manteniamo il metodo esistente)
      try {
        await this.syncImages(catalog, categories);
      } catch (e) {
        console.log('Errore nella syncImages: ', e);
        console.log('Proseguo con le altre sync');
      }

      console.log("Finito tutto quanto")

      // Aggiorna progresso al 100%
      this.updateProgressBar(100, 'Sincronizzazione completata');

      const now = new Date().getTime();
      const username: string = this._jwtService.getUsername();
      this._dbService.replaceIntoRecord('userSettings', ['username', 'type', 'value', 'instant'], [username, 'lastUpdate', now.toString(), now.toString()]);
      this.lastUpdateDate = this.fmtDate(now);

      // Al termine, posso spegnere
      this.updateIsRun$.next(false);
    }catch(error) {
      console.log("Errore in sync, processo terminato.", error);
      Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('SYNCRO.ERROR'));
      // Al termine, posso spegnere
      this.updateIsRun$.next(false);
    }
  }

  private async syncCustomers(){
    let _this = this;

    // 2024-09-17: occorre introdurre un vincolo nella tabella customer di unicità (uid)
    //  dev'essere retrocompatibile, pertanto verifichiamo se già esiste, se non esiste va svuotata la tabella, creato l'indice 
    //  e riscaricata la tabella --->
    let forceDownload:boolean = false;
    try {
      await this._dbService.createUniqueIndex('customers', ['uid']);
    } catch(error) {
      await this._dbService.clearTable('customers');
      await this._dbService.createUniqueIndex('customers', ['uid']);
      forceDownload = true;
    }
    // <---

    await _this._syService.allcustomersbyuser(forceDownload ? null : this.startSyncInstant).then(async (data:BasicResponse)=> {
      console.log("allcustomersbyuser", data);
      if(!!data && data.status === 'KO') {
        console.error(data.error.details)
      } else {
        if(!!data.content && typeof data.content === 'object' && data.content.length > 0)
        {
          let columns: string[] = [];
          let records: {andConditionList: {key: string, value: any}[], values: any[]}[] = [];
          data.content.forEach(async (customer: Customer) => {
            columns = this.prepareColumns(columns, customer);
            let values: string[] = [];
            values = this.prepareValues(values,customer);
            if(customer.cancellationInstant === null)
              records.push({andConditionList: [{key: 'uid', value: customer.uid}], values: values});
            else
            {
              await _this._dbService.deleteRecord('customers', [{key: 'uid', value: `'${customer.uid}'`}]).then(() => {
                console.log("customer cancellato", customer.uid);
              }).catch(err => {
                console.error(`Error delete customer [${customer}]: ${JSON.stringify(err)}`);
              });
            }
          });
          if(!!columns && !!records)
            await _this.saveData('customers', records, columns, true).then(() =>  {
              console.log("allcustomersbyuser ---> finished");
            });
          columns = undefined;
          records = undefined;
        }  else {
          _this.timer(400);
        }
      }
      data = undefined;
    });
    _this = undefined;
  }

  // METODO OBSOLETO - Ora gestito da SyncroUnifiedService
  /*
  private async syncProspectTypes(){
    let _this = this;
    await _this._syService.allprospectsbyuser(_this.startSyncInstant).then(async (data:BasicResponse) => {
      console.log("syncProspectTypes", data);
      if(!!data && data.status === 'KO') {
        console.error(data.error.details)
      } else {
        if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
        {
          let columns: string[] = [];
          let records: {andConditionList: {key: string, value: any}[], values: any[]}[] = [];
          data.content.forEach(async (prospectType: ProspectType) => {
            columns = this.prepareColumns(columns, prospectType);
            let values: string[] = [];
            Object.entries(prospectType).forEach(([key, value]) => {
              values.push(Utils.objToJson(value === null ? '' : value));
            });
            if(prospectType.cancellationInstant === null)
              records.push({andConditionList: [{key: 'uid', value: prospectType.uid}], values: values});
            else
            {
              await _this._dbService.deleteRecord('prospectTypes', [{key: 'uid', value: `'${prospectType.uid}'`}]).then(() => {
                console.log("prospectTypes cancellato", prospectType.uid);
              }).catch(err => {
                console.error(`Error delete prospectTypes [${prospectType}]: ${JSON.stringify(err)}`);
              });
            }
          });
          if(!!columns && !!records)
            await _this.saveData('prospectTypes', records, columns, true).then(() =>  {
              console.log("prospectTytpes ---> finished");
            });
          columns = undefined;
          records = undefined;
        } else {
          _this.timer(50);
        }
      }
      data = undefined;
    });
    _this = undefined;
  }
  */

  // METODO OBSOLETO - Ora gestito da SyncroUnifiedService
  /*
  private async syncDiscounts(salesOrganization) {
    let _this = this;
    await _this._syService.discountlist(salesOrganization, this.startSyncInstant).then(async (data:BasicResponse)=> {
      console.log("discountlist", data);
      if(!!data && data.status === 'KO') {
        console.error(data.error.details)
      } else {
        if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
        {
          await _this._dbService.clearTable('discounts').then(() => {
            console.log("discounts cleared table");
          }).catch(err => {
            console.error(`Error discounts cleared table: ${JSON.stringify(err)}`);
          });

          let columns: string[] = [];
          let records: {andConditionList: {key: string, value: any}[], values: any[]}[] = [];
          data.content.forEach((discount: Discount) => {
            columns = this.prepareColumns(columns, discount);
            let values: string[] = [];
            values = this.prepareValues(values,discount);
            const keys = [{key: 'salesOrganization', value: discount.salesOrganization},
                          {key: 'commissionGroup', value: discount.commissionGroup},
                          {key: 'discount', value: discount.discount}];
            records.push({andConditionList: keys, values: values});
          });
          if(!!columns && !!records)
            await _this.saveData('discounts', records, columns, true).then(() =>  {
              console.log("discountlist ---> finished");
            });
          columns = undefined;
          records = undefined;
        } else {
          _this.timer(300);
        }
      }
      data = undefined;
    });
    _this = undefined;
  }
  */

  // METODO OBSOLETO - Ora gestito da SyncroUnifiedService
  /*
  private async syncCategories() {
    console.log("🔄 Inizio sincronizzazione categorie con servizio v2...");

    try {
      // Inizializza il database v2 se necessario
      await this._syncroV2Orchestrator.initializeDatabase();

      // Ottieni l'ID del catalogo - per ora uso 1 come default
      // TODO: derivare l'ID del catalogo dal catalogName nel localStorage
      const catalogId = 1;
      const pageSize = 10;

      // Esegui la sincronizzazione v2 delle categorie
      const result: SyncResult = await this._syncroV2Orchestrator.syncCatalogCategories(
        catalogId,
        pageSize,
        (progress: SyncProgress) => {
          console.log(`📊 Progresso sincronizzazione: Pagina ${progress.currentPage}, Categorie processate: ${progress.categoriesProcessed}`);
          // Aggiorna il timer con il progresso
          if (progress.categoriesProcessed > 0) {
            this.timer(progress.categoriesProcessed);
          }
        }
      );

      if (result.success) {
        console.log(`✅ Sincronizzazione categorie v2 completata: ${result.totalCategoriesProcessed} categorie in ${result.duration}ms`);
      } else {
        console.error(`❌ Sincronizzazione categorie v2 fallita: ${result.error}`);
        throw new Error(result.error);
      }

    } catch (error) {
      console.error('❌ Errore durante la sincronizzazione categorie v2:', error);
      // Fallback al vecchio metodo in caso di errore
      console.log('🔄 Fallback al vecchio metodo di sincronizzazione...');
      await this.syncCategoriesOld();
    }
  }
  */

  private async syncCategoriesOld() {
    let _this = this;
    await _this._syService.getsynccategories(this.startSyncInstant).then(async (data:BasicResponse) => {
      console.log("getsynccategories", data);
      if(!!data && data.status === 'KO') {
        console.error(data.error.details)
      } else {
        if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
        {
          let columns: string[] = [];
          await data.content.reduce(async (promise, element) => {
            await promise;
            // Devo rimappare l'elemento, esclido le variabili che non servono
            const category = _this.remapObject(new Category(), element);
            // Eventuali root category che non hanno sottocategorie vanno ignorate
            if(!!category.subcategories && Utils.objToJson(category.subcategories).length > 0)
            {
              columns = this.prepareColumns(columns, category);
              let values: string[] = [];
              values = this.prepareValues(values,category, !!this.startSyncInstant); // in insert serve l'id

              const keys = [{key: 'id', value: category.id}]
              const records = {andConditionList: keys, values: values};
              // Poichè i record sono grossi (contengono le subcategories) conviene inserirli uno a uno
              if(category.cancellationInstant === null)
                await _this.saveData('categories', [records], columns, false).then(() =>  {
                  console.log("getsynccategories ---> finished");
                });
              else {
                await _this._dbService.deleteRecord('categories', keys).then(() => {
                  console.log("root category cancellata", keys);
                }).catch(err => {
                  console.error(`Error delete category [${keys}]: ${JSON.stringify(err)}`);
                });
              }
            } else {
              await this._dbService.deleteRecord('categories', [{key: 'id', value: category.id}]).then(() => {
                console.log("root category cancellata", category.id);
              }).catch(err => {
                console.error(`Error delete category id [${category.id}]: ${JSON.stringify(err)}`);
              });
            }
          }, Promise.resolve());
        } else {
          _this.timer(15)
        }
      }
      data = undefined;
    });
    _this = undefined;
  }

  private async syncSubcategory(categories) {
    let _this = this;
    this._store.dispatch(setSubcategories({items: []})); // resetto lo storage
    if(!!categories && categories.length > 0)
    {
      // 2024-07-02: occorre introdurre un vincolo nella tabella subcategories di unicità (idRootCategory, idsubcategory)
      //  dev'essere retrocompatibile, pertanto verifichiamo se già esiste, se non esiste va svuotata la tabella, creato l'indice 
      //  e riscaricata la tabella --->
      let forceDownload:boolean = false;
      try {
        await this._dbService.createUniqueIndex('subcategories', ['idrootcategory','idsubcategory']);
      } catch(error) {
        await this._dbService.clearTable('subcategories');
        await this._dbService.createUniqueIndex('subcategories', ['idrootcategory','idsubcategory']);
        forceDownload = true;
      }
      // <---

      for(let c = 0; c < categories.length; c++)
      {
        const rootCategory = categories[c];
        if (rootCategory)
        {
          await _this._syService.subcategorybycategory(rootCategory, forceDownload ? null : _this.startSyncInstant).then(async (data:BasicResponse) => {
            console.log("subcategorybycategory", data);
            if(!data)
            {
              return new Promise((resolve) => { resolve(null); });
            } else if(data.status === 'KO') {
              console.error(data.error.details)
              return new Promise((resolve) => { resolve(null); });
            } else {
              if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
              {
                let columns: string[] = [];
                let records: {andConditionList: {key: string, value: any}[], values: any[]}[] = [];

                for(let i = 0; i<data.content.length; i++) {
                  const element: Subcategory = data.content[i];
                  // Devo rimappare l'elemento, ottengo letiabili che voglio escludere
                  let subcategory:Subcategory = _this.remapObject(new Subcategory(), element);
                  columns = _this.prepareColumns(columns, subcategory);
                  let values: string[] = [];
                  values = _this.prepareValues(values,subcategory);

                  const keys = [{key: 'idsubcategory', value: subcategory.idsubcategory}, {key: 'idrootcategory', value: subcategory.idrootcategory}];
                  if(subcategory.cancellationinstant === null)
                    records.push({andConditionList: keys, values: values});
                  else {
                    await _this._dbService.deleteRecord('subcategories', keys).then(() => {
                      console.log("subcategory cancellata", keys);
                    }).catch(err => {
                      console.error(`Error [${subcategory}]: ${JSON.stringify(err)}`);
                    });
                  }
                }
                if(!!columns && !!records)
                  await _this.saveData('subcategories', records, columns, true).then(() =>  {
                    console.log("subcategorybycategory ---> finished");
                  });
              } else {
                _this.timer(1000)
                return new Promise((resolve) => { resolve(null); });
              }
            }

            data = undefined;
          }).catch((errore) => {
            console.error("qualcosa è successo", errore);
          });
        }
      }
    }

    _this = undefined;
  }

  // METODO OBSOLETO - Ora gestito da SyncroUnifiedService
  /*
  private async syncProducts(categories) {
    let _this = this;
    this._store.dispatch(setProducts({items: []})); // resetto lo storage
    if(!!categories && categories.length > 0)
    {

      // 2024-07-10: occorre introdurre la colonna root category  --->
      let forceDownload:boolean = false;
      try {
        await this._dbService.addColumn('products', 'idCategory', 'TEXT');
        await this._dbService.clearTable('products');
        forceDownload = true;
      } catch(error) {

      }
      try {
        // 2024-08-27: occorre introdurre la colonna divisionStatusCode  --->
        await this._dbService.addColumn('products', 'divisionStatusCode', 'TEXT');
        await this._dbService.clearTable('products');
        forceDownload = true;
      } catch(error) {

      }

      try {
        // 2024-08-27: occorre introdurre la colonna divisionStatusCode  --->
        await this._dbService.addColumn('datacolCategories', 'divisionStatusCode', 'TEXT');
        // la clear table la fa poi nel metodo di valorizzazione
      } catch(error) {

      }

      for(let c = 0; c < categories.length; c++)
      {
        const rootCategory = categories[c];
        if (rootCategory)
        {
          await _this._syService.productsbycategory(rootCategory, forceDownload ? null :_this.startSyncInstant).then(async (data:BasicResponse) => {
            console.log("productsbycategory --->", rootCategory);
            console.log("productsbycategory", data.content.length);
            if(!data)
            {
              return new Promise((resolve) => { resolve(null); });
            } else if(data.status === 'KO') {
              console.error(data.error.details)
              return new Promise((resolve) => { resolve(null); });
            } else {
              if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
              {
                let columns: string[] = [];
                let records: {andConditionList: {key: string, value: any}[], values: any[]}[] = [];

                // Troviamo il punto medio dell'array
                const metà = Math.ceil(data.content.length / 2);
                
                // Dividiamo l'array in due
                const firstSubarray = data.content.slice(0, metà);
                const secondSubarray = data.content.slice(metà);
                
                // Cicliamo la prima metà
                for (const element of firstSubarray) {
                  // const element: Product = data.content[i];
                  let product:Product = _this.remapObject(new Product(), element);
                  product.idCategory = rootCategory.toString();
                  // product.code = product.code.replace(/^0+/, ''); // tolgo la sfilza di zeri iniziali
                  columns = _this.prepareColumns(columns, product);
                  let values: string[] = [];
                  values = _this.prepareValues(values,product);
                  const keys = [{key: 'code', value: product.code}, {key: 'idCategory', value: product.idCategory},{key: 'idSubCategory', value: product.idSubCategory}];
                  if(product.cancellationInstant === null)
                    records.push({andConditionList: keys, values: values});
                  else {
                    const keyToDelete = [{key: 'code', value: `'${product.code}'`}, {key: 'idCategory', value: `'${product.idCategory}'` }, {key: 'idSubCategory', value: `'${product.idSubCategory}'` }];
                    await _this._dbService.deleteRecord('products', keyToDelete).then(() => {
                      console.log("product cancellato", keyToDelete);
                    }).catch(err => {
                      console.error(`Error [${product}]: ${JSON.stringify(err)}`);
                    });
                  }
                }

                if(!!columns && !!records)
                  await _this.saveData('products', records, columns, true).then(() =>  {
                    console.log("productsbycategory ---> first subarray finished");
                  });

                columns = [];
                records = [];
                
                // Cicliamo la seconda metà
                for (const element of secondSubarray) {
                  // const element: Product = data.content[i];
                  let product:Product = _this.remapObject(new Product(), element);
                  product.idCategory = rootCategory.toString();
                  // product.code = product.code.replace(/^0+/, ''); // tolgo la sfilza di zeri iniziali
                  columns = _this.prepareColumns(columns, product);
                  let values: string[] = [];
                  values = _this.prepareValues(values,product);
                  const keys = [{key: 'code', value: product.code}, {key: 'idCategory', value: product.idCategory},{key: 'idSubCategory', value: product.idSubCategory}];
                  if(product.cancellationInstant === null)
                    records.push({andConditionList: keys, values: values});
                  else {
                    const keyToDelete = [{key: 'code', value: `'${product.code}'`}, {key: 'idCategory', value: `'${product.idCategory}'` }, {key: 'idSubCategory', value: `'${product.idSubCategory}'` }];
                    await _this._dbService.deleteRecord('products', keyToDelete).then(() => {
                      console.log("product cancellato", keyToDelete);
                    }).catch(err => {
                      console.error(`Error [${product}]: ${JSON.stringify(err)}`);
                    });
                  }
                }

                if(!!columns && !!records)
                  await _this.saveData('products', records, columns, true).then(() =>  {
                    console.log("productsbycategory ---> second subarray finished");
                  });
                
                return new Promise((resolve) => { resolve(null); });
              } else {
                _this.timer(1000)
                return new Promise((resolve) => { resolve(null); });
              }
            }

            data = undefined;
          }).catch((errore) => {
            console.error("qualcosa è successo", errore);
          });
        }
      }
    }

    _this = undefined;
  }

  // METODI OBSOLETI - Ora gestiti da SyncroUnifiedService
  /*
  private async getFavorites() {
    var _this = this;
    try{
      await _this._syService.getProducts().then(async (data) => {
        if(!!data.content &&  typeof data.content === 'object' && data.content.length > 0)
        {
          // i miei precedenti li ho appena inviati, quindi cancello i locali e li risincronizzo
          await _this._dbService.clearTable('favorites').then(() => {
            console.log("favorites cleared table");
          }).catch(err => {
            console.error(`Error favorites cleared table: ${JSON.stringify(err)}`);
          });
          data.content.forEach(async (favorite: Favorite) => {
            console.log("Favorite: ", favorite);
            return await this._dbService.addRecords('favorites', ['idCategory', 'customerUid', 'catalogCard'], [[favorite.idCategory, favorite.customerUid, favorite.catalogCard]]);
          });
          console.log("Scritti tutti i favorites");
        }
        console.log("SetFavorites");
        await this.setFavorites(data.content);
      });
    } catch {}

  }

  private async sendFavorites() {
    var _this = this;
    await this._dbService.getAll(["favorites"], ['*']).then(async (data:Favorite[]) => {
      if(!!data && data.length > 0)
      {
        try{
          await _this._syService.saveProducts(data);
        } catch {}
      }
    });
    return new Promise((resolve) => { resolve(null); });
  }
  */

  private async setCatalogByProduct() {
    // Legge tutte le categorie dal database (incluse quelle sincronizzate con v2)
    await this._dbService.getAll(["categories"], ['*']).then(async (data) => {
      console.log(`📊 Caricate ${data?.length || 0} categorie dal database`);

      // Filtra le categorie per mostrare preferibilmente quelle sincronizzate con v2
      let filteredData = data;
      if (data && data.length > 0) {
        const v2Categories = data.filter(cat => cat.syncedWithV2 === 'true');
        if (v2Categories.length > 0) {
          console.log(`✅ Utilizzando ${v2Categories.length} categorie sincronizzate con v2`);
          filteredData = v2Categories;
        } else {
          console.log(`⚠️ Nessuna categoria v2 trovata, utilizzando ${data.length} categorie del vecchio formato`);
        }
      }

      this._store.dispatch(setCategories({ items: filteredData}))
      if(!!filteredData)
      {
        let items: Array<{rootId: string, items: Category[]}> = [];
        for (let i = 0; i < filteredData.length; i++) {
          const element = filteredData[i];
          const previewsItem = await this._categoryService.catalogByProduct(Utils.objToJson(element.subcategories));
          items.push({rootId: element.id, items: previewsItem});
        }
        if(!!items && items.length > 0)
          this._store.dispatch(setCatalogByProduct({items}));
        console.log("setCatalogByProduct ---> finished");
      }
    });
  }

  private setFocusAndDivisionStatusCode() {
    try {
      this._dbService.clearTable('datacolCategories'); // svuoto la tabella
      const conditions = [{key: 'focus', value: 'S'}, {key: 'divisionStatusCode', value: 'Z3'}];
      localStorage.setItem("isFirstUpdate", "false");
      this._store.dispatch(setDCProperty({items: []})); // resetto lo storage
      this._dbService.getColumnFromTableByORCondition('products', ['idSubCategory', 'focus', 'divisionStatusCode'], conditions).then(data => {
        let items: Array<{idSubCategory: string, focus: string, divisionStatusCode: string}> = [];
        data.forEach(async (product:{idSubCategory:string, focus: string, divisionStatusCode: string}) => {
          items.push({idSubCategory: product.idSubCategory, focus: product.focus, divisionStatusCode: product.divisionStatusCode});
          console.log("product", product);
          await this._dbService.addRecords('datacolCategories', ['id', 'focus', 'divisionStatusCode'], [[product.idSubCategory, product.focus, product.divisionStatusCode]]);
        });
        this._store.dispatch(setDCProperty({items}));
        console.log("setCatalogByProduct ---> finished");
      })
    } catch(error) {
      console.error(error);
    }
  }

  private setSubcategories() {
    try {
      this._dbService.getAll(['subcategories'], ['*'], []).then((data:Subcategory[]) => {
        this._store.dispatch(setSubcategories({items: data}));
        console.log("setSubcategories ---> finished");
      })
    } catch(error) {
      console.error(error);
    }
  }

  private setProducts() {
    try {
      this._dbService.getAll(['products'], ['idSubCategory', 'code', 'image', 'name', 'minimumDeliveryQuantity']).then(async (data: Product[]) => {
        const items = await Promise.all((data as Product[]).map(async (item:Product) => {
          const imageDirectory: string = localStorage.getItem("imageDirectory");
          const resolvedImage = await Utils.resolveImageUrlWithOffline(
            this._imageOfflineService,
            this._platform,
            imageDirectory,
            item.image
          );
          return { idSubCategory: item.idSubCategory ,code: item.code, image: resolvedImage, name: item.name, minimumDeliveryQuantity: item.minimumDeliveryQuantity };
        }));

        const distinctItems = items.filter((value, index, self) =>
          index === self.findIndex((t) => (
              t.code === value.code
          ))
        );

        this._store.dispatch(setProducts({ items: distinctItems }));

        console.log("setProducts ---> finished");
      });
    } catch(error) {
      console.error(error);
    }
  }

  private setFavorites(data: Favorite[]) {
    this._store.dispatch(setFavorites({items: []}));
    this._store.dispatch(setFavorites({items: data}));
  }

  private async syncImages(catalog, categories) {
    let _this = this;
    if(this.syncImage)
      {
        console.log("Avvio la sync delle immagini");
        if(!this.startSyncInstant)
        {
          console.log("--> FULL")
          _this = this;
          console.log("Categorie", categories);
          for(let i = 0; i<categories.length; i++){
            const category = categories[i];
            console.log("getFullCatalogImage["+category+"]");
            const resut = await _this._syService.getCatalogImageByCategory(catalog, category, null);
            console.log("finita", category);
            _this.timer(2000);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
        else {
          console.log("--> INCREMENTALE");
          _this = this;
          console.log("Categorie", categories);
          for(let i = 0; i<categories.length; i++){
            const category = categories[i];
            console.log("getFullCatalogImage["+category+"]");
            const resut = await _this._syService.getCatalogImageByCategory(catalog, category, this.startSyncInstant);
            console.log("finita", category);
            _this.timer(2000);
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
          console.log("Cancello immagini obsolete...");
          // TODO: 
          const resut = await _this._syService.getCancelledImages(this.startSyncInstant).then(async data => {
            console.log("getCancelledImages", data);
            if(!!data && data.status === 'KO') {
              console.log(data.error.details)
            } else {
              if(!!data.content && typeof data.content === 'object' && data.content.length > 0)
              {
                for(let i = 0; i<data.content.length; i++){
                  const element = data.content[i];
                  if(!!element['name'] && element['name'].length>0) {
                    const fileName = element['name'].replace('.jpg', '.webp');
                    try {
                        await Filesystem.deleteFile({path: fileName, directory: Directory.Data});
                        console.log("File rimosso", fileName);
                    } catch(error) {
                      console.error(error);
                    }
                  }
                }
                console.log("Terminata rimozione file.");
              }
            }
          });
        }
      }
  }

  private prepareColumns(columns: string[], obj: any){
    if(columns.length === 0)
    {
      columns = Object.keys(obj);
    };
    return columns;
  }

  private prepareValues(values: any[], obj: any, removeId: boolean = false){
    Object.entries(obj).forEach(([key, value]) => {
      if(!(removeId && key === 'id'))
      {
        if(typeof value !== 'object')
        {
          values.push(value);
        } else {
          values.push(JSON.stringify(value));
        }
      }
    });
    return values;
  }

  private remapObject(newObj, oldObj){
    const productKeys=Object.keys(newObj);
    productKeys.forEach(key=>{
      newObj[key]=oldObj[key];
    });
    return newObj;
  }

  private async saveData(tableName:string, records, columns, withId: boolean) {
    let _this = this;
    const simpleRecords = !this.startSyncInstant ? records.map((record) => record.values ) : records;
    const splitted = this.splitRecords(simpleRecords, 25); //FIXME: era 50
    return new Promise<void>(async resolve => {
      if(this.startSyncInstant && withId)
        columns = ['id'].concat(columns); // in inserimento non serve
      await splitted.reduce(async (promise, recordsGroup) => {
        // Questa riga attenderà il completamento dell'ultima funzione asincrona.
        // La prima iterazione utilizza una Promessa già risolta
        // quindi, continuerà immediatamente.
        await promise;
        if(!this.startSyncInstant || tableName === 'favorites')
        {
          await _this._dbService.addRecords(tableName, columns, recordsGroup).then(() => {
            if(!!recordsGroup)
            {
              console.log(`Inserted [${tableName}]: ${recordsGroup.length}`);
              _this.timer(recordsGroup.length);
            }
          }).catch(err => {
            console.error(`Error [${tableName}]: ${JSON.stringify(err)}`);
          });
        } else {
          await _this._dbService.multipleInsertOrReplaceByTransaction(tableName, tableName !== 'categories', columns, recordsGroup).then(() => {
            if(!!recordsGroup) {
              console.log(`Updated [${tableName}]: ${recordsGroup.length}`);
              _this.timer(recordsGroup.length);
            }
          }).catch(err => {
            console.error(`Error [${tableName}]: ${JSON.stringify(err)}`);
          });
        }
    }, Promise.resolve());
      resolve();
    });
  }

  private splitRecords(records, size = 100){
    const chunk = (arr:any, size:any) => arr.reduce((acc:any, _:any, i:any) => (i % size) ? acc : [...acc, arr.slice(i, i + size)], [])
    return chunk(records, size);
  }

  private getAllCategory(){
    let _this = this;
    return new Promise<Category[] >(async resolve => {
      const allCategories = await _this._dbService.getAll(["categories"], ['*']).then((data) => {
        return data;
      });

      // Filtra per preferire le categorie sincronizzate con v2
      let categories: Category[] = allCategories;
      if (allCategories && allCategories.length > 0) {
        const v2Categories = allCategories.filter(cat => cat.syncedWithV2 === 'true');
        if (v2Categories.length > 0) {
          console.log(`📊 getAllCategory: utilizzando ${v2Categories.length} categorie v2 su ${allCategories.length} totali`);
          categories = v2Categories;
        } else {
          console.log(`📊 getAllCategory: utilizzando ${allCategories.length} categorie del vecchio formato`);
        }
      }

      resolve(categories);
    });
  }

  private timer(howMany: number = 1) {
    this.seconds = this.seconds - howMany;
    const percent = (100 * (this.maxValue - this.seconds)) / this.maxValue;
    if(percent < 100)
      this.progress = percent;
    if (this.seconds == 0) {
      this.buttonText = this._translate.instant('SYNCRO.START');
      this.inProgress = !this.inProgress;
    }
    this.cdref.detectChanges();
  }

  /**
   * Aggiorna la progress bar con una percentuale specifica
   * @param percentage Percentuale di completamento (0-100)
   * @param stepName Nome del passo corrente (opzionale)
   */
  private updateProgressBar(percentage: number, stepName?: string) {
    // Assicurati che la percentuale sia tra 0 e 100
    percentage = Math.max(0, Math.min(100, percentage));

    this.progress = percentage;
    this.seconds = 100 - percentage; // Inverti per compatibilità con timer esistente

    if (stepName) {
      console.log(`📊 ${stepName}: ${percentage.toFixed(1)}%`);
    }

    // Forza l'aggiornamento della UI
    this.cdref.detectChanges();
  }

  /**
   * Calcola e aggiorna il progresso della sincronizzazione unificata
   * @param progress Oggetto di progresso dalla sincronizzazione unificata
   */
  private updateSyncProgress(progress: UnifiedSyncProgress) {
    let percentage = 0;

    // Definiamo i pesi per ogni fase della sincronizzazione
    const phaseWeights = {
      'Recupero informazioni catalogo': { weight: 5, base: 0 },
      'Sincronizzazione categorie': { weight: 30, base: 5 },
      'Sincronizzazione prodotti': { weight: 40, base: 35 },
      'Sincronizzazione prospect types': { weight: 10, base: 75 },
      'Sincronizzazione sconti': { weight: 10, base: 85 },
      'Sincronizzazione preferiti': { weight: 5, base: 95 },
      'Sincronizzazione completata': { weight: 0, base: 100 }
    };

    const currentPhase = phaseWeights[progress.currentStep];

    if (currentPhase) {
      percentage = currentPhase.base;

      // Aggiungi progresso interno alla fase corrente
      if (progress.currentStep === 'Sincronizzazione categorie' && progress.totalPages && progress.currentPage !== undefined) {
        // Per le categorie, usa il progresso delle pagine
        const pageProgress = ((progress.currentPage + 1) / progress.totalPages) * currentPhase.weight;
        percentage += pageProgress;
      } else if (progress.currentStep === 'Sincronizzazione prodotti' && progress.productsProcessed > 0) {
        // Per i prodotti, stima il progresso (difficile sapere il totale in anticipo)
        const estimatedProgress = Math.min(currentPhase.weight * 0.8, (progress.productsProcessed / 100) * currentPhase.weight);
        percentage += estimatedProgress;
      } else if (progress.currentStep === 'Sincronizzazione prospect types' && progress.prospectsProcessed > 0) {
        // Per i prospect, assume completamento rapido
        percentage += currentPhase.weight * 0.8;
      } else if (progress.currentStep === 'Sincronizzazione sconti' && progress.discountsProcessed > 0) {
        // Per gli sconti, assume completamento rapido
        percentage += currentPhase.weight * 0.8;
      } else if (progress.currentStep === 'Sincronizzazione preferiti' && progress.favoritesProcessed > 0) {
        // Per i preferiti, assume completamento rapido
        percentage += currentPhase.weight * 0.8;
      }
    }

    // Aggiorna la progress bar
    this.updateProgressBar(percentage, progress.currentStep);
  }

  private fmtDate(instant) {
    if (instant && instant !== '') {
      let date: Date = new Date(parseInt(instant)); // 09/11/2016 16:16pm (GMT)
      return date.toLocaleDateString(this._translate.currentLang.substring(0, 2), {
        // weekday: "short",
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: '2-digit',
        minute: '2-digit',
        hour12: (this._translate.currentLang == 'en-GB')
      });
    }
    return "";
  }

  checkInternetFunc() {
    if (!window || !navigator || !('onLine' in navigator)) return;

    this.appIsOnline$ = Observable.create(observer => {
        observer.next(true);
    }).pipe(map(() => true));

    if (this._platform.is('capacitor')) {
        // on Device - when platform is cordova
       Network.getStatus().then((status) => {
        this.appIsOnline$.next(status);
       });
    } /* else {
        // on Browser - when platform is Browser
        merge(
            of(navigator.onLine),
            fromEvent(window, 'online').pipe(mapTo(true)),
            fromEvent(window, 'offline').pipe(mapTo(false))
        ).subscribe((status: boolean) => this.appIsOnline$.next(status));
    } */

    return this.appIsOnline$;
  }

}
