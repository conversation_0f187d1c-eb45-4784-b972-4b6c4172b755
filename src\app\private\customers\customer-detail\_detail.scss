.container {
    overflow: scroll;
    max-height: calc(100vh - 310px);
    .row {
        display: flex;
        margin: 5px;
        .column {
            flex: 1; 
            min-height: 2.3em;
            height: fit-content;
            &.label {
                max-width: 150px;
                font-size: 14px;
                padding-right: 15px;
                text-align: right;
                padding-top: 3px;
            }
            &.title {
                font-weight: bold;
                text-transform: capitalize;
            }
        }
        .circle {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            background-color: var(--ion-dat-success);
            margin-top: 4px;
            &.status-A {
                background-color: var(--ion-dat-red);
            }
            &.status-S {
                background-color: var(--ion-dat-orange);
            }
        }
        .status-descr {
            position: relative;
            top: -59px;
            float: right;
            font-size: small;
            font-weight: bold;
            &.status-A {
                color: var(--ion-dat-red);
            }
            &.status-S {
                color: var(--ion-dat-orange);
            }
        }
        .value {
            padding: 5px;
            background: var(--ion-dat-gray);
            border-radius: 5px;
            min-height: 2em;
            height: fit-content;
        }
    }
}