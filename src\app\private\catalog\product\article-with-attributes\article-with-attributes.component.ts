import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Classification, Feature, Product } from 'src/app/service/data/product';
import Utils from 'src/app/shared/utils';
import { IonicModule, Platform, PopoverController, ToastController } from '@ionic/angular';
import { BehaviorSubject } from 'rxjs';
import { ProductInCart } from 'src/app/service/data/product-in-cart';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { Cart } from 'src/app/service/data/cart';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { resetCart, setCart } from 'src/app/store/actions/cart.actions';
import { Store } from '@ngrx/store';
import { QuantityPopoverComponent } from 'src/app/private/quantity-popover/quantity-popover.component';
import { Keyboard } from '@capacitor/keyboard';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { ProductQuantityPipe } from 'src/app/shared/product-quantity';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
import { ImageOfflineService } from 'src/app/service/syncro-v2/image-offline.service';

@Component({
    selector: 'app-article-with-attributes',
    templateUrl: './article-with-attributes.component.html',
    styleUrls: ['./article-with-attributes.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
      TranslateModule,
      SafePipe,
      CommonModule,
      ProductQuantityPipe,
      ZeroRemoverPipe
    ]
})
export class ArticleWithAttributesComponent implements OnInit {
  @Input() datacolCategory: CatalogCard;
  @Input() isProspect: boolean;
  @Input() customerUid: string;
  @Input() products: Product[] = [];
  productInCart$: BehaviorSubject<ProductInCart[]> = new BehaviorSubject<ProductInCart[]>([]);
  cart: BehaviorSubject<Cart> = new BehaviorSubject<Cart>(null);
  @Output() closePopover = new EventEmitter();
  @Output() addToCart = new EventEmitter();
  featureMatrix: { code: string, name: string, value: string }[] = [];
  featureColumns: string[] = [];
  itemLabel = this._translate.instant('PRODUCT.ITEM');
  descriptionLabel = this._translate.instant('PRODUCT.DESCRIPTION');
  imageDirectory: string = localStorage.getItem("imageDirectory");

  constructor(private _platform: Platform, private _translate: TranslateService, private _dbService: HybridDbService,
    private _lsService: LocalStorageService, private _store: Store<any>, private _toastController: ToastController,
    private popoverController: PopoverController, private _imageOfflineService: ImageOfflineService) {
      addIcons({ closeOutline });
    }

  async ngOnInit() {
    if (this._platform.is('capacitor')) {
      Keyboard.addListener('keyboardWillShow', (info) => {
        this.adjustPopoverPosition(info.keyboardHeight);
      });

      Keyboard.addListener('keyboardWillHide', () => {
        this.resetPopoverPosition();
      });
    }

    this.products.forEach((product) => {
      // Converte classifications in JSON e lo cast a Classification[]
      const classifications = Utils.objToJson(product.classifications) as Classification[];
      if (!!classifications) {
        // Itera su ogni classificazione
        classifications.forEach((element: Classification) => {
          if (!!element.features) {
            // Itera su ogni feature
            element.features.forEach((item: Feature) => {
              if (!!item.featureValues && item.featureValues.length > 0) {
                // Aggiunge la feature alla lista di featureMatrix
                this.featureMatrix.push({ code: product.code, name: item.name, value: item.featureValues[0].value });
              }
            });
          }
        });
      }
    });
    let nameSet = new Set<string>();
    this.featureMatrix.forEach(feature => {
      nameSet.add(feature.name);
    });
    this.featureColumns = Array.from(nameSet);

    this.products = await Promise.all((this.products as Product[]).map(async (item) => {
      if (!!item.image) {
        item.image = await Utils.resolveImageUrlWithOffline(
          this._imageOfflineService,
          this._platform,
          this.imageDirectory,
          item.image
        );
      }
      else
        item.image = null;
      return item;
    }));
    this.loadCart();
  }

  adjustPopoverPosition(keyboardHeight: number) {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', `--offset-y: -${keyboardHeight / 2}px !important;`);
    }
  }

  resetPopoverPosition() {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', '');
    }
  }

  private async loadCart() {
    if (!!this.customerUid && !this.isProspect) {
      await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
        if (!!data && data.length > 0 && !!data[0]) {
          this.cart.next({ ...data[0] });
          if (!!data[0].products)
            this.productInCart$.next(Utils.objToJson(data[0].products));
        } else {
          this.productInCart$.next([]);
        }
      });
    }
  }

  getValue(item: Product, columnName: string) {
    let finalValue = '';
    if (!!item.classifications && item.classifications.toString() != "null" && item.classifications != null) {
      const classifications = Utils.objToJson(item.classifications) as Classification[];
      classifications.forEach((element: Classification) => {
        if (element.features.filter(i => i.name === columnName).length > 0 &&
          !!element.features.filter(i => i.name === columnName)[0].featureValues &&
          element.features.filter(i => i.name === columnName)[0].featureValues.length > 0
        )
          if (element.features.filter(i => i.name === columnName)[0].featureValues.length === 1)
            finalValue = element.features.filter(i => i.name === columnName)[0].featureValues[0].value;
          else if (element.features.filter(i => i.name === columnName)[0].featureValues.length === 2)
            finalValue = element.features.filter(i => i.name === columnName)[0].featureValues.map(item => item.value).join(' - ');
          else if (element.features.filter(i => i.name === columnName)[0].featureValues.length > 2)
            finalValue = element.features.filter(i => i.name === columnName)[0].featureValues.map(item => item.value).join(' ');
      });
    }
    return finalValue;
  }

  close() {
    this.closePopover.emit();
  }

  async showMultipleQuantity(product: Product) {
    if (document.querySelector('.quantity-popover') == null) {
      const popover = await this.popoverController.create({
        component: QuantityPopoverComponent,
        event: event,
        cssClass: 'quantity-popover',
        translucent: true,
        componentProps: { product }
      });
      popover.onDidDismiss().then((data) => {
        if (!!data.data && !!data.data.newQuantity)
          this.addOneToCart(product, +data.data.newQuantity);
      });

      return await popover.present();
    }
  }

  async addOneToCart(product: Product, multiQuantity?: number) {
    if (!this.isProspect) {
      var cart: Cart = Object.assign({}, this.cart.getValue() as Cart);
      if (!!cart && !!cart.products) {
        cart.products = Utils.objToJson(cart.products);
        const index = cart.products.findIndex(item => item.idProduct === product.code);
        if (index < 0) {
          cart.products.push({ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: +product.minimumDeliveryQuantity });
        } else {
          cart.products[index].quantity = multiQuantity ? multiQuantity : (cart.products[index].quantity + +product.minimumDeliveryQuantity);
        }
        await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(_ => {
          this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
          this.cart.next({ ...cart });
        });
      } else {
        var productsInCart = [{ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: product.minimumDeliveryQuantity }];
        await this._dbService.insertOrReplace("carts", [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }],
          ['id', 'agentCode', 'idCustomer', 'products'],
          [this._lsService.get("customerAgentCode"), this.customerUid, JSON.stringify(productsInCart)]).then(async _ => {
            var cartId = -1;
            await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
              if (!!data[0] && !!data[0]['id']) {
                this.cart.next({ ...data[0] });
                const rows = data[0];
                cartId = rows.id;
              }
            });
            if (cartId > 0)
              this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: cartId, currentQuantityInCart: (productsInCart.length) } }));
          }
          );
      }
      this.presentToast(this._translate.instant("PRODUCT.ITEM_ADDED") + product.code.replace(/^0+/, ''));
      this.addToCart.emit();
    }
  }

  private async presentToast(msg: string) {
    Utils.showSnack(this._toastController, msg, this._translate.instant("SETTINGS.DONE"));
  }

  async removeQuantity(product: Product) {
    var cart: Cart = this.cart.getValue() as Cart;
    cart.products = Utils.objToJson(cart.products);
    const index = cart.products.findIndex(item => item.idProduct === product.code);
    if (index >= 0) {
      const toDelete = +product.minimumDeliveryQuantity === cart.products[index].quantity;
      if (toDelete)
        this.removeProduct(product);
      else {
        if (cart.products[index].quantity > 0) {
          cart.products[index].quantity = cart.products[index].quantity - +product.minimumDeliveryQuantity;
          await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(_ => {
            this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
            this.cart.next({ ...cart });
          });
        }
      }
    }
    this.addToCart.emit();
  }

  async removeProduct(product: Product) {
    var cart: Cart = this.cart.getValue() as Cart;
    cart.products = Utils.objToJson(cart.products);
    const tmpProducts = cart.products.filter((item) => item.idProduct !== product.code);
    if (tmpProducts.length > 0) {
      await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(tmpProducts) }]).then(async _ => {
        cart.products = tmpProducts;
        await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.customerUid }]).then(async (data: Cart[]) => {
          if (!!data[0] && !!data[0]['id']) {
            const rows = data[0];
            this._store.dispatch(setCart({ cart: { idCustomer: this.customerUid, idCart: rows.id, currentQuantityInCart: (tmpProducts.length) } }));
            this.cart.next({ ...cart });
          }
        });
      });
    } else {
      this.deleteCart(cart.id);
    }
  }

  private async deleteCart(cartId: number) {
    await this._dbService.deleteRecord('carts', [{ key: 'id', value: cartId }]).then(_ => {
      Utils.showSnack(this._toastController, this._translate.instant('CARTS.CART_EMPTY'));
      this._store.dispatch(resetCart());
      this.cart.next(null);
    });
  }
}
