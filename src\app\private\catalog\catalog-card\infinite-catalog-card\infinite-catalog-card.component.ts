import { Component, inject } from '@angular/core';
import { CatalogCardComponent } from '../catalog-card.component';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { addIcons } from 'ionicons';
import { star, starOutline, logoEuro, shareSocialOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
import { IonIcon } from '@ionic/angular/standalone';

@Component({
    selector: 'app-infinite-catalog-card',
    templateUrl: './infinite-catalog-card.component.html',
    styleUrls: ['./infinite-catalog-card.component.scss'],
    standalone: true,
    imports: [
      TranslateModule,
      CommonModule,
      SafePipe,
      IonIcon
    ]
})
export class InfiniteCatalogCardComponent extends CatalogCardComponent {
  
  protected catalogService = inject(CatalogService);
  
  constructor() {
    super();
    addIcons({ star, starOutline, logoEuro, shareSocialOutline });
  }
}
