@import '../_common.scss';

.card div div.icon {
  width: 100%;
  height: 100%;
}

// .card div div.detail {
//   width: 100%;
//   align-items: end;
//   display: grid;
//   grid-template-columns: 1fr;
//   grid-template-rows: calc(14px * 3) calc(100% - 14px * 3 - 40px) 40px;
//   max-height: 100%;
// }

::ng-deep .swiper-slide .medium img {
  width: auto !important;
  height: 100% !important;
  max-height: 26vh !important;
  aspect-ratio: 1 !important;
  margin: auto !important;
}

/* TABLET verticale */
@media screen and (min-width: 501px) and (max-width: 850px) and (orientation: portrait) {
  ::ng-deep .swiper-slide .medium img {
    height: auto !important;
    width: 100% !important;
    max-height: auto !important;
    aspect-ratio: 1 !important;
    margin: auto !important;
  }
}

.card.category.red-arrow div {
  height: 225px;
  width: 85%;
  margin: auto;
}

.card.category.red-arrow .name {
  font-size: xx-large;
}

.card.product.medium > div {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: 50% 50%;
  .detail {
      display: grid;
      grid-template-columns: 1fr;
      grid-template-rows: calc(3 * (18px + 3px)) calc(100% - ((3 * (18px + 3px)) + 40px)) 40px !important;
      max-height: 100%;
      align-items: start;
    .name {
      display: -webkit-box;
      height: calc((3 * (18px + 3px)) - 2px) !important;
      font-size: 18px;
      line-height: 18px;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -ms-word-break: break-all;
      word-break: break-word;
      color: var(--ion-dat-red) !important;
      padding-left: 0px;
    }
    .description {
      display: -webkit-box;
      height: calc(100% - 43px);
      font-size: 14px;
      line-height: 17px;
      -webkit-line-clamp: 8;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -ms-word-break: break-all;
      word-break: break-word;
      padding-left: 0px;
      padding-right: 10px;
      text-align: left;
      max-height: calc((50vh - 77px) - ((3 * (18px + 3px)) + 40px + 35px));
    }
    &::after {
      content: "[...]";
      color: var(--ion-dat-red);
      position: relative;
      text-align: left;
      top: -70px;
    }
  }
}


// @supports (-webkit-touch-callout: none) {
//   /* CSS specific to iOS devices */
//   .card.product.medium > div {
//     .detail {
//       grid-template-rows: calc(18px * 3 + 3px) calc(100% - ((18px * 3) + 3px) - 40px) 40px !important;
//       .name {
//         height: calc(18px * 3 + 3px) !important;
//       }
//       .description {
//         height: calc(100% - 30px);
//       }
//     }
//   }
// }

// .card.product div .detail .name {
//   color: var(--ion-dat-red)!important;
//   padding-left: 0px;
//   -webkit-line-clamp: 2;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   -ms-word-break: break-all;
//   word-break: break-all;
//   word-break: break-word;
//   margin-bottom: 20px;
//   -webkit-box-orient: vertical;
//   height: 126px !important;
//   font-size: 18px;
//   line-height: 19px;
//   align-items: flex-start;
// }

// .card.product div .detail .description {
//   display: block;
//   padding-right: 5px;
//   text-align: left;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   height: calc(100% - 60px);
// }

// .card.product div .detail table {
//   vertical-align: top;
//   margin-top: 15px;
//   height: min-content;
// }
