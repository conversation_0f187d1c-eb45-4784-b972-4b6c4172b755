.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        height: 99vh;
        width: 99vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            .close {
                position: relative;
                z-index: 9999;
                background-color: transparent;
                justify-content: end;
            }
        }
        .row {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-top: -77px;
            padding: 0px;
            .product-image {
                width: 99vw;
                height: 99vh;
                pinch-zoom {
                    background-color: #FFF !important;
                    width: 99vw;
                    height: 99vh;
                    div {
                        width: 99vw;
                        height: 99vh;
                        img {
                            position: relative;
                            z-index: 9998;
                            width: 98%;
                            height: 98%;
                            object-fit: contain;
                            margin: auto;
                            position: absolute;
                            top: 0;
                            left: 0;
                            bottom: 0;
                            right: 0;
                            &.full-screen{
                                object-fit: cover;
                                object-position: left top; 
                            }
                        }
                    }
                }
            }
        }
    }
}

