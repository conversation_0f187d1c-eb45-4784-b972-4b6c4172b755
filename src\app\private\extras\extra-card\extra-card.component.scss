.module-box {
    display: block;
    position: relative;
    width: 120px;
    height: 170px;
    margin: 5px;
    border: 1px solid var(--ion-dat-gray);
    display: block;
    float: left;
    &:active {
      transform: translateY(5px);
    }
  }
  
  .module-body {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
  
  .download-button-div {
    margin: 0 auto;
    text-align: center;
    position: relative;
    display: flex;
    opacity: 0;
    height: 100%;
    align-items: center;
  }
  
  .download-button-div:before {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 99;
  }
  
  .download-button-div img {
    transition: opacity ease .5s;
  }
  
  .download-button-div:active {
    opacity: .6;
  }