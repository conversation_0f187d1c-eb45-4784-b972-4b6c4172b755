.title {
    position: absolute;
    top: calc(0px + var(--ion-safe-area-top, 0));
    height: 44px;
    align-items: center;
    display: flex;
    color: white;
    z-index: 9999;
    text-transform: uppercase;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100vw - 280px);
    justify-content: flex-end;
}
.container {
    background-color: var(--ion-dat-gray);
    margin: 2em 2em 0 0;
    width: calc(100% - 4em);
    height: 81vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-wrap: wrap;
    padding: 1em;
    .settings {
        background-color: var(--ion-dat-white);
        height: fit-content;
        align-self: flex-start;
        padding: 1.5em;
        .navigation-choose {
            display: flex;
            flex-direction: column;
            max-width: 250px;
            ion-list-header > ion-label {
                text-transform: uppercase;
                font-weight: bold;
                font-size: 14pt;
                text-align: center;
                margin-bottom: 20px;
            }
            ion-list{
                margin-bottom: 20px;
            }
            .nav-icon {
                margin-right: 10px;
                width: 1.5em;
            }
            ion-button {
                height: 1.8em;
                width: 100%;
                margin-bottom: 20px;
                &:active {
                    transform: translateY(5px);
                }
            }
        }
    }
    .footer {
        font-size: 16px;
        font-weight: bold;
        text-transform: uppercase;
    }
}

ion-list-header, ion-item {
    padding-inline-start: 0;
    --padding-start: 0;
    ion-label {
        margin-top: 0;
    }
}