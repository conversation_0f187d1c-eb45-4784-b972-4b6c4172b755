.card {
    border: 1px solid var(--ion-dat-middle-gray);
    border-radius: 3px;
    width: 100%; //FIXME: da verificare
    height: 115px; /* vanno aggiornati anche i calcoli per la responsività in catalog.ts */
    &:active {
        transform: translateY(5px);
    }
}

.customer {
    background-color: var(--ion-dat-white);
    color: var(--ion-dat-text);
    text-align: left;
    padding: 1rem;
    font-size: 12px;
    p {
        margin: 0;
        line-height: 20px;
    }
    &.selected {
        background-color: var(--ion-dat-selection-gray);
    }
    &.status-A {
        border-right: 10px solid var(--ion-dat-red);
    }
    &.status-S {
        border-right: 10px solid var(--ion-dat-orange);
    }
}

.prospect {
    background-color: var(--ion-dat-green);
    color: var(--ion-dat-white);
    font-size: 40px;
    text-transform: uppercase;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    padding: 15px;
}

.customer-label {
    background-color: var(--ion-dat-middle-gray);
    color: var(--ion-dat-white);
    font-size: 125px;
    text-transform: uppercase;
    font-weight: bold;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: left;
    border: none;
    padding: 15px;
}
