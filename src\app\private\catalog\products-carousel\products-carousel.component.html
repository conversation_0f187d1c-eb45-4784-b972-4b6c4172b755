<app-navigation-queue (goToCategory)="goToCategory($event)"></app-navigation-queue>
<ng-container *ngIf="{ myProducts: pages$ | async } as productData">
  <swiper-container #carslides id="carslides" (swiperslidechange)="slideChanges($event)">
    <swiper-slide *ngFor="let item of productData.myProducts;let i = index" >
      <div class="slider-grid"> 
      <app-product *ngIf="currentIndex == i" #productComponent
        [datacolCategory]="item" [customerUid]="customerUid"
        [customerName]="customerName"
        [isFavorite]="item.isFavorite" [isProspect]="isProspect"
        [code]="item.id" 
        [isPricesShown]="isPricesShown" [isInfoShown]="isInfoShown"
        [showNavigator]="showNavigator"
        (showArticleOut)="showArticle($event)"
        (showArticleWithAttributeOut)="openArticleWithAttributes($event)"
        (showDocumentOut)="openPdf($event)"
        (openVideoOut)="openVideo($event)"
        (openImageViewerOut)="openImageViewer($event)"></app-product>
      </div>
    </swiper-slide>
  </swiper-container>

</ng-container>

<div *ngIf="customerName" class="customer-name">
  <ng-container *ngIf="!isProspect"><img src="../../../../assets/svg/person.svg" /><span [innerHTML]="customerName"></span></ng-container>
  <ng-container *ngIf="isProspect">{{ 'CATALOG.PROSPECT' | translate }} / <span [innerHTML]="customerName"></span></ng-container>
</div>

<app-right-bar [catalogCard]="products[currentIndex]" (markFavorite)="markFavorite($event)" [customerUid]="customerUid" [isFavorite]="isFavorite$|async" [isProspect]="isProspect" [isNavigatorShown]="showNavigator" (showHideNavigatorPanel)="showNavigator = $event" (openInfo)="openInfo()" (openPrices)="openPrices()"></app-right-bar>
<app-navigation-panel [ngClass]="{'visible': showNavigator}" (hideNavigator)="showNavigator = false" (navToPage)="navToPage($event)"></app-navigation-panel>
<app-search *ngIf="isFilterShown" [viewType]="'catalog'" [simpleView]="true" (closeSearch)="closeProductSearch($event)"></app-search>
<app-article *ngIf="isArticleShown" [@datacolPopup] [customerUid]="customerUid" [isProspect]="isProspect" [article]="currentArticle" (addToCart)="productComponent.refreshArticles()" (closePopover)="hideArticle()" (imageViewer)="openImageViewer($event)"></app-article>
<app-prices *ngIf="isPricesShown" [@datacolPopup] [customerUid]="customerUid" [isProspect]="isProspect" [datacolCategoryName]="products[currentIndex].name" [products]="productComponent.getArticles()" (addToCart)="productComponent.refreshArticles()" (closePopover)="hidePrices()"></app-prices>
<app-article-with-attributes *ngIf="isArticleWithAttributesShown" [@datacolPopup] [customerUid]="customerUid" [isProspect]="isProspect"  [datacolCategory]="products[currentIndex]" [products]="productComponent.getArticles()" (addToCart)="productComponent.refreshArticles()" (closePopover)="hideArticleWithAttributes()"></app-article-with-attributes>

<app-download *ngIf="isDownloadShown" [@datacolPopup] [articleCode]="articleCode" [documents]="documentsForDownload" (closePopover)="hideDownload()"></app-download>
<app-videoplayer *ngIf="isVideoPlayerShown" [@datacolPopup] [videoUrl]="videoUrl" (hideVideo)="hideVideo()"></app-videoplayer>
<app-image-viewer *ngIf="isImageViewerShown" [@datacolPopup] [image]="imageToShow" (closePopover)="hideImageViewer()"></app-image-viewer>