export interface Tag {
  idTag: number;
  keyTag: string;
  description: string;
  subTags: Tag[];
  checked?: boolean;
}

export interface TypologyContent {
  tags: Tag[];
  universalTag?: Tag;
}

export interface SectorHierarchyResponse {
  status: string;
  content: TypologyContent;
  error?: any;
}

export interface DatacolCategoryTag {
  keyTag: string;
  description?: string;
}

export interface DatacolCategoryTagsResponse {
  status: string;
  content: {
    tags: DatacolCategoryTag[];
  };
  error?: any;
}

export interface AddRemoveTagRequest {
  idCatalog: number;
  idSubCategory: number;
  keyTag: string;
}

export interface AddRemoveTagResponse {
  status: string;
  results?: Array<{
    esit?: {
      status: string;
      error?: {
        code: string;
        message?: string;
      };
    };
  }>;
  error?: any;
}

// Nuove interfacce per il sistema di filtraggio
export interface CustomerTagFilter {
  customerUid: string;
  settore?: string;
  attivita?: string;
  professione?: string;
  universale: boolean;
  customTags: string[]; // Tag aggiunti manualmente dall'agente
  isDefault: boolean; // Se true, usa solo i tag predefiniti del cliente
}

export interface FilterConfiguration {
  id: string;
  customerUid: string;
  name: string; // Nome della configurazione (es. "Default", "Personalizzata")
  activeTags: string[];
  isDefault: boolean;
  createdAt: number;
  lastUsed: number;
}

export interface CategoryFilterResult {
  visibleCategories: string[]; // IDs delle categorie visibili
  hiddenCategories: string[]; // IDs delle categorie nascoste
  totalVisible: number;
  totalHidden: number;
}

export interface TagFilterState {
  activeCustomerUid: string | null;
  currentFilter: CustomerTagFilter | null;
  availableConfigurations: FilterConfiguration[];
  isFilteringEnabled: boolean;
  lastFilterUpdate: number;
}
