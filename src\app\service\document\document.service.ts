import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';
import { CapacitorHttp } from '@capacitor/core';

@Injectable({
  providedIn: 'root'
})
export class DocumentService {

  getDocumentUrl(code: string, documentType: string, documentNumber: string, versionNumber: string) : Promise<any>{
    const language = localStorage.getItem('language');
    const url = `${environment.hybrisUrl}/${language}/EUR/p/${code}/openDocument?documentType=${documentType}&documentNumber=${documentNumber}&versionNumber=${versionNumber}`;

    return CapacitorHttp.get({url})
      .then(response => {
        return !!response && !!response.data ? JSON.parse(response.data) : null;
      })
      .catch(error => {
        console.error('getDocumentUrl', {error})
      });
  }

  getSharableUrl(code: string, type: 'c' | 'p' = 'c'): string {
    const language = localStorage.getItem('language');
    return `${environment.hybrisUrl}/${language}/EUR/${type}/${code}`;
  }
}
