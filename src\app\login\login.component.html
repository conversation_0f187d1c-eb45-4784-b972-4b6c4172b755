<div class="bg-container"></div>
<div class="bg-container-top"></div>
<div class="bg-container-bottom"></div>
<div class="bg-container-logo">
  <div class="bg-logo-trans"></div>
</div>
<div class="container">
  <div class="box">
    <div class="card-content">
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="7" size-lg="7" class="p-12">
            <object type="image/svg+xml" data="../../assets/svg/logo.svg" class="logo-datacol">
              <img src="../../assets/svg/logo.svg" />
            </object>
            <div class="custom-error">{{customError}}</div>
            <form class="login-form" [formGroup]="loginFormGroup">
                <ion-input
                  name="username"
                  [(ngModel)]="formLoginValue"
                  formControlName="formLogin"
                  #formLogin
                  class="custom user-input"
                  fill="outline"
                  label-placement="floating"
                  [label]="'LOGIN.USERNAME' | translate"
                  (ionChange)="onChange($event)">
                  <ion-icon slot="start" name="person" aria-hidden="true"></ion-icon>
                </ion-input>
                <ion-note slot="error" *ngIf="loginFormGroup.get('formLogin')?.errors?.['required'] && loginFormGroup.get('formLogin')?.touched">
                  {{ 'LOGIN.REQUIRED' | translate }}
                </ion-note>
                <ion-note slot="error" *ngIf="loginFormGroup.get('formLogin')?.errors?.['pattern'] && loginFormGroup.get('formLogin')?.touched">
                  {{ 'LOGIN.PATTERN_ERROR' | translate }}
                </ion-note>

                <ion-input
                  name="password"
                  [(ngModel)]="formPasswordValue"
                  formControlName="formPassword"
                  #formPassword
                  class="custom password-input"
                  type="password"
                  fill="outline"
                  label-placement="floating"
                  [label]="'LOGIN.PASSWORD' | translate"
                  (ionChange)="onChange($event)">
                  <ion-icon slot="start" name="lock-closed" aria-hidden="true"></ion-icon>
                </ion-input>
                <ion-note slot="error" *ngIf="loginFormGroup.get('formPassword')?.errors?.['required'] && loginFormGroup.get('formPassword')?.touched">
                  {{ 'LOGIN.REQUIRED' | translate }}
                </ion-note>
                <br/> 
              <ion-button expand="block" (click)="login(null)">{{ 'LOGIN.LOGIN' | translate }}</ion-button>
              <br/>
              <!-- TODO: da mettere qui il link con plugin di apertura su browser -->
              <a class="link" (click)="presentToast()">{{ 'LOGIN.FORGOT_PASSWORD' | translate }}</a>
            </form>
          </ion-col>
          <ion-col>
            <!-- {{ 'LOGIN.USEFUL_LINKS' | translate }} -->
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  </div>
</div>