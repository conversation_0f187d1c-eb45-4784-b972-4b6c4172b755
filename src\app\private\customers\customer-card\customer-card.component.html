<ng-container *ngTemplateOutlet="customer.type === 'LABEL' ? customerLabel : (customer.type === 'PROSPECT' ? prospect : customerCard); context: { $implicit: customer }"></ng-container>

<ng-template #customerLabel let-customer>
  <div class="card customer-label">{{customer.name}}</div>
</ng-template>

<ng-template #prospect let-customer>
  <div class="card prospect" (click)="showDetail(customer.uid, customer.type)">{{ 'CUSTOMER_LIST.PROSPECT' | translate }}</div>
</ng-template>

<ng-template #customerCard let-customer>
  <div class="card customer status-{{customer.customerStatus}}"  [ngClass]="{'selected': customer.uid == selectedCustomerUid }" (click)="showDetail(customer.uid, customer.type)">
    <b [innerHTML]="customer.name"></b>
    <p>{{customer.uid}}</p>
    <p>{{ 'CUSTOMER_CARD.SALES_LINE' | translate }}: {{getDivision(customer)}}</p>
    <p>{{ 'CUSTOMER_CARD.ACTIVITY' | translate }}: {{customer.industrialSector}}</p>
  </div>
</ng-template>
