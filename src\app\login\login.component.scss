.bg-container {
    position: absolute;
    height: 100vh;
    width: 100vw;
    background-image: url('../../assets/svg/stripe.svg');
    opacity: .5;
    z-index: 3;
}

.bg-container-top {
    position: absolute;
    height: 50vh;
    width: 100vw;
    background-image: linear-gradient(180deg, #BF0101, #6A0000);
    background-repeat: no-repeat;
    z-index: 1;
}

.bg-container-bottom {
    position: absolute;
    top: 50vh;
    height: 50vh;
    width: 100vw;
    background: #FFF;
    z-index: 1;
}

.bg-container-logo {
    position: absolute;
    top: 60vh;
    height: 40vh;
    width: 100vw;
    z-index: 4;
    text-align: center;
    .bg-logo-trans {
        width: calc(100% - 80px);
        height: 100%;
        background-image: url('../../assets/svg/bg-logo.svg');
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;
        z-index: 9998;
        aspect-ratio: 16/9;
        margin-left: 40px;
    }
}

.container {
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    margin-bottom: 15vh;
    .box {
        height: fit-content;
        width: 80%;
        background-color: white;
        border-radius: 3px;
        .card-content {
            height: 100%;
            ion-grid {
                height: 100%;
                padding: 0;
                --ion-grid-column-padding: 70px;
                --ion-grid-column-padding-xs: 20px;
                ion-col:nth-child(2) {
                    background-color: #F8F8F8;
                    border-top-right-radius: 3px;
                    border-bottom-right-radius: 3px;
                }
                ion-row {
                    height: 100%;
                    .logo-datacol {
                        margin-bottom: 1em;
                    }
                    .login-form {
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                    }
                }
            }
        }
    }
}


.custom-error {
    min-height: 41px;
    color: var(--ion-dat-red);
    padding: 11px;
}

.user-input::before,
.password-input::before {
    position: relative;
    z-index: 9999;
    top: 10px;
    padding-right: 20px;
    left: 8px;
    background-size: 15px;
    background-repeat: no-repeat;
    display: inline-block;
    width: 15px; 
    height: 15px;
    content:"";
}


ion-button {
    --background: var(--ion-dat-black);
    color: var(--ion-dat-white);
}

.link {
    color: #5C6368;
    font-weight: bold;
    margin-left: 1em;
}

/* SMARTPHONE orizzontale */
@media screen and (max-width: 850px) and (orientation: landscape) {
    .container{
        height: 100% !important;
        width: 100% !important;
        margin-bottom: 0 !important;
        ion-col {
            padding-inline-start: 15px !important;
            padding-inline-end: 15px !important;
        }
    }
}


/* TABLET verticale */
@media screen and (min-width: 501px) and (max-width: 850px) and (orientation: portrait) {
    ion-col {
        padding-inline-start: 25px !important;
        padding-inline-end: 25px !important;
    }
}

ion-input.custom {
    --border-radius: 30px !important;
    --padding-start: 32px !important;
}