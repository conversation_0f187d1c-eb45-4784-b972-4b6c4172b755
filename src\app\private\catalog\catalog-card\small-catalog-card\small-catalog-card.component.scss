@import '../_common.scss';

::ng-deep .swiper-slide .small img {
  width: 100px !important;
  max-width: 100px !important;
  height: 50px !important;
  max-height: 100px !important;
}

.card div div.icon {
  position: relative;

  img {
    width: 100px !important;
  }

  .subcategories-count {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--ion-dat-red);
    color: var(--ion-dat-white);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid var(--ion-dat-white);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    z-index: 10;
  }
}

.card.product div .detail table td .name {
  padding-right: 15px;
}