// Base styles from assign-tag-dialog
.dialog-accordion-panel {
    background-color: var(--dialog-accordion-bg);
    border-radius: 5px;
    border: 1px solid var(--dialog-accordion-bg);
    margin-top: 1em;
}

.mat-expansion-panel {
    background-color: var(--dialog-accordion-bg) !important;
}

.dialog-accordion-title {
    color: var(--dialog-accordion-color);
}

.dialog-toggle {
    margin-right: 1em;
}

.mat-expansion-panel:not([class*=mat-elevation-z]) {
    box-shadow: none;
}

.mat-expansion-panel-body {
    padding: 10px 24px 16px;
}

.mat-accordion>.mat-expansion-panel-spacing:last-child, 
.mat-accordion>*:last-child:not(.mat-expansion-panel) .mat-expansion-panel-spacing {
    margin-top: 18px !important;
}

.unclickable-panel {
    pointer-events: none;
}

.toggle-profession-label {
    margin-right: 10em; 
    margin-right: 20em;
}

.dialog-toggle.profession-toggle {
    margin-left: 1.5em;
}

// Additional styles for tag filter panel
.filter-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: var(--ion-color-light);
    border-radius: 8px 8px 0 0;
}

.dat-dialog-title {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ion-color-primary);
}

.filter-toggle-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    
    span {
        font-size: 0.9rem;
        color: var(--ion-color-medium);
    }
}

.dat-hr {
    border: none;
    height: 1px;
    background-color: var(--ion-color-light-shade);
    margin: 0;
}

.current-filter-summary {
    padding: 1rem;
    background-color: var(--ion-color-light-tint);
    border-left: 4px solid var(--ion-color-primary);
    margin-bottom: 1rem;
    
    h4 {
        margin: 0 0 0.5rem 0;
        font-size: 1rem;
        color: var(--ion-color-primary);
    }
    
    .filter-summary {
        margin: 0 0 1rem 0;
        font-size: 0.9rem;
        color: var(--ion-color-medium);
    }
}

.predefined-tags-display,
.custom-tags-display {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.tag-chip {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    
    &.predefined {
        background-color: var(--ion-color-primary);
        color: var(--ion-color-primary-contrast);
    }
    
    &.universal {
        background-color: var(--ion-color-success);
        color: var(--ion-color-success-contrast);
    }
    
    &.custom {
        background-color: var(--ion-color-secondary);
        color: var(--ion-color-secondary-contrast);
        cursor: pointer;
        transition: background-color 0.2s ease;
        
        &:hover {
            background-color: var(--ion-color-secondary-shade);
        }
    }
}

.tag-selection-content {
    padding: 1rem;
    
    h4 {
        margin: 0 0 1rem 0;
        font-size: 1rem;
        color: var(--ion-color-dark);
    }
}

.predefined-indicator {
    margin-left: 0.5rem;
    font-size: 0.8rem;
    opacity: 0.7;
}

.no-filter-state {
    padding: 2rem;
    text-align: center;
    color: var(--ion-color-medium);
    
    p {
        margin: 0.5rem 0;
    }
    
    .help-text {
        font-size: 0.9rem;
        font-style: italic;
    }
}

.dat-overlay-loader {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

// Responsive adjustments
@media (max-width: 768px) {
    .filter-panel-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .predefined-tags-display,
    .custom-tags-display {
        justify-content: center;
    }
    
    .toggle-profession-label {
        margin-right: 5em;
    }
}

// Action buttons
.filter-actions {
    padding: 1rem;
    background-color: var(--ion-color-light-tint);
    border-radius: 0 0 8px 8px;

    .action-buttons {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-bottom: 1rem;

        button {
            min-width: 140px;
            height: 40px;
            border-radius: 20px;
            font-weight: 500;
            text-transform: none;

            mat-icon {
                margin-right: 0.5rem;
                font-size: 1.2rem;
            }

            &.apply-filters-btn {
                background-color: var(--ion-color-primary);
                color: var(--ion-color-primary-contrast);

                &:hover:not(:disabled) {
                    background-color: var(--ion-color-primary-shade);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            }

            &.reset-filters-btn {
                border: 2px solid var(--ion-color-medium);
                color: var(--ion-color-medium);

                &:hover:not(:disabled) {
                    background-color: var(--ion-color-light);
                    border-color: var(--ion-color-medium-shade);
                    color: var(--ion-color-medium-shade);
                }

                &:disabled {
                    opacity: 0.6;
                    cursor: not-allowed;
                }
            }
        }
    }

    .help-text {
        text-align: center;
        font-size: 0.85rem;
        color: var(--ion-color-medium);
        font-style: italic;
        margin: 0;
    }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
    .filter-panel-header {
        background-color: var(--ion-color-dark);
    }

    .current-filter-summary {
        background-color: var(--ion-color-dark-tint);
    }

    .filter-actions {
        background-color: var(--ion-color-dark-tint);
    }

    .dat-overlay-loader {
        background-color: rgba(0, 0, 0, 0.8);
    }
}

// Responsive adjustments for action buttons
@media (max-width: 768px) {
    .filter-actions .action-buttons {
        flex-direction: column;
        align-items: center;

        button {
            width: 100%;
            max-width: 200px;
        }
    }
}
