<div class="popover">
  <div class="container">
    <div class="header">
      <button class="close" (click)="close()">
        <ion-icon name="close-outline" size="large"></ion-icon>
      </button>
    </div>
    <div class="row">
      <h1>{{"PROSPECTS.TITLE" | translate}}</h1>
      <div class="prospects">
        <ion-grid>
          <ion-row>
            <ion-col size="6" size-md="4" size-lg="4" *ngFor="let item of prospectTypes" (click)="startCustomerSession(item)">
              <div>{{item.name}}</div>
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
    </div>
  </div>
</div>