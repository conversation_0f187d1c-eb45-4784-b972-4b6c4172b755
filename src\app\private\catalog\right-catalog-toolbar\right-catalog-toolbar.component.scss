.right-bar {
  width: 65px;
  display: flex;
  flex-direction: column;
  position: absolute;
  right: 0;
  height: calc(100% - 88px); /* Tolto header e footer*/
  top: 44px;
  padding-top: 10px;
  justify-content: space-between;
  .top-cell, .bottom-cell {
      flex-direction: column;
      button {
        height: 65px;
        width: 65px;
        background-color: transparent;
        display: none;
        justify-content: center;
        &.visible {
          display: inherit;
        }
        &:active {
            transform: translateY(5px);
        }
        object {
            pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
        }
        &.on object svg {
            stroke: var(--ion-dat-red);
            fill: var(--ion-dat-red);
        }
      }
  }
  .top-cell button {
    padding-top: 5px;
  }
}
