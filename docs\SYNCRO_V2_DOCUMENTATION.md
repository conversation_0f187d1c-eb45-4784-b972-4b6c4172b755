# Sistema di Sincronizzazione v2 - Documentazione Completa

## Indice
1. [Panoramica](#panoramica)
2. [Architettura](#architettura)
3. [Componenti Principali](#componenti-principali)
4. [API Endpoints](#api-endpoints)
5. [<PERSON><PERSON>si di Lavoro](#flussi-di-lavoro)
6. [Database Schema](#database-schema)
7. [Gestione Immagini Offline](#gestione-immagini-offline)
8. [Esempi di Utilizzo](#esempi-di-utilizzo)
9. [Troubleshooting](#troubleshooting)
10. [Migrazione da v1 a v2](#migrazione-da-v1-a-v2)

## Panoramica

Il Sistema di Sincronizzazione v2 è una completa riscrittura del sistema di sincronizzazione dati per l'applicazione mobile Datacol. È progettato per essere più efficiente, robusto e scalabile rispetto alla versione precedente.

### Caratteristiche Principali
- **Sincronizzazione paginata**: Gestisce grandi volumi di dati attraverso la paginazione
- **Gestione immagini offline**: Download e salvataggio automatico delle immagini per l'uso offline
- **Sincronizzazione incrementale**: Supporta timestamp per sincronizzazioni incrementali
- **Gestione categorie eliminate**: Traccia e rimuove le categorie cancellate
- **Architettura modulare**: Separazione chiara delle responsabilità tra servizi
- **Gestione errori robusta**: Recupero automatico da errori e logging dettagliato

### Differenze dalla v1
- Paginazione automatica invece di caricamento completo
- Gestione immagini integrata nel processo di sincronizzazione
- Database schema esteso con metadati di sincronizzazione
- API endpoints dedicati per ogni tipo di entità
- Migliore gestione degli errori e del recovery

## Architettura

Il sistema è basato su un'architettura a tre livelli:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                       │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │ SyncroComponent │  │ Other Components│                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    BUSINESS LOGIC LAYER                     │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           SyncroV2OrchestratorService                   │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │ Category Sync   │  │ Product Sync    │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                      DATA ACCESS LAYER                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ SyncroV2Service │  │ SyncroV2DbService│  │ImageOffline │ │
│  │   (API Calls)   │  │  (Database)     │  │  Service    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Componenti Principali

### 1. SyncroV2Service
**Percorso**: `src/app/service/syncro-v2/syncro-v2.service.ts`

Responsabile delle chiamate API verso il backend.

**Metodi principali**:
- `syncCategories(idCatalog, pageIn, pageSize)`: Sincronizza le categorie
- `syncProducts(idCatalog, pageIn, pageSize, idRootCategory?)`: Sincronizza i prodotti
- `syncDeletedCategories(idCatalog, pageIn, pageSize, fromTimestamp?)`: Sincronizza le categorie eliminate
- `syncAllProducts(idCatalog, pageSize, idRootCategory?, onPageCallback?)`: Sincronizza tutti i prodotti con paginazione automatica

### 2. SyncroV2DbService
**Percorso**: `src/app/service/syncro-v2/syncro-v2-db.service.ts`

Gestisce tutte le operazioni di database per la sincronizzazione v2.

**Metodi principali**:
- `updateDatabaseSchema()`: Aggiorna lo schema del database
- `saveCategoriesV2(categories, idCatalog, pageNumber, timestamp)`: Salva le categorie
- `saveProductsV2(products, idCatalog, pageNumber, timestamp, idRootCategory?)`: Salva i prodotti
- `getLastSyncTimestamp(idCatalog)`: Ottiene l'ultimo timestamp di sincronizzazione
- `markSyncStart(idCatalog, timestamp)`: Segna l'inizio di una sincronizzazione
- `markSyncComplete(idCatalog, timestamp, totalPages)`: Segna il completamento

### 3. SyncroV2OrchestratorService
**Percorso**: `src/app/service/syncro-v2/syncro-v2-orchestrator.service.ts`

Coordina l'intero processo di sincronizzazione.

**Metodi principali**:
- `syncCatalogCategories(idCatalog, pageSize, onProgress?)`: Sincronizza tutte le categorie
- `syncCatalogProducts(idCatalog, pageSize, idRootCategory?, onProgress?)`: Sincronizza tutti i prodotti
- `syncDeletedCategories(idCatalog, fromTimestamp?, pageSize, onProgress?)`: Sincronizza le categorie eliminate
- `syncComplete(idCatalog, pageSize, onProgress?)`: Sincronizzazione completa (categorie + categorie eliminate)

### 4. ImageOfflineService
**Percorso**: `src/app/service/syncro-v2/image-offline.service.ts`

Gestisce il download e il salvataggio delle immagini per l'uso offline.

**Metodi principali**:
- `downloadAndSaveImage(imageUrl, idCatalog, entityType, entityId, idRootCategory?)`: Scarica e salva un'immagine
- `getImageMetadata(imageUrl)`: Ottiene i metadati di un'immagine
- `resolveOfflineImageUrl(imageUrl)`: Risolve l'URL locale di un'immagine
- `cleanupOldImages(olderThanTimestamp, idCatalog?)`: Pulisce le immagini obsolete

## API Endpoints

### 1. Sincronizzazione Categorie
```
GET /appcatalogs/syncCategories?idCatalog={idCatalog}&pageIn={pageIn}&pageSize={pageSize}
```

**Parametri**:
- `idCatalog` (required): ID del catalogo
- `pageIn` (required): Numero della pagina (0-based)
- `pageSize` (required): Dimensione della pagina (consigliato: 10-50)

**Risposta**:
```json
{
  "status": "OK",
  "content": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "image": "string",
      "idCatalog": number,
      "idParent": "string",
      "idRootCategory": "string",
      "isRootCategory": boolean,
      "modifiedInstant": number,
      "productsCount": number
    }
  ],
  "totalPages": number,
  "totalCount": number
}
```

### 2. Sincronizzazione Prodotti
```
GET /appcatalogs/syncProducts?idCatalog={idCatalog}&pageIn={pageIn}&pageSize={pageSize}&idRootCategory={idRootCategory}
```

**Parametri**:
- `idCatalog` (required): ID del catalogo
- `pageIn` (required): Numero della pagina (0-based)
- `pageSize` (required): Dimensione della pagina (consigliato: 100)
- `idRootCategory` (optional): ID della categoria radice per filtrare i prodotti

**Risposta**:
```json
{
  "status": "OK",
  "content": [
    {
      "code": "string",
      "name": "string",
      "description": "string",
      "image": "string",
      "idSubCategory": "string",
      "minimumDeliveryQuantity": "string",
      "modifiedInstant": number,
      "cancellationInstant": "string"
    }
  ],
  "totalPages": number,
  "totalCount": number
}
```

### 3. Sincronizzazione Categorie Eliminate
```
GET /appcatalogs/syncDeletedCategories?idCatalog={idCatalog}&pageIn={pageIn}&pageSize={pageSize}&fromTimestamp={fromTimestamp}
```

**Parametri**:
- `idCatalog` (required): ID del catalogo
- `pageIn` (required): Numero della pagina (0-based)
- `pageSize` (required): Dimensione della pagina (consigliato: 10)
- `fromTimestamp` (optional): Timestamp di partenza per la sincronizzazione incrementale

**Risposta**:
```json
{
  "status": "OK",
  "content": [
    {
      "id": "string",
      "cancellationInstant": "string"
    }
  ],
  "totalPages": number,
  "totalCount": number
}
```

## Flussi di Lavoro

### 1. Sincronizzazione Completa delle Categorie

```mermaid
sequenceDiagram
    participant UI as SyncroComponent
    participant O as SyncroV2Orchestrator
    participant S as SyncroV2Service
    participant DB as SyncroV2DbService
    participant API as Backend API

    UI->>O: syncCatalogCategories(idCatalog, pageSize)
    O->>DB: markSyncStart(idCatalog, timestamp)
    O->>S: syncCategories(idCatalog, 0, pageSize)
    S->>API: GET /appcatalogs/syncCategories
    API-->>S: Response with categories page 0
    S-->>O: BasicResponse
    O->>DB: saveCategoriesV2(categories, idCatalog, 0, timestamp)
    
    loop For each additional page
        O->>S: syncCategories(idCatalog, pageN, pageSize)
        S->>API: GET /appcatalogs/syncCategories
        API-->>S: Response with categories page N
        S-->>O: BasicResponse
        O->>DB: saveCategoriesV2(categories, idCatalog, pageN, timestamp)
        O->>UI: onProgress callback
    end
    
    O->>DB: markSyncComplete(idCatalog, timestamp, totalPages)
    O-->>UI: SyncResult
```

### 2. Sincronizzazione Prodotti con Immagini

```mermaid
sequenceDiagram
    participant O as SyncroV2Orchestrator
    participant S as SyncroV2Service
    participant DB as SyncroV2DbService
    participant IMG as ImageOfflineService
    participant API as Backend API

    O->>S: syncProducts(idCatalog, pageIn, pageSize, idRootCategory)
    S->>API: GET /appcatalogs/syncProducts
    API-->>S: Response with products
    S-->>O: BasicResponse
    O->>DB: saveProductsV2(products, idCatalog, pageIn, timestamp, idRootCategory)
    
    loop For each product with image
        DB->>IMG: downloadAndSaveImage(imageUrl, idCatalog, 'PRODUCT', productCode, idRootCategory)
        IMG->>API: GET /multimedia/get/image/{catalog}/{idRootCategory}/{imageName}
        API-->>IMG: Image blob
        IMG->>IMG: Save image to filesystem
        IMG->>DB: Save image metadata to offline_images table
    end
```

### 3. Sincronizzazione Incrementale delle Categorie Eliminate

```mermaid
sequenceDiagram
    participant O as SyncroV2Orchestrator
    participant S as SyncroV2Service
    participant DB as SyncroV2DbService
    participant API as Backend API

    O->>DB: getLastSyncTimestamp(idCatalog)
    DB-->>O: lastTimestamp
    O->>S: syncDeletedCategories(idCatalog, 0, pageSize, lastTimestamp)
    S->>API: GET /appcatalogs/syncDeletedCategories?fromTimestamp=lastTimestamp
    API-->>S: Response with deleted categories
    S-->>O: BasicResponse
    
    loop For each deleted category
        O->>DB: deleteRecord('categories', [{key: 'id', value: categoryId}])
    end
```

## Database Schema

### Tabelle Esistenti Estese

#### 1. Tabella `categories`
Colonne aggiunte per la sincronizzazione v2:
```sql
ALTER TABLE categories ADD COLUMN lastSyncTimestamp TEXT;
ALTER TABLE categories ADD COLUMN idCatalog TEXT;
ALTER TABLE categories ADD COLUMN syncPageNumber TEXT;
ALTER TABLE categories ADD COLUMN syncedWithV2 TEXT;
ALTER TABLE categories ADD COLUMN isRootCategoryByRegex TEXT;
```

#### 2. Tabella `products`
Colonne aggiunte per la sincronizzazione v2:
```sql
ALTER TABLE products ADD COLUMN lastSyncTimestamp TEXT;
ALTER TABLE products ADD COLUMN idCatalog TEXT;
ALTER TABLE products ADD COLUMN syncPageNumber TEXT;
ALTER TABLE products ADD COLUMN syncedWithV2 TEXT;
ALTER TABLE products ADD COLUMN idRootCategory TEXT;
ALTER TABLE products ADD COLUMN imageOfflineAvailable TEXT;
```

### Nuove Tabelle

#### 3. Tabella `offline_images`
Memorizza i metadati delle immagini scaricate per l'uso offline:
```sql
CREATE TABLE offline_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    imageUrl TEXT NOT NULL,
    localPath TEXT NOT NULL,
    fileName TEXT NOT NULL,
    idCatalog INTEGER NOT NULL,
    idRootCategory TEXT,
    entityType TEXT NOT NULL, -- 'PRODUCT' or 'CATEGORY'
    entityId TEXT NOT NULL,
    downloadTimestamp INTEGER NOT NULL,
    fileSize INTEGER,
    mimeType TEXT,
    isAvailable INTEGER DEFAULT 1
);

CREATE INDEX idx_offline_images_url ON offline_images(imageUrl);
CREATE INDEX idx_offline_images_entity ON offline_images(entityType, entityId);
CREATE INDEX idx_offline_images_catalog ON offline_images(idCatalog);
```

## Gestione Immagini Offline

### Processo di Download
1. **Identificazione**: Durante il salvataggio di prodotti/categorie, viene identificata la presenza di immagini
2. **Download**: L'immagine viene scaricata tramite l'API `/multimedia/get/image/{catalog}/{idRootCategory}/{imageName}`
3. **Salvataggio**: L'immagine viene salvata nel filesystem del dispositivo nella directory `images/`
4. **Metadati**: I metadati dell'immagine vengono salvati nella tabella `offline_images`

### Risoluzione URL Offline
```typescript
// Esempio di utilizzo
const offlineUrl = await imageOfflineService.resolveOfflineImageUrl(originalImageUrl);
if (offlineUrl) {
    // Usa l'URL locale
    imageElement.src = offlineUrl;
} else {
    // Fallback all'URL originale o immagine placeholder
    imageElement.src = originalImageUrl || 'assets/placeholder.png';
}
```

### Pulizia Immagini Obsolete
```typescript
// Pulisce le immagini più vecchie di 30 giorni
const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
await imageOfflineService.cleanupOldImages(thirtyDaysAgo, catalogId);
```

## Esempi di Utilizzo

### 1. Sincronizzazione Completa di un Catalogo
```typescript
import { SyncroV2OrchestratorService, SyncProgress, SyncResult } from './syncro-v2-orchestrator.service';

// Inietta il servizio
constructor(private syncroV2Orchestrator: SyncroV2OrchestratorService) {}

async syncCompleteCatalog() {
    const catalogId = 1;
    const pageSize = 50;

    try {
        // Inizializza il database
        await this.syncroV2Orchestrator.initializeDatabase();

        // Sincronizzazione completa (categorie + categorie eliminate)
        const result: SyncResult = await this.syncroV2Orchestrator.syncComplete(
            catalogId,
            pageSize,
            (progress: SyncProgress) => {
                console.log(`Progresso: Pagina ${progress.currentPage}/${progress.totalPages}`);
                console.log(`Categorie processate: ${progress.categoriesProcessed}`);
                console.log(`Categorie eliminate: ${progress.deletedCategoriesProcessed}`);

                // Aggiorna la UI con il progresso
                this.updateProgressBar(progress);
            }
        );

        if (result.success) {
            console.log(`✅ Sincronizzazione completata in ${result.duration}ms`);
            console.log(`📁 Categorie processate: ${result.totalCategoriesProcessed}`);
            console.log(`🗑️ Categorie eliminate: ${result.totalDeletedCategoriesProcessed}`);
        } else {
            console.error('❌ Errore durante la sincronizzazione:', result.error);
        }
    } catch (error) {
        console.error('❌ Errore critico:', error);
    }
}
```

### 2. Sincronizzazione Solo Prodotti di una Categoria
```typescript
async syncProductsByCategory() {
    const catalogId = 1;
    const idRootCategory = '1002196';
    const pageSize = 100;

    const result: SyncResult = await this.syncroV2Orchestrator.syncCatalogProducts(
        catalogId,
        pageSize,
        idRootCategory,
        (progress: SyncProgress) => {
            console.log(`📦 Prodotti processati: ${progress.productsProcessed}`);
        }
    );

    console.log(`✅ Sincronizzati ${result.totalProductsProcessed} prodotti`);
}
```

### 3. Sincronizzazione Incrementale
```typescript
async syncIncremental() {
    const catalogId = 1;

    // Ottieni l'ultimo timestamp di sincronizzazione
    const lastTimestamp = await this.syncroV2DbService.getLastSyncTimestamp(catalogId);

    if (lastTimestamp) {
        // Sincronizza solo le categorie eliminate dall'ultimo sync
        const result = await this.syncroV2Orchestrator.syncDeletedCategories(
            catalogId,
            lastTimestamp,
            10
        );
        console.log(`🗑️ Rimosse ${result.totalDeletedCategoriesProcessed} categorie`);
    } else {
        console.log('⚠️ Nessun timestamp precedente trovato, esegui una sincronizzazione completa');
    }
}
```

### 4. Gestione Immagini Offline
```typescript
// Nel componente che mostra i prodotti
async loadProductWithOfflineImage(product: Product) {
    const imageDirectory = localStorage.getItem('imageDirectory');

    // Prova a risolvere l'immagine offline
    const offlineImageUrl = await this.imageOfflineService.resolveOfflineImageUrl(product.image);

    if (offlineImageUrl) {
        // Immagine disponibile offline
        this.productImageUrl = offlineImageUrl;
        this.isImageOffline = true;
    } else {
        // Usa l'immagine online o placeholder
        this.productImageUrl = product.image || 'assets/placeholder.png';
        this.isImageOffline = false;

        // Opzionalmente, scarica l'immagine per il futuro
        if (product.image && this.isOnline) {
            this.imageOfflineService.downloadAndSaveImage(
                product.image,
                this.catalogId,
                'PRODUCT',
                product.code,
                product.idRootCategory
            );
        }
    }
}
```

## Troubleshooting

### Problemi Comuni

#### 1. Errore "Database schema not updated"
**Causa**: Il database non è stato inizializzato con le nuove colonne v2.
**Soluzione**:
```typescript
await this.syncroV2DbService.updateDatabaseSchema();
```

#### 2. Immagini non vengono scaricate
**Causa**: Problemi di connessione o permessi filesystem.
**Debug**:
```typescript
// Verifica la connessione
const isOnline = await Utils.checkInternet();
console.log('Connessione:', isOnline);

// Verifica i permessi
const imageDirectory = localStorage.getItem('imageDirectory');
console.log('Directory immagini:', imageDirectory);
```

#### 3. Sincronizzazione si blocca su una pagina
**Causa**: Errore nell'API o dati corrotti.
**Soluzione**: Implementa retry logic e logging dettagliato:
```typescript
let retryCount = 0;
const maxRetries = 3;

while (retryCount < maxRetries) {
    try {
        const response = await this.syncroV2Service.syncCategories(catalogId, pageNumber, pageSize);
        break; // Successo, esci dal loop
    } catch (error) {
        retryCount++;
        console.warn(`⚠️ Tentativo ${retryCount}/${maxRetries} fallito:`, error);

        if (retryCount >= maxRetries) {
            throw new Error(`Sincronizzazione fallita dopo ${maxRetries} tentativi`);
        }

        // Attendi prima del prossimo tentativo
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
    }
}
```

#### 4. Performance lente durante la sincronizzazione
**Cause possibili**:
- Dimensione pagina troppo grande
- Troppi download di immagini simultanei
- Database non ottimizzato

**Soluzioni**:
```typescript
// Riduci la dimensione della pagina
const pageSize = 25; // invece di 100

// Limita i download simultanei di immagini
const maxConcurrentDownloads = 3;

// Ottimizza il database
await this.dbService.createIndex('categories', ['idCatalog', 'lastSyncTimestamp']);
```

### Logging e Debug

#### Abilitare il logging dettagliato
```typescript
// Nel servizio
console.log('🔗 SyncroV2Service.syncCategories - URL:', url);
console.log('📊 Progresso sincronizzazione:', progress);
console.log('💾 Salvataggio categorie:', categories.length);
```

#### Monitoraggio delle performance
```typescript
const startTime = Date.now();
// ... operazione di sincronizzazione
const duration = Date.now() - startTime;
console.log(`⏱️ Sincronizzazione completata in ${duration}ms`);
```

## Migrazione da v1 a v2

### Differenze Principali
1. **API**: Nuovi endpoint dedicati invece di endpoint generici
2. **Database**: Nuove colonne e tabelle per metadati
3. **Immagini**: Gestione integrata invece di processo separato
4. **Paginazione**: Automatica invece di manuale

### Processo di Migrazione

#### 1. Aggiornamento Database
```typescript
// Eseguito automaticamente all'avvio
await this.syncroV2DbService.updateDatabaseSchema();
```

#### 2. Migrazione Dati Esistenti
```typescript
// Segna i dati esistenti come non sincronizzati con v2
await this.dbService.execute(`
    UPDATE categories SET syncedWithV2 = 'false' WHERE syncedWithV2 IS NULL
`);
await this.dbService.execute(`
    UPDATE products SET syncedWithV2 = 'false' WHERE syncedWithV2 IS NULL
`);
```

#### 3. Prima Sincronizzazione v2
```typescript
// Esegui una sincronizzazione completa per popolare i nuovi campi
await this.syncroV2Orchestrator.syncComplete(catalogId, pageSize);
```

### Compatibilità
- Il sistema v2 è **retrocompatibile** con i dati esistenti
- I componenti UI esistenti continuano a funzionare
- La migrazione è **trasparente** per l'utente finale

### Rollback (se necessario)
```typescript
// In caso di problemi, è possibile tornare al sistema v1
// rimuovendo le nuove colonne (non consigliato in produzione)
await this.dbService.execute(`ALTER TABLE categories DROP COLUMN syncedWithV2`);
```

---

## Conclusioni

Il Sistema di Sincronizzazione v2 rappresenta un significativo miglioramento in termini di:
- **Performance**: Paginazione e download parallelo
- **Affidabilità**: Gestione errori e recovery automatico
- **Usabilità**: Sincronizzazione trasparente e progressiva
- **Manutenibilità**: Codice modulare e ben documentato

Per ulteriori informazioni o supporto, consultare il team di sviluppo o aprire un issue nel repository del progetto.
