.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        height: 90vh;
        max-height: 600px;
        width: 90vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            .close {
                background-color: transparent;
                justify-content: end;
            }
        }
        .row {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-top: -77px;
            padding: 25px;
            h1 {
                text-transform: uppercase;
                font-weight: bold;
                font-size: 19px;
                color: var(--ion-dat-red);
            }
            .products {
                overflow: auto;
                &::-webkit-scrollbar {
                    height: 7px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: var(--ion-dat-gray);
                    border-radius: 20px;
                }
                table {
                    margin: auto;
                    border: none;
                    border-collapse: collapse;
                    tr {
                        border: 1px solid var(--ion-dat-middle-gray);
                        &:nth-child(odd) td{
                            background-color: var(--ion-dat-white);
                        }
                        &:nth-child(even) td{
                            background-color: var(--ion-dat-gray);
                        }
                        th {
                            background-color: var(--ion-dat-black);
                            font-size: .85em;
                            letter-spacing: .1em;
                            text-transform: uppercase;
                            color: var(--ion-dat-white);
                        }
                        td {
                            &.description {
                                white-space: nowrap;
                            }
                            object {
                                margin: auto;
                                display: flex;
                                align-items: center;
                                height: 30px;
                                width: 30px;
                                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
                            }
                            
                        }
                    }
                }
            }
        }
    }
}

.hidden {
    display: none !important;
}

@media screen and (max-width: 600px) {
    table {
        border: 0;
        thead {
            border: none;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }
        tr {
            border-bottom: 3px solid var(--ion-dat-middle-gray);
            display: block;
            margin-bottom: .625em;
        }

        td {
        border-bottom: 1px solid var(--ion-dat-middle-gray);
        display: block;
        font-size: .8em;
        text-align: right;
        }

    td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
        }
    }
        td:last-child {
        border-bottom: 0;
    }
}

.table-container{
	overflow: scroll;
	margin: 0 auto;
    width: inherit;
	table {
		position: relative;
		border-collapse: collapse;
		table-layout: fixed;
		text-align: center;
		th {
			min-width: 150px;
			position: sticky;
			top: 0;
			z-index: 1;
			white-space: nowrap;
		}
		td{
            width: 100px;
		}
		th:nth-child(1), td:nth-child(1) {
			position: sticky;
			left: 0;
            min-width: 70px;
			z-index: 9999;
		}
		th:nth-child(2), td:nth-child(2) {
			position: sticky;
			left: 70px;
            min-width: 60px;
			z-index: 9999;
		}
		th:nth-child(3), td:nth-child(3) {
			position: sticky;
			left: 130px;
            min-width: 100px;
			z-index: 9999;
		}
        th:nth-child(4), td:nth-child(4) {
			position: sticky;
			left: 230px;
			z-index: 9999;
            min-width: 250px;
            border-right: 1px solid var(--ion-dat-red);
		}
	}
}

.focusProd {
  background-image: url('../../../../../assets/svg/focus.svg');
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: 2px;
}

.z3 {
    background-image: url('../../../../../assets/icon/icona_attenzione.png');
    background-repeat: no-repeat;
    background-position-x: right;
    background-position-y: 2px;
    width: 20px;
    height: 20px;
    position: absolute;
    right: 0;
    bottom: 2px
}

.product-quantity-in-cart, .product-min-quantity 
{
    font-size: 14px;
    width: 100%;
    text-align: center;
    font-weight: bold;
    position: absolute;
    margin-top: -6px;
    text-align: center;
}
.product-quantity-in-cart
{
    color: var(--ion-dat-red);
}

.quantity {
    white-space: nowrap;
    button {
        background: var(--ion-dat-dark-gray);
        width: 25px;
        height: 25px;
        color: var(--ion-dat-white);
        border-radius: 3px;
        padding: 0;
        &:active {
            transform: translateY(5px);
        }
        ion-icon {
            --ionicon-stroke-width: 83px;
        }
        &:first-child{
            margin-right: 10px;
        }
    }
}

svg {
    width: 30px;
    height: 30px;
    margin-left: -4px;
    &.inCart{
        path, ellipse {
            fill: #008EA7 !important;
        }
    }
}

.item-description {
    text-align: left;
    white-space: nowrap;
}