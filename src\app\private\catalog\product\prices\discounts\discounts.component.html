<div class="scroll">
<table>
  <tbody>
    <tr>
      <td rowspan="3" [attr.data-label]="discountList" class="price-class">{{discountList}}</td>
      <td [attr.colspan]="discounts.length + 1" data-label="" class="title">{{productCode | zeroRemover}} - {{productName}}</td>
    </tr>
    <tr>
      <td data-label="">{{"DISCOUNTS.DISCOUNT" | translate}}</td>
      <td data-label="" class="discount" *ngFor="let item of discounts">{{formatPercent(item.discount)}}</td>
    </tr>
    <tr>
      <td data-label="" class="price">{{"DISCOUNTS.PRICE" | translate}}</td>
      <td data-label="0" class="price" *ngFor="let item of discounts">{{formatPrice(item.discount)}}</td>
    </tr>
  </tbody>
</table>
</div>
