import { ChangeDetectionStrategy, Component, Input, SimpleChanges } from '@angular/core';
import { Customer } from 'src/app/service/data/customer';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';  
@Component({
    selector: 'app-general-data',
    templateUrl: './general-data.component.html',
    styleUrls: ['./general-data.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class GeneralDataComponent {
  @Input() customer: Customer = null;

  constructor() { }

}
