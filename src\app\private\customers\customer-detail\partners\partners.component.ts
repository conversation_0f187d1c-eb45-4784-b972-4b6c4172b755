import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Customer } from 'src/app/service/data/customer';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
@Component({
    selector: 'app-partners',
    templateUrl: './partners.component.html',
    styleUrls: ['./partners.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class PartnersComponent {
  @Input() customer: Customer = null;

  constructor() { }

  filterPartners(){
    return this.customer.addresses?.filter(x => x.shippingAddress)
  }
}
