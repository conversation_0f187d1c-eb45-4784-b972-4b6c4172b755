import { Component } from '@angular/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { StartDbService } from 'src/app/service/start-db/start-db.service';
import { NavigationService } from 'src/app/service/navigation/navigation.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { SearchComponent } from '../search/search.component';
import { ProspectTypesComponent } from '../prospect-types/prospect-types.component';
import { CatalogChoiceComponent } from './catalog-choice/catalog-choice.component';
import { addIcons } from 'ionicons';
import { peopleOutline, bookOutline, documentTextOutline } from 'ionicons/icons';

@Component({
    selector: 'app-home',
    templateUrl: './home.component.html',
    styleUrls: ['./home.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      SearchComponent,
      ProspectTypesComponent,
      CatalogChoiceComponent
    ]
})
export class HomeComponent {
  agentName = '';
  cartsCounter = 0;
  isSearchShown: boolean  = false;
  isProspectTypesShown: boolean = false;
  isCatalogChoiseShown: boolean = false;

  constructor(private _dbService: HybridDbService, private _startDb: StartDbService, private _navigationService: NavigationService){
    addIcons({ peopleOutline, bookOutline, documentTextOutline });
  }

  async ngOnInit() {
    this.agentName = localStorage.getItem('agentName');
    this.getAllData();
  }

  showOnTablet() {
    if (window.matchMedia('screen and (max-width: 500px) and (orientation: portrait)').matches ||
        window.matchMedia('screen and (max-width: 850px) and (orientation: landscape)').matches
    ) {
      return false;
    }
    return true;
  }

  goToExtras() {
    console.log('goToExtras');
    this._navigationService.navigateTo('/private/extras');
  }

  async showCatalogChoice() {
    this.isCatalogChoiseShown = true;
  }

  hideCatalogChoice() {
    this.isCatalogChoiseShown = false;
  }

  startCustomerSession() {
    this.hideCatalogChoice();
    this.isSearchShown = true;
  }

  closeCustomerSearch(event) {
    this.isSearchShown = false;
  }

  openProspectTypes() {
    this.hideCatalogChoice();
    this.isProspectTypesShown = true;
  }

  closeProspectTypes() {
    this.isProspectTypesShown = false;
  }

  goToCustomerList(){
    this._navigationService.navigateTo('/private/customers');
  }

  goToCarts() {
    this._navigationService.navigateTo('/private/carts');
  }

  private async getAllData(){
    await this._dbService.getAll(['carts'], ['count(*) as count']).then((data) => {
      console.log(data);
      this.cartsCounter = (data.length === 1 && typeof data[0]['count'] !== "undefined") ? data[0]['count'] : 0;
    });

    // await this._dbService.getAll(['files'], ['name', 'data']).then((data) => {
    //   this.immaginediprova = `data:image/webp;base64,${data[0]['data']}`;
    // });
  }
}

