import { Injectable } from '@angular/core';
import jwt_decode from "jwt-decode";
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root'
})
export class JWTTokenService {
  decodedToken: { [key: string]: any };

  constructor(private locStorage: LocalStorageService) {}

  setToken(token: String) {
    this.locStorage.set("token", token);
  }

  getToken() {
    return this.locStorage.get("token");
  }

  decodeToken() {  
    if(this.getToken() === 'offline')
      this.decodedToken = {
        "username": localStorage.getItem("offline_username"),
        "iss": "datacol",
        "exp": localStorage.getItem("offline_exp")
      }
    else if(this.locStorage.get("token")) {
      this.decodedToken = jwt_decode(this.getToken());
    }
  }

  getDecodeToken() {
    return jwt_decode(this.getToken());
  }

  getUsername() {
    this.decodeToken();
    return this.decodedToken ? this.decodedToken.username : null;
  }

  getIss() {
    this.decodeToken();
    return this.decodedToken ? this.decodedToken.iss : null;
  }

  getExpiryTime() {
    this.decodeToken();
    return this.decodedToken ? this.decodedToken.exp : null;
  }

  isTokenExpired(): boolean {
    const expiryTime: number = +(this.getExpiryTime());
    if (expiryTime) {
      const cond:boolean = ((1000 * expiryTime) - (new Date()).getTime()) < 5000;
      if (cond)
        this.setToken(null);
      return cond
    } else {
      return false;
    }
  }
}
