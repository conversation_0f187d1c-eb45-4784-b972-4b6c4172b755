import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { CustomerCardType, CustomerCard } from 'src/app/service/data/customer-card';
import Utils from 'src/app/shared/utils';

@Component({
    selector: 'app-customer-card',
    templateUrl: './customer-card.component.html',
    styleUrls: ['./customer-card.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ] 
})
export class CustomerCardComponent {
  @Input() customer: CustomerCard = null;
  @Input() selectedCustomerUid: string = null;
  @Output() openDetail = new EventEmitter();

  constructor() { }

  showDetail(uid: string, type: CustomerCardType = 'CARD') {
    this.openDetail.emit({uid, type});
  }

  getDivision(customer: CustomerCard) {
    return !!customer.infoDataList && customer.infoDataList.length > 0 ? Utils.objToJson(customer.infoDataList)[0].division : '-';
  }

}
