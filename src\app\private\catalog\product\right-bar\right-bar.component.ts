import { Component, EventEmitter, Input, Output } from '@angular/core';
import { Share } from '@capacitor/share';
import { select, Store } from '@ngrx/store';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { DocumentService } from 'src/app/service/document/document.service';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addFavorite, removeFavorite } from 'src/app/store/actions/favorites.actions';

@Component({
    selector: 'app-right-bar',
    templateUrl: './right-bar.component.html',
    styleUrls: ['./right-bar.component.scss'],
    standalone: true,
    imports: [
      SafePipe
    ]
})
export class RightBarComponent {
  @Input() catalogCard: CatalogCard = null;
  @Input() customerUid: string = null;
  @Input() isFavorite: boolean = false;
  @Input() isProspect: boolean = false;
  @Input() isNavigatorShown: boolean = false;
  @Output() openInfo= new EventEmitter();
  @Output() showHideNavigatorPanel = new EventEmitter();
  @Output() markFavorite = new EventEmitter();
  isInfoShown: boolean = false;
  @Output() openPrices= new EventEmitter();

  constructor(private _dbService: HybridDbService, private _service: DocumentService, private _store: Store<any>) { }

  showHideNavigator() {
    this.isNavigatorShown = !this.isNavigatorShown;
    this.showHideNavigatorPanel.emit(this.isNavigatorShown);
  }

  info() {
    this.isInfoShown = !this.isInfoShown;
    this.openInfo.emit();
  }

  prices() {
    this.openPrices.emit();
  }

  favorite(event) {
    if(this.isFavorite)
      this.removeFavorite(event);
    else
      this.addFavorite(event);
  }

  private async addFavorite(event){
    event.stopPropagation();
    this.catalogCard = {...this.catalogCard};
    this.catalogCard.isFavorite = true;
    this.catalogCard.type = 'PRODUCT';
    this.isFavorite = true;
    this._dbService.addRecord("favorites", ['idCategory', 'customerUid', 'catalogCard'], [this.catalogCard.id, this.customerUid, JSON.stringify(this.catalogCard) ] ).then(()=> {
      let currentCustomer = null;
      this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
      this._store.dispatch(addFavorite({idCategory: this.catalogCard.id.toString(), customerUid: currentCustomer.uid, catalogCard: this.catalogCard }));
      this.markFavorite.emit(true);
    });
  }

  private async removeFavorite(event){
    event.stopPropagation();
    this.catalogCard = {...this.catalogCard};
    this.catalogCard.isFavorite = false;
    this.isFavorite = false;
    this._dbService.deleteRecord("favorites", [{key:'idCategory', value: `'${this.catalogCard.id}'` }, {key:'customerUid', value: `'${this.customerUid}'`}]).then(()=> {
      let currentCustomer = null;
      this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
      this._store.dispatch(removeFavorite({idCategory: this.catalogCard.id.toString(), customerUid: currentCustomer.uid }));
      this.markFavorite.emit(false);
    });
  }

  async share(event) {
    event.stopPropagation();
    // es: https://eshop.datacol.com/store/datacol/it/EUR/c/1000212
    const url = this._service.getSharableUrl(this.catalogCard.id, 'c');
    await Share.share({
      title: this.catalogCard.name,
      text: this.catalogCard.name,
      url
    });
  }
}
