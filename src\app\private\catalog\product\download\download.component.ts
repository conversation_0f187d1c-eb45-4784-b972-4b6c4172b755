import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IonicModule, LoadingController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Document } from 'src/app/service/data/product';
import { DocumentService } from 'src/app/service/document/document.service';
import Utils from 'src/app/shared/utils';
import { <PERSON>rows<PERSON> } from '@capacitor/browser';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';

@Component({
    selector: 'app-download',
    templateUrl: './download.component.html',
    styleUrls: ['./download.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
    ]
})
export class DownloadComponent implements OnInit {
  @Input() documents: Document[];
  @Input() articleCode: string;
  @Output() closePopover = new EventEmitter();

  constructor(private _translate: TranslateService, private _documentService: DocumentService, private _toastController: ToastController, private _loadingController: LoadingController) { 
    addIcons({ closeOutline });
  }

  ngOnInit() {}

  async openPdf(document: Document) {
    event.stopPropagation();
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();
    if (Utils.isActiveConnection()) {

      if(!!document && !!document.documentType && !!document.documentNumber && !!document.versionNumber){
        this._documentService.getDocumentUrl(this.articleCode, document.documentType, document.documentNumber, document.versionNumber).then(data => {
          if(!!data && !!data['docUrl']){
            Utils.showSnack(this._toastController, this._translate.instant('GENERICS.OPENING_IN_PROGRESS'), 'OK', true).then(async _=>{
              await Browser.open({ url: data['docUrl'] });
              loading.dismiss();
            });
          } else
            Utils.showSnack(this._toastController, this._translate.instant('GENERICS.DOCUMENT_NOT_PRESENT'), this._translate.instant("SETTINGS.DONE"), true);
            loading.dismiss();
        })
      }
      else
      {
        Utils.showSnack(this._toastController, this._translate.instant('GENERICS.DOCUMENT_NOT_PRESENT'), this._translate.instant("SETTINGS.DONE"), true);
        loading.dismiss();
      }
    } else {
      Utils.showSnack(this._toastController,this._translate.instant('GENERICS.ERROR_NO_NETWORK'));
      loading.dismiss();
    }
    return false;
  }

  close() {
    this.closePopover.emit();
  }
}
