<div #title id="title" class="title">
  {{ 'SYNCRO.TITLE' | translate }}
</div>
<div class="container" *ngIf="instantChecked">
  <object *ngIf="!checkInternet || recommendWifi || !lastUpdateDate" type="image/svg+xml" data="../../assets/svg/attention.svg" class="nav-icon">
    <img src="../../assets/svg/attention.svg" chec />
  </object>
  <ng-container *ngIf="!checkInternet || recommendWifi">
    <div class="lb-1" *ngIf="checkInternet && recommendWifi">
      {{ 'GENERICS.WIFI_MESSAGE' | translate }}
    </div>
    <div class="lb-1" *ngIf="!checkInternet">
      {{ 'GENERICS.NO_NETWORK' | translate }}
    </div>
  </ng-container>
  <div class="lb-2" *ngIf="lastUpdateDate">
    {{ 'SYNCRO.LAST_UPDATE' | translate }} <b>{{lastUpdateDate}}</b>
  </div>
  <div class="lb-2" *ngIf="!lastUpdateDate">
    {{ 'SYNCRO.FIRST_UPDATE' | translate }}
  </div>
  <div class="progress-bar-block">
    <div class="progress-bar">
      <div class="progress-bar-fill" [ngStyle]="{'width.%': progress}"></div>
    </div>
    <div class="percent" *ngIf="inProgress">Progress: {{fmtProgress(progress)}} % <div class="lds-ellipsis"><div></div><div></div><div></div><div></div></div></div>
    <ion-button [disabled]="(!instantChecked || !checkInternet || inProgress)" (click)="startSync()">{{buttonText}}</ion-button>
  </div>
</div>

