import { Pipe, PipeTransform } from '@angular/core';
import { ProductInCart } from '../service/data/product-in-cart';
import { Cart } from '../service/data/cart';
import Utils from './utils';

@Pipe({
    name: 'productQuantity',
    standalone: true,
    pure: false
})
export class ProductQuantityPipe implements PipeTransform {
  constructor() { }
  transform(cart: Cart, code: string) {
    if(!!cart && !!cart.products)
    {
      const product = Utils.objToJson(cart.products).find(item => item.idProduct === code) as ProductInCart;
      if(!!product)
        return product.quantity;
      else
        return 0;
    }
    else 
      return 0;
  }
}