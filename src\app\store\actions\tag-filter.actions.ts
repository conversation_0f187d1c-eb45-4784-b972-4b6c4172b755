import { createAction, props } from "@ngrx/store";
import { 
  CustomerTagFilter, 
  FilterConfiguration, 
  TagFilterState 
} from "../../private/model/sector-hierarchy-response";

// Actions per la gestione dello stato del filtro tag
export const setTagFilterState = createAction(
  '[TagFilter] Set Filter State',
  props<{ filterState: TagFilterState }>()
);

export const setActiveCustomerFilter = createAction(
  '[TagFilter] Set Active Customer Filter',
  props<{ customerUid: string; filter: CustomerTagFilter }>()
);

export const updateCurrentFilter = createAction(
  '[TagFilter] Update Current Filter',
  props<{ filter: CustomerTagFilter }>()
);

export const addCustomTag = createAction(
  '[TagFilter] Add Custom Tag',
  props<{ keyTag: string }>()
);

export const removeCustomTag = createAction(
  '[TagFilter] Remove Custom Tag',
  props<{ keyTag: string }>()
);

export const setFilteringEnabled = createAction(
  '[TagFilter] Set Filtering Enabled',
  props<{ enabled: boolean }>()
);

export const loadFilterConfigurations = createAction(
  '[TagFilter] Load Filter Configurations',
  props<{ configurations: FilterConfiguration[] }>()
);

export const saveFilterConfiguration = createAction(
  '[TagFilter] Save Filter Configuration',
  props<{ configuration: FilterConfiguration }>()
);

export const deleteFilterConfiguration = createAction(
  '[TagFilter] Delete Filter Configuration',
  props<{ configurationId: string }>()
);

export const selectFilterConfiguration = createAction(
  '[TagFilter] Select Filter Configuration',
  props<{ configurationId: string }>()
);

export const resetTagFilter = createAction(
  '[TagFilter] Reset Filter'
);

// Actions per la gestione delle categorie filtrate
export const setFilteredCategories = createAction(
  '[TagFilter] Set Filtered Categories',
  props<{ visibleCategoryIds: string[]; hiddenCategoryIds: string[] }>()
);

export const updateCategoryVisibility = createAction(
  '[TagFilter] Update Category Visibility',
  props<{ categoryId: string; isVisible: boolean }>()
);

// Actions per la gestione delle configurazioni predefinite vs personalizzate
export const showConfigurationChoiceDialog = createAction(
  '[TagFilter] Show Configuration Choice Dialog',
  props<{ customerUid: string; hasCustomConfiguration: boolean }>()
);

export const applyDefaultConfiguration = createAction(
  '[TagFilter] Apply Default Configuration',
  props<{ customerUid: string }>()
);

export const applyCustomConfiguration = createAction(
  '[TagFilter] Apply Custom Configuration',
  props<{ customerUid: string; configurationId: string }>()
);
