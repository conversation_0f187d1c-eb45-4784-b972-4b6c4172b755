<div class="container">
  <button (click)="close()" class="btn-close">
    <div class="icon-close"></div>
  </button>
  <div class="sections">
    <div (click)="showSectionDetail('generalData')">
      <object *ngIf="selectedSession === 'generalData'" type="image/svg+xml" data="../../assets/svg/general-data-sel.svg">
        <img src="../../assets/svg/general-data-sel.svg" />
      </object>
      <object *ngIf="selectedSession !== 'generalData'" type="image/svg+xml" data="../../assets/svg/general-data.svg">
        <img src="../../assets/svg/general-data.svg" />
      </object>
      <div class="text">{{ 'CUSTOMER_DETAIL.GENERAL_DATA' | translate }}</div>
    </div>
    <div (click)="showSectionDetail('addresses')">
      <object *ngIf="selectedSession === 'addresses'" type="image/svg+xml" data="../../assets/svg/addresses-sel.svg">
        <img src="../../assets/svg/addresses-sel.svg" />
      </object>
      <object *ngIf="selectedSession !== 'addresses'" type="image/svg+xml" data="../../assets/svg/addresses.svg">
        <img src="../../assets/svg/addresses.svg" />
      </object>
      <div class="text">{{ 'CUSTOMER_DETAIL.ADDRESSES' | translate }}</div>
    </div>
    <div (click)="showSectionDetail('bankingData')">
      <object *ngIf="selectedSession === 'bankingData'" type="image/svg+xml" data="../../assets/svg/banking-data-sel.svg">
        <img src="../../assets/svg/banking-data-sel.svg" />
      </object>
      <object *ngIf="selectedSession !== 'bankingData'" type="image/svg+xml" data="../../assets/svg/banking-data.svg">
        <img src="../../assets/svg/banking-data.svg" />
      </object>
      <div class="text">{{ 'CUSTOMER_DETAIL.BANKING_DATA' | translate }}</div>
    </div>
    <div (click)="showSectionDetail('payement_data')">
      <object *ngIf="selectedSession === 'payement_data'" type="image/svg+xml" data="../../assets/svg/payement-sel.svg">
        <img src="../../assets/svg/payement-sel.svg" />
      </object>
      <object *ngIf="selectedSession !== 'payement_data'" type="image/svg+xml" data="../../assets/svg/payement.svg">
        <img src="../../assets/svg/payement.svg" />
      </object>
      <div class="text">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA' | translate }}</div>
    </div>
    <div (click)="showSectionDetail('partners')">
      <object *ngIf="selectedSession === 'partners'" type="image/svg+xml" data="../../assets/svg/partners-sel.svg">
        <img src="../../assets/svg/partners-sel.svg" />
      </object>
      <object *ngIf="selectedSession !== 'partners'" type="image/svg+xml" data="../../assets/svg/partners.svg">
        <img src="../../assets/svg/partners.svg" />
      </object>
      <div class="text">{{ 'CUSTOMER_DETAIL.PARTNERS' | translate }}</div>
    </div>
    <!-- <div (click)="showSectionDetail('maps')">
      {{ 'CUSTOMER_DETAIL.MAPS' | translate }}
    </div> -->
  </div>
  <div class="section-detail">
    <div *ngIf="selectedSession == 'generalData'">
      <app-general-data *ngIf="customerData" [customer]="customerData"></app-general-data>
    </div>
    <div *ngIf="selectedSession == 'bankingData'">
      <app-banking-data *ngIf="customerData" [customer]="customerData"></app-banking-data>
    </div>
    <div *ngIf="selectedSession == 'addresses'">
      <app-addresses *ngIf="customerData" [customer]="customerData"></app-addresses>
    </div>
    <div *ngIf="selectedSession == 'payement_data'">
      <app-payement-data *ngIf="customerData" [customer]="customerData"></app-payement-data>
    </div>
    <div *ngIf="selectedSession == 'partners'">
      <app-partners *ngIf="customerData" [customer]="customerData"></app-partners>
    </div>
    <!-- <div *ngIf="selectedSession == 'maps'">
      <app-maps *ngIf="customerData" [customer]="customerData"></app-maps>
    </div> -->
  </div>
  <ion-button (click)="startCustomerSession()" class="tb-btn"  >
    <div>
      {{ 'CUSTOMER_DETAIL.START_CUSTOMER_SESSION' | translate }}
    </div>
    <ion-icon name="caret-forward-outline"></ion-icon>
  </ion-button>
</div>