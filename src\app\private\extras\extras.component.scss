.title {
    position: absolute;
    top: calc(0px + var(--ion-safe-area-top, 0));
    height: 44px;
    align-items: center;
    display: flex;
    color: white;
    z-index: 9999;
    text-transform: uppercase;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100vw - 280px);
    justify-content: flex-end;
}
H1 {
    text-transform: capitalize;
    border-bottom: 2px solid var(--ion-dat-middle-gray);
    padding-bottom: 10px;
    width: 100%;
}
.container {
    width: 100%;
    max-height: calc(100vh - 230px);
    overflow-y: auto;
    margin-bottom: 90px;
}

ion-content {
    height: 100%;
}