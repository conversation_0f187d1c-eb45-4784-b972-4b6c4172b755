import { Address } from "./address";

export interface InfoDataList {
    distributionChannel: string;
    division: string;
    salesOrganization: string; 
    userPriceGroupCode: string;
}

export class Customer {
    name: string = null;
    uid: string = null;
    abiCode: string = null;
    active: string = null;
    additionalExemption: string = null;
    addresses: Address[] = null;
    agent: string = null;
    ams: string = null;
    atecoCode: string = null;
    bankBranch: string = null;
    bankCode: string = null;
    bankName: string = null;
    cabCode: string = null;
    catalog: string = null;
    catalogFromStore: string = null;
    catalogName: string = null;
    classificationKey: string = null;
    codiceAttivita: string = null;
    codiceProfessione: string = null;
    currency: string = null;
    customerClass: string = null;
    customerStatus: string = null;
    description2: string = null;
    fiscalCode: string = null;
    ibanCode: string = null;
    iconStatus: string = null;
    iconterms: string = null;
    industrialSector: string = null;
    invoiceCalendar: string = null;
    payment: string = null;
    reaCode: string = null;
    reasonStatus: string = null;
    ribaExemption: string = null;
    riskClass: string = null;
    sam: string = null;
    selectable: string = null;
    selected: string = null;
    shipment: string = null;
    srm: string = null;
    userPriceGroupCode: string = null;
    vatCode: string = null;
    infoDataList: InfoDataList[] = null;
    cancellationInstant: string = null;
}

