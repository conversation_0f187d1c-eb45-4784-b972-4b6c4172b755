export class FeatureValue {
    value: string = null;
}

export class Feature {
    name: string = null;
    featureValues: FeatureValue[] = null;
}

export class Classification {
    code: string = null;
    name: string = null;
    features: Feature[] = null;
}

export class Document {
    catalogVersion: string = null;
    code: string = null;
    documentName: string = null;
    documentNumber: string = null;
    documentType: string = null;
    versionNumber: string = null;
}

export class Media {
    code: string = null;
    image: string = null;
    imageUrl: string = null;
    position: string = null;
}

export class PriceRow {
    conditionType: string = null;
    currencyIso: string = null;
    distributionChannel: string = null;
    division: string = null;
    customerPriceList: string = null;
    b2bUnitCode: string = null;
    formattedValue: string = null;
    priceList: string = null;
    salesOrganization: string = null;
    unitFactor: string = null;
    value: string = null;
    startTime: string = null;
    endTime: string = null;
    gp: string = null;
}

export class ProductReferences {
    referenceType: string = null;
    target: Target = null;
}

export class Target {
    code: string = null;
}

export class Image {
  altText: string = null;
	format: string = null;
	imageType: string = null;
	galleryIndex: string = null;
	positionMc: string = null;
  shortCaption: string = null;
	url: string = null;
	mediaTypology: string = null;
}

export class Product {
    code: string = null;
    description: string = null;
    idSubCategory: string = null;
    image: string = null;
    classifications: Classification[] | null = null;
    documents: Document[] = null;
    media: Media[] = null;
    mediaVideoURL: string = null;
    minimumDeliveryQuantity: string = null;
    name: string = null;
    priceRows: PriceRow[] = null;
    productReferences: ProductReferences[] = null;
    professionJobProduct: string = null;
    thumbnail: string = null;
    creationInstant: string = null;
    modifiedInstant: string = null;
    cancellationInstant: string = null;
    focus: string = null;
    images: Image[] = null;
    idCategory: string = null;
    divisionStatusCode: string = null;
}

export type ProductSearchItem = Pick<Product, 'idSubCategory' | 'code' | 'image' | 'name' | 'minimumDeliveryQuantity'>;
