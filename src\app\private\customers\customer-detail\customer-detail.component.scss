.container {
    padding: 10px;
    height: 100%;
    .sections {
        display: flex;
        div {
            flex: 1; 
            justify-content: center;
            flex-direction: column;
            white-space: nowrap;
            text-align: center;
            padding: 4px;
            object {
                width: 4em;
                height: 4em;
                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
            }
            .text {
                padding-top: 5px;
                text-transform: capitalize;
                font-size: 14px;
            }
        }
    }
    .section-detail {
        padding-top: 25px;
    }
    .btn-close {
        background: url('../../../../assets/svg/close.svg');
        width: 1em;
        height: 1em;
        background-size: contain;
        background-repeat: no-repeat;
        button {
            background: transparent !important;
        }
    }
    ion-button {
        width: calc(100% - 20px);
        height: 2em;
        margin-top: 26px;
        position: absolute;
        bottom: 10px;
    }
}

.tb-btn {
    transform: scale(1);
	animation: pulse 2s infinite;
    div {
        padding-left: 15px;
    }
    ion-icon {
        right: 0;
        position: absolute;
    }
}
@keyframes pulse {
	0% {
		transform: scale(0.95);
	}

	70% {
		transform: scale(1);
	}

	100% {
		transform: scale(0.95);
	}
}

/* TABLET orizzontale */
@media screen and (max-height: 576px) and (orientation: landscape) {
    object {
        width: 2em !important;
        height: 2em !important;
        pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
    }
}