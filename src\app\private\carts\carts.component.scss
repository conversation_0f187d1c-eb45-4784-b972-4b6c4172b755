@import '_common.scss';

ion-list {
    overflow-y: scroll;
    max-height: calc(100vh - 356px);
    .row {
        .cart-number{
            width: 50px;
            text-align: center;
        }
        .cart{
            width: 60px;
            font-size: 40px;
            margin-top: 5px;
        }
        .order-row-number {
            width: 50px;
            text-align: center;
            color: var(--ion-dat-red);
        }
        .customer{
            padding-left: 25px;
            font-weight: bold;
            width: 100%;
        }
        .item-sliding-animation {
            -webkit-animation: slidingAnimation;
            animation: slidingAnimation;
            -webkit-animation-duration: 1000ms;
            animation-duration: 1000ms;
            animation-iteration-count: infinite;
        }
    }
}
