import { Injectable, inject } from '@angular/core';
import { Platform } from '@ionic/angular';
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem';
import { Capacitor } from '@capacitor/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';

/**
 * Interfaccia per i metadati delle immagini salvate offline
 */
export interface ImageMetadata {
  id?: number;
  imageUrl: string;
  localPath: string;
  fileName: string;
  idCatalog: number;
  idRootCategory?: string;
  entityType: 'PRODUCT' | 'CATEGORY';
  entityId: string;
  downloadTimestamp: number;
  fileSize?: number;
  mimeType?: string;
  isAvailable: boolean;
}

/**
 * Servizio per la gestione delle immagini offline
 * Gestisce il download, salvataggio e recupero delle immagini per l'uso offline
 */
@Injectable({
  providedIn: 'root'
})
export class ImageOfflineService extends BasicService {

  private _dbService = inject(HybridDbService);
  private readonly TABLE_NAME = 'offline_images';

  constructor() {
    super();
  }

  /**
   * Inizializza il database per le immagini offline
   */
  async initializeImageDatabase(): Promise<void> {
    try {
      // Crea la tabella per i metadati delle immagini se non esiste
      await this._dbService.createTable(this.TABLE_NAME, [
        { columnName: 'id', columnType: 'INTEGER PRIMARY KEY AUTOINCREMENT' },
        { columnName: 'imageUrl', columnType: 'TEXT NOT NULL' },
        { columnName: 'localPath', columnType: 'TEXT NOT NULL' },
        { columnName: 'fileName', columnType: 'TEXT NOT NULL' },
        { columnName: 'idCatalog', columnType: 'INTEGER NOT NULL' },
        { columnName: 'idRootCategory', columnType: 'TEXT' },
        { columnName: 'entityType', columnType: 'TEXT NOT NULL' },
        { columnName: 'entityId', columnType: 'TEXT NOT NULL' },
        { columnName: 'downloadTimestamp', columnType: 'INTEGER NOT NULL' },
        { columnName: 'fileSize', columnType: 'INTEGER' },
        { columnName: 'mimeType', columnType: 'TEXT' },
        { columnName: 'isAvailable', columnType: 'INTEGER DEFAULT 1' }
      ], false);

      // Crea indici per migliorare le performance
      await this._dbService.createIndex('offline_images', ['imageUrl']);
      await this._dbService.createIndex('offline_images', ['entityType', 'entityId']);
      await this._dbService.createIndex('offline_images', ['idCatalog']);

      console.log('✅ Database immagini offline inizializzato con successo');
    } catch (error) {
      console.error('❌ Errore durante l\'inizializzazione del database immagini:', error);
      throw error;
    }
  }

  /**
   * Scarica e salva un'immagine per l'uso offline
   * @param imageUrl URL dell'immagine da scaricare
   * @param idCatalog ID del catalogo
   * @param entityType Tipo di entità (PRODUCT o CATEGORY)
   * @param entityId ID dell'entità
   * @param idRootCategory ID della categoria radice (opzionale)
   * @returns Promise<ImageMetadata | null>
   */
  async downloadAndSaveImage(
    imageUrl: string,
    idCatalog: number,
    entityType: 'PRODUCT' | 'CATEGORY',
    entityId: string,
    idRootCategory?: string
  ): Promise<ImageMetadata | null> {
    try {
      if (!imageUrl || imageUrl === 'null') {
        console.warn('⚠️ URL immagine non valido:', imageUrl);
        return null;
      }

      // Verifica se l'immagine è già stata scaricata
      const existingImage = await this.getImageMetadata(imageUrl);
      if (existingImage && existingImage.isAvailable) {
        console.log('📷 Immagine già disponibile offline:', imageUrl);
        return existingImage;
      }

      await this._platform.ready();
      if (!this._platform.is('capacitor')) {
        console.warn('⚠️ Download immagini supportato solo su dispositivi mobili');
        return null;
      }

      // Genera un nome file unico
      const fileName = this.generateFileName(imageUrl, entityType, entityId);
      const localPath = `images/${fileName}`;

      // Scarica l'immagine
      console.log('📥 Scaricamento immagine:', imageUrl);
      const response = await this.basicGetMedia(`/multimedia/get/image/${idCatalog}/${idRootCategory || ''}/${this.extractImageName(imageUrl)}`, false);

      if (response === 'OK') {
        // Salva i metadati nel database
        const metadata: ImageMetadata = {
          imageUrl,
          localPath,
          fileName,
          idCatalog,
          idRootCategory,
          entityType,
          entityId,
          downloadTimestamp: Date.now(),
          isAvailable: true
        };

        await this.saveImageMetadata(metadata);
        console.log('✅ Immagine salvata offline:', fileName);
        return metadata;
      } else {
        console.error('❌ Errore durante il download dell\'immagine:', imageUrl);
        return null;
      }
    } catch (error) {
      console.error('❌ Errore durante il salvataggio dell\'immagine:', error);
      return null;
    }
  }

  /**
   * Ottiene i metadati di un'immagine dal database
   * @param imageUrl URL dell'immagine
   * @returns Promise<ImageMetadata | null>
   */
  async getImageMetadata(imageUrl: string): Promise<ImageMetadata | null> {
    try {
      const result = await this._dbService.getColumnFromTableByANDCondition(
        'offline_images',
        ['*'],
        [{ key: 'imageUrl', value: imageUrl }]
      );

      if (result && result.length > 0) {
        const row = result[0];
        return {
          id: row.id,
          imageUrl: row.imageUrl,
          localPath: row.localPath,
          fileName: row.fileName,
          idCatalog: parseInt(row.idCatalog),
          idRootCategory: row.idRootCategory,
          entityType: row.entityType as 'PRODUCT' | 'CATEGORY',
          entityId: row.entityId,
          downloadTimestamp: parseInt(row.downloadTimestamp),
          fileSize: row.fileSize ? parseInt(row.fileSize) : undefined,
          mimeType: row.mimeType,
          isAvailable: row.isAvailable === 1
        };
      }

      return null;
    } catch (error) {
      console.error('❌ Errore durante il recupero dei metadati immagine:', error);
      return null;
    }
  }

  /**
   * Salva i metadati di un'immagine nel database
   * @param metadata Metadati dell'immagine
   */
  private async saveImageMetadata(metadata: ImageMetadata): Promise<void> {
    const columns = [
      'imageUrl', 'localPath', 'fileName', 'idCatalog', 'idRootCategory',
      'entityType', 'entityId', 'downloadTimestamp', 'fileSize', 'mimeType', 'isAvailable'
    ];
    const values = [
      metadata.imageUrl,
      metadata.localPath,
      metadata.fileName,
      metadata.idCatalog.toString(),
      metadata.idRootCategory || '',
      metadata.entityType,
      metadata.entityId,
      metadata.downloadTimestamp.toString(),
      metadata.fileSize?.toString() || '',
      metadata.mimeType || '',
      metadata.isAvailable ? '1' : '0'
    ];

    await this._dbService.addRecord('offline_images', columns, values);
  }

  /**
   * Genera un nome file unico per l'immagine
   * @param imageUrl URL originale dell'immagine
   * @param entityType Tipo di entità
   * @param entityId ID dell'entità
   * @returns Nome file generato
   */
  private generateFileName(imageUrl: string, entityType: string, entityId: string): string {
    const timestamp = Date.now();
    const extension = this.extractFileExtension(imageUrl) || 'webp';
    return `${entityType.toLowerCase()}_${entityId}_${timestamp}.${extension}`;
  }

  /**
   * Estrae il nome dell'immagine dall'URL
   * @param imageUrl URL dell'immagine
   * @returns Nome dell'immagine
   */
  private extractImageName(imageUrl: string): string {
    return imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
  }

  /**
   * Estrae l'estensione del file dall'URL
   * @param imageUrl URL dell'immagine
   * @returns Estensione del file
   */
  private extractFileExtension(imageUrl: string): string {
    const fileName = this.extractImageName(imageUrl);
    const lastDot = fileName.lastIndexOf('.');
    return lastDot > 0 ? fileName.substring(lastDot + 1) : '';
  }

  /**
   * Risolve l'URL dell'immagine per l'uso offline
   * @param imageUrl URL originale dell'immagine
   * @returns Promise<string | null> URL locale dell'immagine o null se non disponibile
   */
  async resolveOfflineImageUrl(imageUrl: string): Promise<string | null> {
    try {
      if (!imageUrl || imageUrl === 'null') {
        return null;
      }

      const metadata = await this.getImageMetadata(imageUrl);
      if (metadata && metadata.isAvailable) {
        await this._platform.ready();
        if (this._platform.is('capacitor')) {
          const imageDirectory = localStorage.getItem('imageDirectory');
          if (imageDirectory) {
            return `${imageDirectory}${metadata.fileName}`;
          }
        }
      }

      return null;
    } catch (error) {
      console.error('❌ Errore durante la risoluzione dell\'URL immagine offline:', error);
      return null;
    }
  }

  /**
   * Pulisce le immagini offline obsolete
   * @param olderThanTimestamp Timestamp prima del quale eliminare le immagini
   * @param idCatalog ID del catalogo (opzionale, per filtrare per catalogo)
   */
  async cleanupOldImages(olderThanTimestamp: number, idCatalog?: number): Promise<void> {
    try {
      console.log(`🧹 Pulizia immagini offline più vecchie di ${new Date(olderThanTimestamp).toISOString()}`);

      // Ottieni tutte le immagini e filtra manualmente (per ora)
      const allImages = await this._dbService.getAll([this.TABLE_NAME]);

      const imagesToCleanup = allImages.filter(image => {
        const isOld = parseInt(image.downloadTimestamp) < olderThanTimestamp;
        const matchesCatalog = !idCatalog || parseInt(image.idCatalog) === idCatalog;
        return isOld && matchesCatalog;
      });

      console.log(`🗑️ Trovate ${imagesToCleanup.length} immagini da pulire`);

      // Per ora, segna solo come non disponibili
      for (const image of imagesToCleanup) {
        await this._dbService.updateRecord(
          this.TABLE_NAME,
          [{ key: 'id', value: image.id }],
          [{ key: 'isAvailable', value: 0 }]
        );
      }

      console.log(`✅ Pulizia completata: ${imagesToCleanup.length} immagini marcate come non disponibili`);
    } catch (error) {
      console.error('❌ Errore durante la pulizia delle immagini offline:', error);
    }
  }
}
