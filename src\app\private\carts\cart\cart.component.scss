@import '../_common.scss';

/* .container {
    transition: transform 0.5s ease;
} */

ion-list {
    max-width: 850px !important;
    background: transparent;
    overflow-y: scroll;
    max-height: calc(100vh - 356px);
    .row {
        background-color: white;
        .cart-number{
            width: 60px;
            text-align: center;
        }
        .product-image {
            width: 130px;
            margin-top: 5px;
        }
        .article {
            width: 250px;
            text-align: left;
            padding-left: 10px;
        }
        .description{
            width: 60%;
            padding-left: 10px;
        }
        .quantity {
            width: 72px;
            text-align: center;
            padding-right: 5px;
            white-space: nowrap;
            object {
                width: 40px;
                height: 40px;
                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
            }
            .product-quantity-in-cart
            {
                font-size: 14px;
                width: 30px;
                text-align: center;
                font-weight: bold;
                position: absolute;
                margin-top: -6px;
                margin-left: 6px;
                color: var(--ion-dat-red);
                white-space: nowrap;
            }
        }
        .multiplier {
            width: 62px;
            height: 40px;
            margin-top: 5px;
            text-align: right;
            border: 1px solid gray;
            border-radius: 5px;
            padding: 8px;
        }
    }
}

.none {
    display: inline-flex;
    width: 680px;
    margin-left: 45px;
    .item-btn{
        width: 40px;
        height: 40px;
        margin-right: 20px;
        --padding-start: 0;
        --padding-end: 0;
        --border-radius: 5px;
        --background: var(--ion-dat-black);
    }
}

.toolbar {
    max-width: 850px !important;
    background: transparent;
    overflow-y: scroll;
    max-height: calc(100vh - 356px);
}

.button-bar ion-button {
    margin-bottom: 10px;
    margin-right: 0;
    margin-top: 30px;
}

app-search {
    position: absolute;
    margin-top: -40px;
    display: block;
    width: 500px;

    ion-list {
        margin-top: -10px !important;
    }
}