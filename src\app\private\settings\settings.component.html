<div #title id="title" class="title">
  {{ 'SETTINGS.TITLE' | translate }}
</div>
<div class="container">
  <div class="settings">
    <div class="navigation-choose">
      <ion-list>
        <ion-radio-group [(ngModel)]="navigationType" value="paginated">
          <ion-list-header>
            <ion-label>{{ 'SETTINGS.CATALOG_NAVIGATION' | translate }}</ion-label>
          </ion-list-header>
          <ion-item>
            <object type="image/svg+xml" data="../../assets/svg/infinite.svg" class="nav-icon">
              <img src="../../assets/svg/infinite.svg" chec />
            </object>
            <ion-label>{{ 'SETTINGS.INFINITE_SCROLL' | translate }}</ion-label>
            <ion-radio slot="end" value="infinite"></ion-radio>
          </ion-item>
          <ion-item>
            <object type="image/svg+xml" data="../../assets/svg/products.svg" class="nav-icon">
              <img src="../../assets/svg/products.svg" />
            </object>
            <ion-label>{{ 'SETTINGS.PRODUCTS' | translate }}</ion-label>
            <ion-radio slot="end" value="paginated"></ion-radio>
          </ion-item>
        </ion-radio-group>
      </ion-list>
      <ion-button [disabled]="buttonDisabled" (click)="doConfirm()">{{ 'SETTINGS.CONFIRM' | translate }}</ion-button>
      <ion-button [disabled]="buttonDisabled" (click)="doReset()">{{ 'SETTINGS.RESET_CACHE' | translate }}</ion-button>
      {{ 'SETTINGS.RESET_CACHE_DESCRIPTION' | translate }}
    </div>
  </div>
  <div class="footer">
    {{ 'SETTINGS.APP_VERSION' | translate }} {{appVersion}}
  </div>
</div>