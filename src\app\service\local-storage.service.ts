import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {
  private storageSub= new Subject<{key:string, value: string}>();

  set(key: string, value: String) {
    if(value !== null)
    {
      localStorage.setItem(key, value.toString());
    }
    else
      localStorage.removeItem(key);
  }

  get(key: string) {
    return localStorage.getItem(key);
  }

  remove(key: string) {
    localStorage.removeItem(key);
    this.storageSub.next({key:key, value: null});
  }

  watchStorage(): Observable<any> {
    return this.storageSub.asObservable();
  }
}
