import { Component, EventEmitter, inject, Input, OnInit, Output, ViewChild } from '@angular/core';
import { CatalogCard } from 'src/app/service/data/catalog-card';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { Platform } from '@ionic/angular';
import { DocumentService } from 'src/app/service/document/document.service';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { select, Store } from '@ngrx/store';
import { addFavorite, removeFavorite } from 'src/app/store/actions/favorites.actions';
import { Subcategory } from 'src/app/service/data/subcategory';
import Utils from 'src/app/shared/utils';
import { Share } from '@capacitor/share';
import { CardType, CatalogService } from 'src/app/service/catalog/catalog.service';

export interface Price {
  id: string,
  name: string,
}
@Component({
    selector: 'app-catalog-card',
    template: '',
    styles: [],
    standalone: true
})
export class CatalogCardComponent implements OnInit {

  protected catalogService = inject(CatalogService);

  @Input() item: CatalogCard = null;
  @Output() openCategory = new EventEmitter();
  @Output() openPrices = new EventEmitter<Price>();
  @Output() refreshView = new EventEmitter();
  @Output() openSubcategoriesPreview = new EventEmitter();
  imageDirectory: string = localStorage.getItem('imageDirectory');
  private customerUid: string = null;
  private longPressEnabled: boolean = false;
  resolvedImageUrl: string | null = null;
  card: CatalogCard | null = null;
  navigationType: 'P' | 'C' | 'I' = 'P';
  productsCount = '0';
  private cardView: CardType = CardType.SMALL;
  private _platform = inject(Platform);
  private _dbService = inject(HybridDbService);
  private _service = inject(DocumentService);
  private _lsService = inject(LocalStorageService);
  private _store = inject(Store<any>);
  
  constructor() {}

  ngOnInit(){
    this.navigationType = (this._lsService.get('catalog_navigation_type') || 'P') as 'P' | 'C' | 'I';
    this.card = {...this.item};
    this.cardView = (!!localStorage.getItem('cardViewType') ? localStorage.getItem('cardViewType') as CardType : CardType.SMALL);
    if(!!this.card.idRootCategory && Utils.objToJson(this.card.isProduct))
    {
      let subcategories = [];
      this._store.pipe(select('subcategories')).subscribe(res => subcategories = res).unsubscribe();
      const filtered = subcategories.filter((item:Subcategory) => !!item.idrootcategory && item.idrootcategory.toString() === this.card.idRootCategory.toString() && item.idsubcategory.toString() === this.card.id.toString());
      if(filtered.length >0) {
        this.card.image = filtered[0].image;

        if(this.cardView === CardType.LARGE)
          this.productsCount = filtered[0].productscount;
      } else
        this.card.image = null; // devo prendere quella che ho scaricato per la datacol category, non il parametro in DAT_Categories
    }
    this.getImageUrl();

    this.customerUid = this.catalogService.userUid;
    this.longPressEnabled = this.catalogService.longPressEnabled;
  }

  showPreviewSubcategories() {
    this.openSubcategoriesPreview.emit(this.card);
  }

  showActions: boolean = false;

  showInfiniteCardActions() {
    this.showActions = !this.longPressEnabled;
  }

  open(idRootCategory: string, idCategory: string, isProduct: string) {

    
    if(this.card.isProduct != true) {
      if(this.showActions) {
        this.showActions = false;
        return;
      }
    }
    this.openCategory.emit({idRootCategory, idCategory, isProduct});
  }

  @ViewChild('popover') popover;
  isPopoverOpen = false;

  presentPopover(e: Event) {
    e.stopPropagation();
    this.popover.event = e;
    this.isPopoverOpen = true;
  }

  cardShowPrices(event) {
    this.isPopoverOpen = false;
    this.showActions = false;
    event.stopPropagation();
    const price: Price = {
      id: this.card.id,
      name: this.card.name
    };
    this.openPrices.emit(price);
  }

  async cardAddFavorite(event){
    this.isPopoverOpen = false;
    this.showActions = false;
    event.stopPropagation();
    this.card = {...this.card};
    this.card.isFavorite = true;
    this._dbService.addRecord("favorites", ['idCategory', 'customerUid', 'catalogCard'], [this.card.id, this.customerUid, JSON.stringify(this.card)] ).then(()=> {
      let currentCustomer = null;
      this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
      this._store.dispatch(addFavorite({idCategory: this.card.id.toString(), customerUid: currentCustomer.uid, catalogCard: this.card }));
      this.refreshView.emit();
    });
  }

  async cardRemoveFavorite(event){
    this.isPopoverOpen = false;
    this.showActions = false;
    event.stopPropagation();
    this.card = {...this.card};
    this.card.isFavorite = false;
    this._dbService.deleteRecord("favorites", [{key:'idCategory', value: `'${this.card.id}'` }, {key:'customerUid', value: `'${this.customerUid}'`}]).then(()=> {
      let currentCustomer = null;
      this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
      this._store.dispatch(removeFavorite({idCategory: this.card.id.toString(), customerUid: currentCustomer.uid }));
      this.refreshView.emit();
    });
  }

  async share(event) {
    this.isPopoverOpen = false;
    this.showActions = false;
    event.stopPropagation();
    // es: https://eshop.datacol.com/store/datacol/it/EUR/Food/c/1000212
    const url = this._service.getSharableUrl(this.card.id, 'c');
    Share.share({
      title: this.card.name,
      text: this.card.name,
      url: url
    });
  }

  getImageUrl(){
    this.resolvedImageUrl = null;
    if(!!this.card && !!this.card.image)
    {
      if (this._platform.is('capacitor')) {
        const fileName = this.card.image.substring(this.card.image.lastIndexOf('/')+1).replace('jpg', 'webp');
        this.resolvedImageUrl = this.imageDirectory + fileName;
      }
    }
  }
}
