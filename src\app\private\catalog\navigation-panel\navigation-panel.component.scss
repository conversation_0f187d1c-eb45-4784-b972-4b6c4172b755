@mixin level($level) {
  .level-#{$level} {
    padding-left: calc($level * 33px) !important;
  }
}

.navigator {
  width: 50%;
  height: calc(100% - 88px);
  background-color: var(--ion-dat-popover);
  position: absolute;
  right: 65px;
  top: 43px;
  z-index: 9999;
  padding: 25px;
  overflow-y: scroll;
  .close {
    padding: 0;
    width: 27px;
    height: 27px;
    margin-bottom: 33px;
    ion-icon {
      color: var(--ion-dat-red);
      width: 27px;
      height: 27px;
    }
  }
  div.header {
    position: fixed;
    right: 95px;
  }

  div.level-0 {
    margin-top: 50px;
  }

  div.level-0 ~ div.level-0 {
    margin-top: 0px;
  }

  div[class^='level'] {
    font-size: large;
    color: var(--ion-dat-white);
    line-height: 40px;
    border-bottom: 1px solid var(--ion-dat-dark-gray);
    display: flex;
    align-items: center;
    display: none;
    .icon-expand-collapse {
      margin-right: 10px;
      background-size: contain;
      background-repeat: no-repeat;
      background-image: url('../../../../assets/svg/expand.svg');
      display: inherit;
      width: 25px;
      height: 25px;
      div {
        display: none;
      }
      &.collapse {
        background-image: url('../../../../assets/svg/collapse.svg');
      }
    }
    &.visible {
      display: flex;
      div {
        display: initial;
      }
      .icon-expand-collapse {
        display: var(--expandible) !important;
      }
    }
    &.selected {
      color: var(--ion-dat-yellow);
      font-weight: bold;
    }
  }
  @include level(0);
  @include level(1);
  @include level(2);
  @include level(3);
  .level-0 {
    text-transform: uppercase;
    font-weight: bold;
  }
  .level-1 {
    font-weight: bold;
  }
  .icon-filler {
    width: 41px;
    display: none !important;
    &.visible {
      display: inherit;
    }
  }
}

div[class^='level']:active {
  &.visible {
    color: var(--ion-dat-yellow);
    font-weight: bold;
  }
}


@media screen and (orientation: portrait) {
  .navigator {
    width: calc(100% - 130px);
  }
}
