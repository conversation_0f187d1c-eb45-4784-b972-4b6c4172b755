import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { SyncroV2Service } from './syncro-v2.service';
import { BasicResponse } from '../data/basic-response';
import { environment } from 'src/environments/environment';

describe('SyncroV2Service', () => {
  let service: SyncroV2Service;
  let httpMock: HttpTestingController;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [SyncroV2Service]
    });
    service = TestBed.inject(SyncroV2Service);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('syncCategories', () => {
    it('should sync categories successfully', async () => {
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [
          {
            id: '1',
            name: 'Test Category',
            description: 'Test Description',
            idCatalog: 1,
            isRootCategory: true
          }
        ],
        totalPages: 1,
        totalCount: 1
      };

      const promise = service.syncCategories(1, 0, 10);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncCategories?idCatalog=1&pageIn=0&pageSize=10`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await promise;
      expect(result.status).toBe('OK');
      expect(result.content.length).toBe(1);
      expect(result.content[0].name).toBe('Test Category');
    });

    it('should handle API errors', async () => {
      const promise = service.syncCategories(1, 0, 10);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncCategories?idCatalog=1&pageIn=0&pageSize=10`);
      req.error(new ErrorEvent('Network error'));

      try {
        await promise;
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('syncProducts', () => {
    it('should sync products successfully', async () => {
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [
          {
            code: 'PROD001',
            name: 'Test Product',
            description: 'Test Product Description',
            image: 'test-image.jpg',
            idSubCategory: 'CAT001'
          }
        ],
        totalPages: 1,
        totalCount: 1
      };

      const promise = service.syncProducts(1, 0, 100);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=100`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await promise;
      expect(result.status).toBe('OK');
      expect(result.content.length).toBe(1);
      expect(result.content[0].code).toBe('PROD001');
    });

    it('should sync products with root category filter', async () => {
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [],
        totalPages: 0,
        totalCount: 0
      };

      const promise = service.syncProducts(1, 0, 100, 1002196);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=100&idRootCategory=1002196`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await promise;
      expect(result.status).toBe('OK');
    });
  });

  describe('syncDeletedCategories', () => {
    it('should sync deleted categories successfully', async () => {
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [
          {
            id: '1',
            cancellationInstant: '2024-01-01T00:00:00Z'
          }
        ],
        totalPages: 1,
        totalCount: 1
      };

      const promise = service.syncDeletedCategories(1, 0, 10);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=1&pageIn=0&pageSize=10`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await promise;
      expect(result.status).toBe('OK');
      expect(result.content.length).toBe(1);
    });

    it('should sync deleted categories with timestamp', async () => {
      const timestamp = 1005420790000;
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [],
        totalPages: 0,
        totalCount: 0
      };

      const promise = service.syncDeletedCategories(1, 0, 10, timestamp);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=1&pageIn=0&pageSize=10&fromTimestamp=${timestamp}`);
      expect(req.request.method).toBe('GET');
      req.flush(mockResponse);

      const result = await promise;
      expect(result.status).toBe('OK');
    });
  });

  describe('syncAllProducts', () => {
    it('should sync all products with pagination', async () => {
      const mockPage1: BasicResponse = {
        status: 'OK',
        content: [
          { code: 'PROD001', name: 'Product 1' },
          { code: 'PROD002', name: 'Product 2' }
        ],
        totalPages: 2,
        totalCount: 3
      };

      const mockPage2: BasicResponse = {
        status: 'OK',
        content: [
          { code: 'PROD003', name: 'Product 3' }
        ],
        totalPages: 2,
        totalCount: 3
      };

      let callbackCount = 0;
      const onPageCallback = jasmine.createSpy('onPageCallback').and.callFake((pageData, pageNumber) => {
        callbackCount++;
        expect(pageData.status).toBe('OK');
        expect(pageNumber).toBe(callbackCount - 1);
      });

      const promise = service.syncAllProducts(1, 2, undefined, onPageCallback);

      // First page request
      const req1 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=2`);
      req1.flush(mockPage1);

      // Second page request
      const req2 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=1&pageSize=2`);
      req2.flush(mockPage2);

      const results = await promise;
      expect(results.length).toBe(2);
      expect(onPageCallback).toHaveBeenCalledTimes(2);
    });

    it('should handle empty response', async () => {
      const mockResponse: BasicResponse = {
        status: 'OK',
        content: [],
        totalPages: 0,
        totalCount: 0
      };

      const promise = service.syncAllProducts(1, 10);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=10`);
      req.flush(mockResponse);

      const results = await promise;
      expect(results.length).toBe(1);
      expect(results[0].content.length).toBe(0);
    });

    it('should handle API error in pagination', async () => {
      const promise = service.syncAllProducts(1, 10);

      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=10`);
      req.error(new ErrorEvent('Network error'));

      try {
        await promise;
        fail('Should have thrown an error');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });
  });

  describe('URL construction', () => {
    it('should construct correct URLs for different methods', () => {
      // Test che gli URL vengano costruiti correttamente
      // Questo test verifica indirettamente attraverso le chiamate HTTP mock
      
      service.syncCategories(1, 5, 25);
      const categoriesReq = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncCategories?idCatalog=1&pageIn=5&pageSize=25`);
      categoriesReq.flush({ status: 'OK', content: [] });

      service.syncProducts(2, 3, 50, 999);
      const productsReq = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=2&pageIn=3&pageSize=50&idRootCategory=999`);
      productsReq.flush({ status: 'OK', content: [] });

      service.syncDeletedCategories(3, 1, 15, 1234567890);
      const deletedReq = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=3&pageIn=1&pageSize=15&fromTimestamp=1234567890`);
      deletedReq.flush({ status: 'OK', content: [] });
    });
  });

  describe('Parameter validation', () => {
    it('should handle default parameters correctly', () => {
      service.syncCategories(1);
      const req = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncCategories?idCatalog=1&pageIn=0&pageSize=10`);
      req.flush({ status: 'OK', content: [] });

      service.syncProducts(1);
      const req2 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncProducts?idCatalog=1&pageIn=0&pageSize=100`);
      req2.flush({ status: 'OK', content: [] });

      service.syncDeletedCategories(1);
      const req3 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=1&pageIn=0&pageSize=10`);
      req3.flush({ status: 'OK', content: [] });
    });

    it('should handle null/undefined timestamp correctly', () => {
      service.syncDeletedCategories(1, 0, 10, null);
      const req1 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=1&pageIn=0&pageSize=10`);
      req1.flush({ status: 'OK', content: [] });

      service.syncDeletedCategories(1, 0, 10, undefined);
      const req2 = httpMock.expectOne(`${environment.apiUrl}/appcatalogs/syncDeletedCategories?idCatalog=1&pageIn=0&pageSize=10`);
      req2.flush({ status: 'OK', content: [] });
    });
  });
});
