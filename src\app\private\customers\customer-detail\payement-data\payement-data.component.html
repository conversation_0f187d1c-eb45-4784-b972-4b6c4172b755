<div class="container">
  <div class="row">
    <div class="column label"></div>
    <div class="column title">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA' | translate }}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_STATE' | translate }}</div>
    <div class="column">
      <div class="circle status-{{customer.customerStatus}}"></div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'A'">{{ 'CUSTOMER_DETAIL.CUSTOMER_CANCELED' | translate }}</div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'S'">{{ 'CUSTOMER_DETAIL.CUSTOMER_SUSPENDED' | translate }}</div>
    </div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_CUSTOMER_CODE' | translate }}</div>
    <div class="column value">{{customer.uid}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_BUSINESS_NAME' | translate }}</div>
    <div class="column value">{{customer.name}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_PAYEMENT' | translate }}</div>
    <div class="column value">{{customer.name}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_SHIPMENT' | translate }}</div>
    <div class="column value">{{customer.shipment}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_CURRENCY' | translate }}</div>
    <div class="column value">{{customer.currency}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_RIBA_EXEMPTION' | translate }}</div>
    <div class="column value">{{customer.ribaExemption}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_REA_CODE' | translate }}</div>
    <div class="column value">{{customer.reaCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.PAYEMENT_DATA_ATECO_CODE' | translate }}</div>
    <div class="column value">{{customer.atecoCode}}</div>
  </div>
</div>