/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

@import "tailwindcss";

/* Angular Material Theme - Light Theme */
@import '@angular/material/prebuilt-themes/azure-blue.css';

/* Core CSS required for Ionic components to work properly */
@import "@ionic/angular/css/core.css";
/* Basic CSS for apps built with Ionic */
@import "@ionic/angular/css/normalize.css";
@import "@ionic/angular/css/structure.css";
@import "@ionic/angular/css/typography.css";
@import '@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import "@ionic/angular/css/padding.css";
@import "@ionic/angular/css/float-elements.css";
@import "@ionic/angular/css/text-alignment.css";
@import "@ionic/angular/css/text-transformation.css";
@import "@ionic/angular/css/flex-utils.css";

// Variables
@import "theme/variables.scss";


ion-button {
    --border-radius: 30px;
    font-weight: bold;
    text-transform: uppercase;
    --padding-start: 2em;
    --padding-end: 2em;
    font-size: 1em;
    outline: none;
}

button {
  outline: none;
}

.toast-custom-class {
    --background: var(--ion-dat-success);
    --color: var(--ion-dat-white);
    .toast-button-inner {
        color: var(--ion-dat-white) !important;
    }
}

.toast-error-class {
    --background: var(--ion-dat-red);
    --color: var(--ion-dat-white);
    .toast-button-inner {
        color: var(--ion-dat-white) !important;
    }
}

ion-toast.toast-error-class {
    color: white;
    --background: var(--ion-dat-red);
    --button-color: white !important;
}

ion-toast.toast-custom-class {
    color: white;
    --background: var(--ion-dat-success);
    --button-color: white !important;
}


.custom-alert {
    --min-width: 70%;
    --max-width: 70%;
    --width: 70%;
    .alert-head {
        height: 180px;
        padding-top: 110px;
        text-align: center;
        &::before {
            content: '';
            background: url(./assets/svg/attention.svg) no-repeat;
            background-size: cover;
            height: 80px;
            width: 80px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 20px;
        }
    }
    .alert-button-group {
        justify-content: space-around;
        padding-left: 25%;
        padding-right: 25%;
        margin-bottom: 30px;
    }
    button.alert-button{
        border-radius: 50px;
        text-transform: uppercase;
        font-weight: bold;
        max-width: 120px;
        min-width: 120px;
        text-align: center;
        &:nth-child(1){
            background-color: var(--ion-dat-red);
            color: var(--ion-dat-white);
        }
        &:nth-child(2){
            background-color: var(--ion-dat-middle-gray);
            color: var(--ion-dat-white);
        }
        .alert-button-inner.sc-ion-alert-md {
            justify-content: center;
        }
    }
}

ion-popover.quantity-popover {
    --width: clamp(180px, 50vw, 350px);
}


.swiper-pagination-bullet-custom {
    width: 20px;
    height: 20px;
    text-align: center;
    line-height: 20px;
    font-size: 12px;
    color: #000;
    opacity: 1;
    background: rgba(0, 0, 0, 0.2);
  }
  
  .swiper-pagination-bullet-active {
    color: #fff;
    background: #007aff;
  }