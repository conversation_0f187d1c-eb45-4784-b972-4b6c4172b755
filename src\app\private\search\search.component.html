<div class="overlay" (click)="close($event)" [ngClass]="{'simple-view': simpleView, 'minified': customStyle == 'minified' }" >
  <h1 *ngIf="!simpleView">{{'CUSTOMER_SEARCH.TITLE' | translate}}</h1>
  <ion-searchbar [formControl]="searchField" debounce="100" (click)="searchbarClick($event)" showCancelButton="never" animated placeholder="{{'CUSTOMER_SEARCH.SEARCH' | translate}}"></ion-searchbar>
  <ion-list class="search-results">
    <ion-item *ngFor="let item of filtered$ | async" (click)="startSession(item)">
      <div class="uid" *ngIf="!!item.uid">{{ item.uid }}</div>
      <div *ngIf="!!item.image" class="image">
        <img [src]="item.image | safe" width="100" height="100" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
      </div>
      <div class="code" *ngIf="!!item.code">{{ item.code | zeroRemover }}</div>
      <div class="name" [innerHTML]="item.name"></div>
    </ion-item>
  </ion-list>
</div>
