import { ChangeDetectorRef, Component, ElementRef, OnInit, ViewChild, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { LoadingController } from '@ionic/angular';
import { Customer } from 'src/app/service/data/customer';
import { CustomerCard, CustomerCardType } from 'src/app/service/data/customer-card';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { TranslateService } from '@ngx-translate/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import Utils from 'src/app/shared/utils';
import { SwiperContainer } from 'swiper/element';
import { ScreenOrientation } from '@capacitor/screen-orientation';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { CustomerCardComponent } from './customer-card/customer-card.component';  
import { SearchComponent } from '../search/search.component';
import { ProspectTypesComponent } from '../prospect-types/prospect-types.component';
import { CustomerDetailComponent } from './customer-detail/customer-detail.component';
import { Capacitor } from '@capacitor/core';
@Component({
  selector: 'app-customers',
  templateUrl: './customers.component.html',
  styleUrls: ['./customers.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  animations: [
    trigger('flyInOut', [
      state('in', style({ transform: 'translateX(0)' })),
      transition('void => *', [
        style({ transform: 'translateX(100%)' }),
        animate(100)
      ]),
      transition('* => void', [
        animate(100, style({ transform: 'translateX(100%)' }))
      ])
    ])
  ],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    CustomerCardComponent,
    SearchComponent,
    ProspectTypesComponent,
    CustomerDetailComponent
  ]
})
export class CustomersComponent implements OnInit {
  @ViewChild('slides', { static: true }) swiper: ElementRef | undefined;
  @ViewChild('cards') cards: ElementRef;
  isProspectTypesShown: boolean = false;
  screenWidth = 0;
  screenHeight = 0;
  customers: CustomerCard[] = [];
  slideOpts = {
    initialSlide: 1,
    speed: 400,
    pagination: false
  };
  rows = 3;
  columns = 4;
  cardForPage = this.rows * this.columns;
  totalPages = 1;
  summary: { index: number, letter: string }[] = [];
  detailIsOpen: boolean = false;
  selectedCustomerUid: string = null;
  currentPageIndex = 10;
  currentLetter = '#'
  tappedLetter = null;
  CARD_WIDTH = 250// * window.devicePixelRatio;
  CARD_HEIGHT = 115// * window.devicePixelRatio;
  CARD_GAP = 6 // * window.devicePixelRatio;
  isFilterShown: boolean = false;

  constructor(private _dbService: HybridDbService, private _screenOrientation: ScreenOrientation, private _cdr: ChangeDetectorRef, private _translate: TranslateService, public _loadingController: LoadingController) {
    const physicalScreenWidth = window.screen.width// * window.devicePixelRatio;
    const physicalScreenHeight = window.screen.height// * window.devicePixelRatio;
    this.screenWidth = physicalScreenWidth;
    this.screenHeight = physicalScreenHeight;

    // Controllo per sicurezza...
    if ((window.matchMedia("(orientation: landscape)").matches && physicalScreenHeight > physicalScreenWidth) ||
      (window.matchMedia("(orientation: portrait)").matches && physicalScreenWidth > physicalScreenHeight)) {
      this.screenWidth = physicalScreenHeight;
      this.screenHeight = physicalScreenWidth;
    }
  }

  ngOnInit() {
    this.drawGrid(false);
    if (Capacitor.isNativePlatform()) {
      ScreenOrientation.addListener('screenOrientationChange', () => {
        [this.screenWidth, this.screenHeight] = [this.screenHeight, this.screenWidth];
        this.drawGrid(true);
      });
    }
  }

  async getAllData() {
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();

    const _this = this;
    this.summary = [];
    this.customers = [];
    await this._dbService.getAll(["customers"], ['name', 'uid', 'industrialSector', 'customerStatus', 'infoDataList']).then((data) => {
      if (data.length == 0) {
        this.totalPages = 0;
      }
      else {
        // Trasformo in un elenco di oggetti CustomerCard
        this.customers = data.map((item: Customer) => {
          return item as unknown as CustomerCard
        });
        // Aggiungo le etichette di lettera
        this.addAlphLabel();
        // Metto tutto in ordine alfabetico
        this.customers = this.customers.sort(function (a: CustomerCard, b: CustomerCard) {
          return Utils.compareStrings(a.name, b.name);
        });
        // Aggiungo in testa la card di accesso ai prospect
        this.customers = [this.createProspectCard()].concat(this.customers);

        this.totalPages = Math.ceil(this.customers.length / this.cardForPage);

        if (this.swiper)
          this.swiper.nativeElement.swiper.slideTo(0);
        this.currentLetter = '#';
      }
    }).finally(() => {
      loading.dismiss();
    });
  }

  slideChanges(slide: SwiperContainer) {
    const index = slide.swiper.activeIndex;
    this.currentPageIndex = index;
    if (this.tappedLetter !== null) // questo perchè il tap può portare a una pagina con più iniziali
      this.currentLetter = this.tappedLetter;
    else
      this.currentLetter = this.getLetterByIndex(index);
    this.tappedLetter = null;
  }

  private setRowsAndColumns() {
    const height = this.screenHeight - 200;//(220 * window.devicePixelRatio); // tolgo header, footer, paginator, padding vari
    let width = this.screenWidth - 50;//(50 * window.devicePixelRatio);
    if (this.detailIsOpen)
      width = width / 100 * 60;
    this.columns = Math.floor(width / (this.CARD_WIDTH + this.CARD_GAP)); // L'ambiezza delle colonne va sommata con il gap
    this.rows = Math.floor((height + this.CARD_GAP) / (this.CARD_HEIGHT + this.CARD_GAP));
    this.cardForPage = this.rows * this.columns;
  }

  getPaginatedCardForPage(page) {
    let res = this.paginator(this.customers, page + 1, this.cardForPage);
    return res
  }

  paginator(items, page, per_page) {
    if (items.length == 0)
      return items;
    var page = page || 1,
      per_page = per_page || 10,
      offset = (page - 1) * per_page;
    const el = items.slice(offset).slice(0, per_page);

    // Preparo il sommario per gestire la paginazione -->
    if (page === 1)
      this.summary = [];
    const labels = el.filter((customer: CustomerCard) => {
      return customer.type === 'LABEL';
    });
    if (typeof labels !== "undefined") {
      labels.forEach(label => {
        const exist = this.summary.find((item: { index: number, letter: string }) => { return item.letter === label.name.charAt(0).toUpperCase() });
        if (typeof exist === "undefined")
          this.summary.push({ index: page, letter: label.name.charAt(0).toUpperCase() });
      });
    }
    // <-- Preparo il sommario per gestire la paginazione
    return el;
  }

  private addAlphLabel() {
    for (var i = 'a'.charCodeAt(0); i <= 'z'.charCodeAt(0); i++) {
      const char: string = String.fromCharCode(i);
      if (this.customers.length > 0) {
        const elem: CustomerCard = this.customers.find((customer: CustomerCard) => {
          return customer.name.length > 0 && customer.name.charAt(0).toLowerCase() === char.toLowerCase();
        });
        if (!!elem && elem.uid !== null)
          this.customers.push(new CustomerCard(char, 'LABEL'));
      }
    }
    const elem = this.customers.find((customer: CustomerCard) => {
      return customer.name.length > 0 && Array.from(Array(10).keys()).map(String).includes(customer.name.charAt(0));
    });
    if (!!elem && elem.uid !== null)
      this.customers.push(new CustomerCard('#', 'LABEL'));
  }

  private createProspectCard() {
    return new CustomerCard('', 'PROSPECT');
  }

  openDetail(event: { uid: string, type: CustomerCardType }) {
    if (event.type === 'CARD') {
      this.selectedCustomerUid = event.uid;
      this.detailIsOpen = true;
    }
    else if (event.type === 'PROSPECT') {
      this.openProspectTypes();
    }
  }

  closeDetail(uid: string) {
    this.selectedCustomerUid = null;
    this.detailIsOpen = false;
  }

  getAlphabet() {
    return Array.from({ length: 26 }, (_, i) => String.fromCharCode('A'.charCodeAt(0) + i));
  }

  goToLetter(letterToFind: string) {
    this.tappedLetter = letterToFind;
    const found: { index: number, letter: string } = this.summary.find((item: { index: number, letter: string, class: string }) => {
      return item.letter === letterToFind;
    });
    this.currentLetter = letterToFind;
    if (this.swiper)
      this.swiper.nativeElement.swiper.slideTo(found.index - 1);
  }

  getLetterByIndex(index: number) {
    // if(index === 1)
    //  return '#';
    let found = this.summary.filter((item: { index: number, letter: string, class: string }) => {
      return item.index === index + 1;
    });
    if (found && found.length > 0)
      return found[found.length - 1].letter;

    found = this.summary.filter((item: { index: number, letter: string, class: string }) => {
      return item.index <= index + 1;
    });
    if (found && found.length > 0)
      return found[found.length - 1].letter; // prendo il primo che trovo
    else
      return '#';
  }

  // getIndexByLetter(letterToFind: string) {
  //   const found: {index: number, letter: string} = this.summary.find((item:{index: number, letter: string, class: string}) => {
  //     return item.letter === letterToFind;
  //   });
  //   if(typeof found !== "undefined")
  //     return found.index;
  //   return -1;
  // }

  isDisabled(letter: string) {
    const found = this.summary.filter(l => l.letter === letter);
    return !(typeof found !== "undefined" && found.length >= 1);
  }

  private drawGrid(withChange: boolean): Promise<void> {
    if (withChange) {
      this._cdr.detectChanges();
    }
    this.setRowsAndColumns(); // Setta righe e colonne e card per pagina in base alla risoluzione dello schermo
    return this.getAllData();
  }

  // private goToCurrentSelected(uid: string) {
  //   for(let i = 0; i < this.totalPages; i++) {
  //     const page = this.getPaginatedCardForPage(i);
  //     const found = page.find((element:CustomerCard) => element.uid === uid);
  //     if(found)
  //     {
  //       if(this.slides)
  //         this.slides.slideTo( i );
  //       this.currentLetter = this.getLetterByIndex(i);
  //       break;
  //     }
  //   };
  // }

  private openProspectTypes() {
    this.isProspectTypesShown = true;
  }

  closeProspectTypes() {
    this.isProspectTypesShown = false;
  }

  openSearch() {
    this.isFilterShown = true;
  }

  closeCustomerSearch(event) {
    this.isFilterShown = false;
    this.openDetail({ uid: event, type: 'CARD' });
  }
}
