# Sistema Database Unificato - Documentazione

## 📋 Panoramica

Il Sistema Database Unificato fornisce un'interfaccia trasparente per gestire dati sia su piattaforme web (IndexedDB) che mobile (SQLite), con integrazione automatica degli store NgRx e compatibilità completa con il sistema SyncroV2.

## 🏗️ Architettura

### Componenti Principali

1. **HybridDbService** - Servizio unificato che seleziona automaticamente il database
2. **DbService** - Gestione SQLite per piattaforme mobile (Capacitor)
3. **IndexedDbService** - Gestione IndexedDB per piattaforme web
4. **SyncroV2DbService** - Integrazione con sistema di sincronizzazione v2
5. **UnifiedDbTestComponent** - Componente per testing del sistema

### Selezione Automatica Database

```typescript
// Il sistema seleziona automaticamente:
// - IndexedDB quando platform.is('capacitor') = false (web)
// - SQLite quando platform.is('capacitor') = true (mobile)

const platformInfo = hybridDbService.getPlatformInfo();
console.log(platformInfo);
// Output: { platform: 'web', isWeb: true, isMobile: false }
// oppure: { platform: 'mobile', isWeb: false, isMobile: true }
```

## 🚀 Utilizzo Base

### Inizializzazione

```typescript
import { HybridDbService } from 'src/app/shared/hybrid-db.service';

constructor(private hybridDb: HybridDbService) {}

// Verifica disponibilità
const isAvailable = await this.hybridDb.isDatabaseAvailable();
```

### Operazioni CRUD

```typescript
// CREATE - Inserimento
await this.hybridDb.addRecord(
  'categories',
  ['id', 'name', 'description'],
  ['cat_001', 'Categoria Test', 'Descrizione test']
);

// READ - Lettura
const categories = await this.hybridDb.getAll(['categories']);
const specificCategory = await this.hybridDb.getRecordsByANDCondition(
  'categories',
  [{ key: 'id', value: 'cat_001' }]
);

// UPDATE - Aggiornamento
await this.hybridDb.updateRecord(
  'categories',
  [{ key: 'id', value: 'cat_001' }],
  [{ key: 'name', value: 'Categoria Aggiornata' }]
);

// DELETE - Eliminazione
await this.hybridDb.deleteRecord(
  'categories',
  [{ key: 'id', value: 'cat_001' }]
);
```

### Operazioni Avanzate

```typescript
// Inserimento multiplo
await this.hybridDb.addRecords(
  'products',
  ['id', 'name', 'price'],
  [
    ['prod_001', 'Prodotto 1', '10.99'],
    ['prod_002', 'Prodotto 2', '15.99']
  ]
);

// Query con condizioni
const expensiveProducts = await this.hybridDb.getRecordsByANDCondition(
  'products',
  [{ key: 'price', value: '15.99' }]
);

// Raggruppamento
const groupedData = await this.hybridDb.getAllGroupedBy(
  'products',
  ['category', 'count'],
  ['category']
);
```

## 🔄 Integrazione SyncroV2

### Sincronizzazione con Aggiornamento Store

```typescript
import { SyncroV2OrchestratorService } from 'src/app/service/syncro-v2/syncro-v2-orchestrator.service';

// La sincronizzazione aggiorna automaticamente gli store NgRx
const result = await this.syncroV2Orchestrator.syncCatalogCategories(
  catalogId,
  pageSize,
  (progress) => {
    console.log(`Progresso: ${progress.categoriesProcessed} categorie`);
  }
);

// Gli store NgRx vengono aggiornati automaticamente:
// - setCategories({ items: allCategories })
// - setRootCategories({ rootCategories: rootCategories })
// - setProducts({ items: allProducts })
```

### Inizializzazione Schema

```typescript
import { SyncroV2DbService } from 'src/app/service/syncro-v2/syncro-v2-db.service';

// Aggiorna lo schema per compatibilità SyncroV2
await this.syncroV2DbService.updateDatabaseSchema();
```

## 🧪 Testing

### Componente di Test

```typescript
// Utilizza UnifiedDbTestComponent per testing interattivo
import { UnifiedDbTestComponent } from 'src/app/shared/unified-db-test.component';

// Oppure usa il servizio di test programmaticamente
import { DbTestService } from 'src/app/shared/db-test.service';

const testResult = await this.dbTestService.testUnifiedDatabaseSystem();
console.log('Sistema funzionante:', testResult.databaseWorking);
```

### Test Automatici

```typescript
// Test completo del sistema
const result = await this.dbTestService.testUnifiedDatabaseSystem();

// Risultato:
{
  platform: 'web' | 'mobile',
  databaseWorking: boolean,
  syncroV2Compatible: boolean,
  storeIntegration: boolean,
  errors: string[],
  warnings: string[]
}
```

## 📱 Compatibilità Piattaforme

### Web (IndexedDB)
- ✅ Tutte le operazioni CRUD
- ✅ Transazioni
- ✅ Indici automatici
- ✅ Gestione errori avanzata
- ⚠️ Limitazioni modalità privata browser

### Mobile (SQLite)
- ✅ Tutte le operazioni CRUD
- ✅ Transazioni ACID
- ✅ Query SQL complete
- ✅ Performance ottimali
- ✅ Storage persistente

## 🔧 Configurazione

### Dipendenze Richieste

```json
{
  "@capacitor-community/sqlite": "^5.x.x",
  "@ngrx/store": "^17.x.x",
  "@ionic/angular": "^7.x.x"
}
```

### Providers

```typescript
// app.config.ts
import { HybridDbService } from './shared/hybrid-db.service';
import { DbService } from './shared/db.service';
import { IndexedDbService } from './shared/indexeddb.service';

providers: [
  HybridDbService,
  DbService,
  IndexedDbService,
  // ... altri providers
]
```

## 🚨 Gestione Errori

### Errori Comuni

```typescript
try {
  await this.hybridDb.addRecord('table', ['col'], ['val']);
} catch (error) {
  if (error.message.includes('QuotaExceededError')) {
    // Storage pieno (IndexedDB)
    console.error('Storage quota exceeded');
  } else if (error.message.includes('Database locked')) {
    // Database bloccato (SQLite)
    console.error('Database is locked');
  }
}
```

### Verifica Disponibilità

```typescript
const isAvailable = await this.hybridDb.isDatabaseAvailable();
if (!isAvailable) {
  // Fallback o messaggio errore
  console.warn('Database not available');
}
```

## 📊 Monitoraggio

### Statistiche Database

```typescript
import { UnifiedDatabaseExampleService } from 'src/app/service/unified-database/unified-database-example.service';

const stats = await this.exampleService.getDatabaseStats();
console.log('Statistiche:', stats);
// Output: { platform: 'web', databaseType: 'IndexedDB', categoriesCount: 150, ... }
```

### Log e Debug

```typescript
// Il sistema fornisce log dettagliati:
// 🔧 Inizializzazione
// 📱 Informazioni piattaforma
// ✅ Operazioni completate
// ❌ Errori
// ⚠️ Avvertimenti
```

## 🎯 Best Practices

1. **Sempre verificare disponibilità database prima dell'uso**
2. **Gestire errori appropriatamente per ogni piattaforma**
3. **Utilizzare transazioni per operazioni multiple**
4. **Testare su entrambe le piattaforme**
5. **Monitorare performance e storage usage**

## 🔄 Migrazione

### Da DbService/IndexedDbService Diretti

```typescript
// Prima (non consigliato)
constructor(private dbService: DbService) {}

// Dopo (consigliato)
constructor(private hybridDb: HybridDbService) {}

// Le API sono identiche, solo il servizio cambia
```

## 📚 Esempi Completi

Vedi `UnifiedDatabaseExampleService` per esempi completi di:
- Operazioni CRUD unificate
- Sincronizzazione con store NgRx
- Gestione errori
- Test compatibilità
- Monitoraggio sistema
