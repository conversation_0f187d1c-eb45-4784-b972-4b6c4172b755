<div class="container">
  <div class="row">
    <div class="column label"></div>
    <div class="column title">{{ 'CUSTOMER_DETAIL.GENERAL_DATA' | translate }}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_DATA_STATE' | translate }}</div>
    <div class="column">
      <div class="circle status-{{customer.customerStatus}}"></div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'A'">{{ 'CUSTOMER_DETAIL.CUSTOMER_CANCELED' | translate }}</div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'S'">{{ 'CUSTOMER_DETAIL.CUSTOMER_SUSPENDED' | translate }}</div>
    </div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_DATA_CUSTOMER_CODE' | translate }}</div>
    <div class="column value">{{customer.uid}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_DATA_BUSINESS_NAME' | translate }}</div>
    <div class="column value">{{customer.name}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_DATA_AGENT' | translate }}</div>
    <div class="column value">{{customer.agent}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_FISCAL_CODE' | translate }}</div>
    <div class="column value">{{customer.fiscalCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_VAT_CODE' | translate }}</div>
    <div class="column value">{{customer.vatCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_INDUSTRIAL_SECTOR' | translate }}</div>
    <div class="column value">{{customer.industrialSector}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_RISK_CLASS' | translate }}</div>
    <div class="column value">{{customer.riskClass}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_PRICE_GROUP' | translate }}</div>
    <div class="column value">{{customer.userPriceGroupCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.GENERAL_COMMUNITY_VAT_CODE' | translate }}</div>
    <div class="column value">{{customer.vatCode}}</div>
  </div>
</div>