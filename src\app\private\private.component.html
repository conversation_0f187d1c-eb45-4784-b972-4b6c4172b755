<ion-content (click)="utilityMenuShowed = false" class="private-ion-content"
  forceOverscroll="false">
  <ion-header>
    <ion-toolbar id="leftToolbar" color="dark" [ngClass]="{'green-toolbar': isProspect()}"
      [ngClass]="{'yellow-toolbar': isFavourites()}">
      <ion-buttons slot="primary">
        @if(hasRoute('catalog') && !isProspect()) {
        <ion-button (click)="gotToCartDetail($event)" class="tb-btn">
          <div class="counter-container">
            <div class="cart-counter">{{(cartsCounter$ | async).currentQuantityInCart }}</div>
          </div>
        </ion-button>
        }
        <ion-button (click)="showUtilityMenu($event)" class="tb-btn">
          <div class="icon-settings"></div>
        </ion-button>
        <ion-button (click)="logout()" class="tb-btn">
          <div class="icon-logout"></div>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  @if(!hasRoute('home')) {
  <div class="toolbar">
    <app-toolbar [customerData]="customerData()" (goToHome)="goToHome()" (goCustomGoBack)="goCustomGoBack()"
      (openSearch)="openSearch()" (captureCurrentView)="captureCurrentView()"></app-toolbar>
  </div>
  }
  <div [ngClass]="{'content-with-toolbar': !hasRoute('home')}">
    <router-outlet (activate)="onRouterOutletActivate($event)"></router-outlet>
    </div>
  <ion-footer>
    <ion-toolbar [color]="'light'">
      <ion-title>
        <object type="image/svg+xml" data="../../assets/svg/logo.svg" class="logo-datacol">
          <img src="../../assets/svg/logo.svg" />
        </object>
      </ion-title>
    </ion-toolbar>
  </ion-footer>
</ion-content>

<div class="utility-menu" [ngClass]="{'show': utilityMenuShowed}">
  <div (click)="goTo('settings')">
    <object type="image/svg+xml" data="../../assets/svg/settings.svg">
      <img src="../../assets/svg/settings.svg" />
    </object>
    {{'SETTINGS.MENU' | translate }}
  </div>
  <div (click)="goTo('syncro')">
    <object type="image/svg+xml" data="../../assets/svg/sync.svg">
      <img src="../../assets/svg/sync.svg" />
    </object>
    {{'SYNCRO.MENU' | translate }}
  </div>
</div>