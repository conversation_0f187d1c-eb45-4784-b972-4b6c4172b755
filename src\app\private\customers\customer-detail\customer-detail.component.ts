import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { Router } from '@angular/router';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { Customer } from "../../../service/data/customer";
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { GeneralDataComponent } from './general-data/general-data.component';
import { BankingDataComponent } from './banking-data/banking-data.component';
import { AddressesComponent } from './addresses/addresses.component';
import { PayementDataComponent } from './payement-data/payement-data.component';
import { PartnersComponent } from './partners/partners.component';

type SectionType = 'generalData' | 'bankingData' | 'addresses' | 'payement_data' | 'partners' | 'maps';

@Component({
    selector: 'app-customer-detail',
    templateUrl: './customer-detail.component.html',
    styleUrls: ['./customer-detail.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      GeneralDataComponent,
      BankingDataComponent,
      AddressesComponent,
      PayementDataComponent,
      PartnersComponent
    ] 
})
export class CustomerDetailComponent implements OnChanges {
  @Input() customerUid: string = null;
  @Output() closeDetail = new EventEmitter<string>();
  
  selectedSession:SectionType  = 'generalData';
  customerData: Customer = null;

  constructor(private _router: Router, private _dbService: HybridDbService, private _cdr: ChangeDetectorRef) { }

  async ngOnChanges(changes: SimpleChanges) {
    const _this = this;
    await this._dbService.getRecordsByANDCondition("customers", [{key:'uid', value: this.customerUid}]).then((data) => {
      // Trasformo in un elenco di oggetti CustomerCard
      this.customerData = data[0];
      this.selectedSession = 'generalData';
      this._cdr.detectChanges();
    });
  }

  close() {
    this.closeDetail.emit(this.customerUid);
  }

  showSectionDetail(selected:SectionType){
    this.selectedSession = selected;
  }

  startCustomerSession(){
    if(this.customerData)
      this._router.navigate(['/private/catalog'], { queryParams: { 
                                                      customerUid: this.customerData.uid,
                                                      customerName: this.customerData.name,
                                                      isProspect: false,
                                                      showFavorites: false
                                                  }});
  }

}
