import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Address } from 'src/app/service/data/address';
import { Customer } from 'src/app/service/data/customer';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-addresses',
    templateUrl: './addresses.component.html',
    styleUrls: ['./addresses.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class AddressesComponent {
  @Input() customer: Customer = null;

  constructor() { 
  }

  filterBillingAddress(addresses: Address[]){
    const filtered = addresses.filter((x:Address) => x.billingAddress );
    if(filtered)
      return filtered[0].formattedAddress;
    else 
      return '';
  }

  filterBillingPhone(addresses: Address[]){
    const filtered = addresses.filter((x:Address) => x.billingAddress );
    if(filtered)
      return filtered[0].phone;
    else 
      return '';
  }
}
