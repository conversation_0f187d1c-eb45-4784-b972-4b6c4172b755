<ng-container *ngTemplateOutlet="card.type == 'PRODUCT' ? product : null; context: { $implicit: card }"></ng-container>
<ng-container *ngTemplateOutlet="card.type == 'FAKE' ? fake : null; context: { $implicit: card }"></ng-container>
<ng-container
  *ngTemplateOutlet="catalogService.intoSubcategory && catalogService.isInfiniteView && card.type == 'CATEGORY' ? arrow : null; context: { $implicit: card }"></ng-container>

<ng-template #product let-card>
  <div class="card product infinite" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)"
    [ngClass]="{'white-card': !catalogService.isRootView || catalogService.isShowFavorites, 'favorite': card.isFavorite}"
    longPress (mouseLongPress)="showInfiniteCardActions()">
    @if(showActions) {
    <div class="actions-card">
      @if(!catalogService.isProspect && card.isFavorite) {
      <ion-icon name="star" (click)="cardRemoveFavorite($event)"></ion-icon>
      }

      @if(!catalogService.isProspect && !card.isFavorite) {
      <ion-icon name="star-outline" (click)="cardAddFavorite($event)"></ion-icon>
      }

      <ion-icon name="logo-euro" (click)="cardShowPrices($event)"></ion-icon>
      <ion-icon name="share-social-outline" (click)="share($event)"></ion-icon>
    </div>
    }
    @if(card.divisionStatusCode === 'Z3' && !showActions) {
    <div id="z3" class="zetatre"></div>
    }
    @if(card.focus === 'S' && !showActions) {
    <div id="focusProd" class="focusprodotto"></div>
    }
    @if(!showActions) {
    <div>
      <div class="icon">
        <img id="img" [src]="card.imageUrl | safe" width="100%" height="100%"
          onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
      </div>
      <div class="detail">
        <span class="name">{{card.name}}</span>
      </div>
    </div>
    }
  </div>
</ng-template>

<ng-template #fake let-card>
  <div class="card product infinite white-card">
    <div>
      &nbsp;
    </div>
  </div>
</ng-template>

<ng-template #arrow let-card>
  <div longPress (mouseLongPress)="showPreviewSubcategories()" (click)="open(card.idRootCategory,card.idSubCategory, card.isProduct)" class="arrow-card">
    <div class="divider">
      <span class="divider-title uppercase">{{card.name}}</span>
    </div>
  </div>
</ng-template>