import { Pipe, PipeTransform } from '@angular/core';
import { ProductInCart } from '../service/data/product-in-cart';

@Pipe({
    name: 'inCart2',
    standalone: true,
    pure: false
})
export class InCart2Pipe implements PipeTransform {
  constructor() { }
  transform(products: ProductInCart[], code: string) {
    if(!!products)
    {
      return products.filter(item => item.idProduct === code).length;
    }
    else 
      return 0;
  }
}