import { Country } from "./country";
import { RegionClass } from "./region-class";

export interface Address {
    billingAddress: string;
    companyName: string;
    country: Country;
    defaultAddress: string;
    formattedAddress: string;
    id: string;
    line1: string;
    phone: string;
    postalCode: string;
    region: RegionClass;
    shippingAddress:      string;
    town: string;
    visibleInAddressBook: string;
}

