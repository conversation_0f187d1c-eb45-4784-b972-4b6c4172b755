.container {
    padding: 15px;
    padding-right: 0px;
    .row.category {
        display: flex;
        swiper-container, swiper-slide {
            width: 370px;
            ::ng-deep .swiper-pagination.swiper-pagination-bullets {
                position: relative !important;
                padding-top: 10px !important;
            }
        }
        .col {
            &:first-of-type{
                width: fit-content;
            }
            &:nth-of-type(2) {
                width: 100%;
                padding-left: 5px;
                padding-right: 5px;
            }

            .product-image {
                width: 370px;
                height: 370px;
                pinch-zoom {
                    background-color: #FFF !important;
                    img {
                        width: 370px !important;
                        max-width: 370px !important;
                        height: 370px !important;
                        max-height: 370px !important;
                    }
                }
            }
            .image-desc {
                text-align: center;
            }
            .name {
                color: var(--ion-dat-red);
                text-transform: uppercase;
                font-size: 20px;
                font-weight: bold;
                padding-bottom: 10px;
            }
            .products {
                height: calc(100vh - 338px);
                overflow: scroll;
                &::-webkit-scrollbar {
                    width: 12px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: var(--ion-dat-gray);
                    border-radius: 20px;
                }
                table {
                    border: 1px solid var(--ion-dat-middle-gray);
                    border-collapse: collapse;
                    margin: 0;
                    padding: 0;
                    width: 100%;
                    tr {
                        background-color: var(--ion-dat-black);
                        border: 1px solid var(--ion-dat-middle-gray);
                        padding: .35em;
                        position: relative !important;
                        &:nth-child(odd) td{
                            background-color: var(--ion-dat-white);
                        }
                        &:nth-child(even) td{
                            background-color: var(--ion-dat-gray);
                        }

                        th, td {
                            padding: 2px;
                            text-align: center;
                            position: relative !important;
                        }
                        th {
                            font-size: .85em;
                            letter-spacing: .1em;
                            text-transform: uppercase;
                            color: var(--ion-dat-white);
                        }
                        td {
                            img {
                                width: 60px !important;
                                height: 60px !important
                            }
                            &:not(.buttons):active {
                                object, img, div {
                                    transform: translateY(5px);
                                }
                            }
                            object {
                                width: 30px;
                                height: 30px;
                                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
                            }
                            &.buttons {
                                width: 130px;
                                button {
                                    background-color: transparent;
                                    &:active {
                                        transform: translateY(5px);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

app-tabs {
    width: 100%;
    .sliding {
        overflow-x: scroll;
        overflow-y: hidden;
        white-space: nowrap;
        height: 100%;
        width: 100%;
        display: inline-block;
        .suggested-product{
            display: inline-block;
            text-align: center;
            padding-top: 14px;
            padding-left: 5px;
            padding-right: 5px;
            padding-bottom: 5px;
            text-decoration: none;
            width: 170px;
            div:active {
                transform: translateY(5px);
            }
            .box {
                display: flex;
                flex-direction: column;
                text-align: center;
                white-space: break-spaces;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 4;
                line-clamp: 4;
                -webkit-box-orient: vertical;
                img {
                    margin: auto;
                    width: 60px !important;
                    height: 60px !important;
                }
                .z3 {
                    left: 0 !important;
                    top: 10px !important;
                }
            }
        }
    }
}

.focusProd {
  background-image: url('../../../../assets/svg/focus.svg');
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: 2px;
}

.z3 {
    background-image: url('../../../../assets/icon/icona_attenzione.png');
    background-repeat: no-repeat;
    background-position-x: right;
    background-position-y: 2px;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 0;
}

app-right-bar {
    position: absolute;
    top: 65px;
    right: 0;
}

@media screen and (max-width: 600px) {
    table {
        border: 0;
        thead {
            border: none;
            clip: rect(0 0 0 0);
            height: 1px;
            margin: -1px;
            overflow: hidden;
            padding: 0;
            position: absolute;
            width: 1px;
        }
        tr {
            border-bottom: 3px solid var(--ion-dat-middle-gray);
            display: block;
            margin-bottom: .625em;
        }

        td {
        border-bottom: 1px solid var(--ion-dat-middle-gray);
        display: block;
        font-size: .8em;
        text-align: right;
        }

    td::before {
      /*
      * aria-label has no advantage, it won't be read inside a table
      content: attr(aria-label);
      */
      content: attr(data-label);
        float: left;
        font-weight: bold;
        text-transform: uppercase;
        }
    }
        td:last-child {
        border-bottom: 0;
    }
}

// .customer-name {
//     position: absolute;
//     bottom: 15px;
//     font-weight: bold;
//     color: var(--ion-dat-dark-gray);
//     text-transform: uppercase;
//     z-index: 9999;
//     img {
//         margin-right: 10px;
//     }
// }

.references {
    margin-top: -15px;
    margin-bottom: 20px;
}

swiper-slide {
  display: flex;
  flex-direction: column;
}

// app-navigation-panel {
//   display: none;
//   &.visible {
//     display: inherit;
//   }
// }

/* TABLET verticale */
@media screen and (min-width: 501px) and (max-width: 850px) and (orientation: portrait) {
    .container .row {
        flex-wrap: wrap;
    }
    .products {
        height: inherit !important;
        margin-bottom: 20px;
    }
}

.box {
    font-size: 14px;
}

.product-quantity-in-cart, .product-min-quantity 
{
    font-size: 12px;
    width: 50px;
    text-align: center;
    font-weight: bold;
    position: absolute;
    margin-top: -6px;
    margin-left: 0px;
}

.product-quantity-in-cart
{
    color: var(--ion-dat-red);
}

svg {
    width: 30px;
    height: 30px;
    &.inCart{
        path, ellipse {
            fill: #008EA7 !important;
        }
    }
}