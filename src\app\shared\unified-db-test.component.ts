import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, LoadingController, ToastController } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { DbTestService } from './db-test.service';
import { HybridDbService } from './hybrid-db.service';
import { SyncroV2DbService } from '../service/syncro-v2/syncro-v2-db.service';

@Component({
  selector: 'app-unified-db-test',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    TranslateModule
  ],
  template: `
    <ion-header>
      <ion-toolbar>
        <ion-title>Test Sistema Database Unificato</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <ion-card>
        <ion-card-header>
          <ion-card-title>Informazioni Piattaforma</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p><strong>Piattaforma:</strong> {{ platformInfo.platform }}</p>
          <p><strong>Tipo:</strong> {{ platformInfo.isWeb ? 'Web (IndexedDB)' : 'Mobile (SQLite)' }}</p>
          <p><strong>Database Attivo:</strong> {{ platformInfo.isWeb ? 'IndexedDB' : 'SQLite' }}</p>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Test Rapidi</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runBasicTest()"
            [disabled]="testing">
            <ion-icon name="checkmark-circle-outline" slot="start"></ion-icon>
            Test Database Base
          </ion-button>

          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runSyncroV2Test()"
            [disabled]="testing">
            <ion-icon name="sync-outline" slot="start"></ion-icon>
            Test Compatibilità SyncroV2
          </ion-button>

          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runFullTest()"
            [disabled]="testing">
            <ion-icon name="flask-outline" slot="start"></ion-icon>
            Test Completo Sistema Unificato
          </ion-button>

          <ion-button 
            expand="block" 
            color="primary" 
            (click)="initializeDatabase()"
            [disabled]="testing">
            <ion-icon name="construct-outline" slot="start"></ion-icon>
            Inizializza Schema Database
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card *ngIf="testResults">
        <ion-card-header>
          <ion-card-title>Risultati Test</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <pre>{{ testResults }}</pre>
        </ion-card-content>
      </ion-card>

      <ion-card *ngIf="lastTestResult">
        <ion-card-header>
          <ion-card-title>Stato Sistema</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-item>
            <ion-label>Database Funzionante</ion-label>
            <ion-icon 
              [name]="lastTestResult.databaseWorking ? 'checkmark-circle' : 'close-circle'" 
              [color]="lastTestResult.databaseWorking ? 'success' : 'danger'"
              slot="end">
            </ion-icon>
          </ion-item>

          <ion-item>
            <ion-label>SyncroV2 Compatibile</ion-label>
            <ion-icon 
              [name]="lastTestResult.syncroV2Compatible ? 'checkmark-circle' : 'close-circle'" 
              [color]="lastTestResult.syncroV2Compatible ? 'success' : 'danger'"
              slot="end">
            </ion-icon>
          </ion-item>

          <ion-item>
            <ion-label>Store NgRx Integrato</ion-label>
            <ion-icon 
              [name]="lastTestResult.storeIntegration ? 'checkmark-circle' : 'warning'" 
              [color]="lastTestResult.storeIntegration ? 'success' : 'warning'"
              slot="end">
            </ion-icon>
          </ion-item>

          <div *ngIf="lastTestResult.errors.length > 0" class="ion-margin-top">
            <h4 style="color: var(--ion-color-danger)">Errori:</h4>
            <ul>
              <li *ngFor="let error of lastTestResult.errors" style="color: var(--ion-color-danger)">
                {{ error }}
              </li>
            </ul>
          </div>

          <div *ngIf="lastTestResult.warnings.length > 0" class="ion-margin-top">
            <h4 style="color: var(--ion-color-warning)">Avvertimenti:</h4>
            <ul>
              <li *ngFor="let warning of lastTestResult.warnings" style="color: var(--ion-color-warning)">
                {{ warning }}
              </li>
            </ul>
          </div>
        </ion-card-content>
      </ion-card>
    </ion-content>
  `,
  styles: [`
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      font-size: 12px;
      background: var(--ion-color-light);
      padding: 10px;
      border-radius: 4px;
      max-height: 300px;
      overflow-y: auto;
    }
    
    ion-button {
      margin: 8px 0;
    }
  `]
})
export class UnifiedDbTestComponent implements OnInit {
  testing = false;
  testResults: string = '';
  lastTestResult: any = null;
  platformInfo: any = {};

  constructor(
    private dbTestService: DbTestService,
    private hybridDbService: HybridDbService,
    private syncroV2DbService: SyncroV2DbService,
    private loadingController: LoadingController,
    private toastController: ToastController
  ) {}

  ngOnInit() {
    this.platformInfo = this.hybridDbService.getPlatformInfo();
  }

  async runBasicTest() {
    await this.runTest('Test Database Base', async () => {
      return await this.dbTestService.showDatabaseReport();
    });
  }

  async runSyncroV2Test() {
    await this.runTest('Test Compatibilità SyncroV2', async () => {
      const result = await this.dbTestService.testUnifiedDatabaseSystem();
      return `SyncroV2 Compatibile: ${result.syncroV2Compatible ? '✅' : '❌'}\n` +
             `Errori: ${result.errors.join(', ') || 'Nessuno'}`;
    });
  }

  async runFullTest() {
    await this.runTest('Test Completo Sistema Unificato', async () => {
      this.lastTestResult = await this.dbTestService.testUnifiedDatabaseSystem();
      return await this.dbTestService.showUnifiedSystemReport();
    });
  }

  async initializeDatabase() {
    await this.runTest('Inizializzazione Schema Database', async () => {
      await this.syncroV2DbService.updateDatabaseSchema();
      return 'Schema database inizializzato con successo per SyncroV2';
    });
  }

  private async runTest(testName: string, testFunction: () => Promise<string>) {
    this.testing = true;
    const loading = await this.loadingController.create({
      message: `Esecuzione ${testName}...`,
      spinner: 'crescent'
    });
    await loading.present();

    try {
      this.testResults = await testFunction();
      
      const toast = await this.toastController.create({
        message: `${testName} completato`,
        duration: 2000,
        color: 'success',
        position: 'top'
      });
      await toast.present();
    } catch (error) {
      this.testResults = `Errore durante ${testName}: ${error.message}`;
      
      const toast = await this.toastController.create({
        message: `Errore durante ${testName}`,
        duration: 3000,
        color: 'danger',
        position: 'top'
      });
      await toast.present();
    } finally {
      await loading.dismiss();
      this.testing = false;
    }
  }
}
