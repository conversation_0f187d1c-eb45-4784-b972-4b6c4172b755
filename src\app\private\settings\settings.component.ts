import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { App } from '@capacitor/app';
import { AlertController, IonicModule, LoadingController, Platform, ToastController } from '@ionic/angular';
import { select, Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CatalogService, ViewType } from 'src/app/service/catalog/catalog.service';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { StartDbService } from 'src/app/service/start-db/start-db.service';
import Utils from 'src/app/shared/utils';

@Component({
    selector: 'app-settings',
    templateUrl: './settings.component.html',
    styleUrls: ['./settings.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class SettingsComponent {
  
  protected appVersion: string = '0.0.0';
  protected navigationType: ViewType = ViewType.PAGINATED;
  protected buttonDisabled: boolean = false;

  constructor(private _router: Router, private platform: Platform,
    private _toastController: ToastController, private _translate: TranslateService,
    private _lsService: LocalStorageService,
    public _loadingController: LoadingController, private _alertController: AlertController,
    private _startDbService: StartDbService, private _store: Store<any>,
    private _catalogService: CatalogService) {
  }

  ngAfterViewInit() {
    App.getInfo().then((info) => {
      this.appVersion = info.version;
    });
    const result = this._lsService.get('catalog_navigation_type') as ViewType;
    this.navigationType = !!result ? result : ViewType.PAGINATED;
  }

  doConfirm() {
    this.buttonDisabled = true;
    this._lsService.set('catalog_navigation_type', this.navigationType);
    // Update the catalog service with the new view type
    this._catalogService.setViewType(this.navigationType);
    if(this.navigationType === ViewType.PAGINATED) {
      localStorage.setItem('cardViewType', 'small');
    }
    this._lsService.remove("categories");
    Utils.showSnack(this._toastController,this._translate.instant('SETTINGS.CONF_SAVED'), this._translate.instant("SETTINGS.DONE"), true);
    this.buttonDisabled = false;
  }

  async doReset(){
    this.buttonDisabled = true;
    let _this = this;
    const alert = await this._alertController.create({
      header: this._translate.instant('SETTINGS.CONF_TITLE'),
      message: this._translate.instant('SETTINGS.CONF_MSG'),
      buttons: [
        {
          text: this._translate.instant('SETTINGS.CANCEL'),
          role: 'cancel',
          cssClass: 'secondary',
          id: 'cancel-button',
          handler: (blah) => {
            this.buttonDisabled = false;
          }
        }, {
          text: this._translate.instant('SETTINGS.CONFIRM'),
          id: 'confirm-button',
          handler: async () => {
            const loading = await this._loadingController.create({
              message: this._translate.instant('GENERICS.WAIT')
            });
            loading.present();
            this._lsService.remove("categories");
            this.navigationType = ViewType.PAGINATED;
            this._lsService.set('catalog_navigation_type', this.navigationType);
            // Update the catalog service with the new view type
            this._catalogService.setViewType(this.navigationType);
            localStorage.setItem("isFirstUpdate", "true");
            await this._startDbService.clearDB();
            loading.dismiss();
            Utils.showSnack(this._toastController, this._translate.instant('SETTINGS.CACHE_CLEANED'), this._translate.instant("SETTINGS.DONE"), true);
            this.buttonDisabled = false;
          }
        }
      ]
    });

    await alert.present();
  }

  settingsGoComponentGoBack() {
    let routingStack = [];
    this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
    const lastRouting = routingStack[routingStack.length - 2];
    let navHistory = [];
    this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
    const lastHistory = navHistory[navHistory.length - 1];
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();

    if(lastRouting.component.includes('catalog') && !lastRouting.component.includes('product'))
    {
      this._router.navigate(['/private/catalog'], { queryParams: {
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isProspect: lastRouting.component.includes('isProspect=true'),
        showFavorites: false,
        clickedNavarId: !!lastHistory ? lastHistory.item.id : null,
        turnAround: true
      }});
    } else if (lastRouting.component.includes('product')) {
      this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], { queryParams: {
        idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
        current: JSON.stringify(lastHistory.item.datacolCategory.category),
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isFavorite: lastHistory.item.isFavorite,
        isProspect: lastRouting.component.includes('isProspect=true'),
      }});
    }
    else if(!!lastRouting.component)
      this._router.navigate([lastRouting.component]);
  }
}
