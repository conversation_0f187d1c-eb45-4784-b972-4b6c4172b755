<div #title id="title" class="title">
  {{ 'EXTRAS.TITLE' | translate }}
</div>
<h1>{{"EXTRAS.TITLE" | translate}}</h1>
<div class="container">
  <ng-container *ngIf="extras.length > 0">
    <app-extra-card *ngFor="let item of extras" [extra]="item"></app-extra-card>
  </ng-container>
  <ng-container *ngIf="isFinished && extras.length === 0">
    {{"EXTRAS.NO_EXTRAS" | translate}}
  </ng-container>
</div>