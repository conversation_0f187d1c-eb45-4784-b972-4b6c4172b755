import { createReducer, on } from "@ngrx/store";
import { editNavigationTree, setNavigationTree } from "../actions/navigation-tree.actions";

export const navigationTreeReducer = createReducer(
  [],
  on(setNavigationTree, (state, actions) => actions.items),
  on(editNavigationTree, (state, action) => state.map(item => {
    return item.id === action.item.id ? {...item, ...action.item} : item;
  }))
)
