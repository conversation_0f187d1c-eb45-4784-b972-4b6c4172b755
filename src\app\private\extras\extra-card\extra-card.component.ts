import { Component, Input } from '@angular/core';
import { Extra } from 'src/app/service/data/extra';
import Utils from 'src/app/shared/utils';
import { LoadingController, Platform, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { Browser } from '@capacitor/browser';

@Component({
    selector: 'app-extra-card',
    templateUrl: './extra-card.component.html',
    styleUrls: ['./extra-card.component.scss'],
    standalone: true
})
export class ExtraCardComponent {
  @Input() extra: Extra = null;

  constructor(private _toastController: ToastController, private _translate: TranslateService, private _loadingController: LoadingController,
    private _platform: Platform) {}

  async openWindow(event: Event, url:string) {
    event.stopPropagation();
    const loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT')
    });
    loading.present();
    if (Utils.isActiveConnection() && url && url.length > 0) {
      Utils.showSnack(this._toastController, this._translate.instant('GENERICS.OPENING_IN_PROGRESS'), 'OK', true).then(async _=>{
        if (this._platform.is('ios')) {
          await Browser.open({ url });
        } else {
          window.open(url, '_system');
        }
        loading.dismiss();
      });
    } else {
      Utils.showSnack(this._toastController,this._translate.instant('GENERICS.ERROR_NO_NETWORK'));
    }
    return false;
  }
}
