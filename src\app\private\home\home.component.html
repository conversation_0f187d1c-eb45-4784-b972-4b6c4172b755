<ion-grid>
  <ion-row>
    <ion-col>
        <div class="welcome">
          <H1 *ngIf="agentName">Ciao, {{agentName}}</H1>
        </div>
    </ion-col>
  </ion-row>
  <ion-row class="menu" >
    <ion-col>
      <div class="container">
        <div class="grid">
          <div class="cell">
            <div class="card-content" (click)="goToExtras()">
              <ion-icon name="document-text-outline"></ion-icon>
              <div class="card-title">{{ 'HOME.SEE_EXTRAS' | translate }}</div>
              <div class="button-list">
                <ion-button color="primary">{{ 'HOME.GO' | translate }}</ion-button>
              </div>
            </div>
          </div>
          <div class="cell special" *ngIf="showOnTablet()">
            <div class="card-content" (click)="showCatalogChoice()">
              <ion-icon name="book-outline"></ion-icon>
              <div class="card-title">{{ 'HOME.GO_CATALOG' | translate }}</div>
              <div class="button-list">
                <ion-button color="secondary">{{ 'HOME.GO' | translate }}</ion-button>
              </div>
            </div>
          </div>
          <div class="cell">
            <div class="card-content" (click)="goToCustomerList()">
              <ion-icon name="people-outline"></ion-icon>
              <div class="card-title">{{ 'HOME.CUSTOMER_LIST' | translate }}</div>
              <div class="button-list">
                <ion-button color="primary">{{ 'HOME.GO' | translate }}</ion-button>
              </div>
            </div>
          </div>
          <div class="cell" *ngIf="showOnTablet()" (click)="goToCarts()">
            <div class="card-content">
              <div class="counter-container">
                <div class="cart-counter">{{cartsCounter}}</div>
              </div>
              <div class="card-title">{{ 'HOME.CARTS_TO_BE_TRANSFERED' | translate }}</div>
              <div class="button-list">
                <ion-button color="primary">{{ 'HOME.GO' | translate }}</ion-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>

<app-search *ngIf="isSearchShown" [viewType]="'customers'" [simpleView]="false" (closeSearch)="closeCustomerSearch($event)"></app-search>
<app-prospect-types *ngIf="isProspectTypesShown" (closeProspectTypes)="closeProspectTypes()"></app-prospect-types>
<app-catalog-choice *ngIf="isCatalogChoiseShown" (hideCatalogChoice)="hideCatalogChoice()" (startCustomerSession)="startCustomerSession()" (startProspectSession)="openProspectTypes()" ></app-catalog-choice>
