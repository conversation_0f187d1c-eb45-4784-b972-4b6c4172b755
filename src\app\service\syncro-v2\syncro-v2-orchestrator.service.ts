import { Injectable, inject } from '@angular/core';
import { SyncroV2Service } from './syncro-v2.service';
import { SyncroV2DbService } from './syncro-v2-db.service';
import { BasicResponse } from '../data/basic-response';
import { Category } from '../data/category';
import { TypologyContent, Tag } from '../../private/model/sector-hierarchy-response';
import { HybridDbService } from '../../shared/hybrid-db.service';

export interface SyncProgress {
  currentPage: number;
  totalPages?: number;
  totalCount?: number;
  categoriesProcessed: number;
  productsProcessed?: number;
  deletedCategoriesProcessed: number;
  isComplete: boolean;
  error?: string;
}

export interface SyncResult {
  success: boolean;
  totalCategoriesProcessed: number;
  totalProductsProcessed?: number;
  totalDeletedCategoriesProcessed: number;
  totalPages: number;
  duration: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SyncroV2OrchestratorService {

  private _syncroV2Service = inject(SyncroV2Service);
  private _syncroV2DbService = inject(SyncroV2DbService);
  private _hybridDbService = inject(HybridDbService);

  constructor() { }

  /**
   * Inizializza il database per la sincronizzazione v2
   */
  async initializeDatabase(): Promise<void> {
    await this._syncroV2DbService.updateDatabaseSchema();
  }

  /**
   * Ottiene le root categories identificate tramite regex durante la sincronizzazione v2
   * @param idCatalog ID del catalogo (opzionale)
   * @returns Promise<Category[]> Array delle root categories
   */
  async getRootCategoriesByRegex(idCatalog?: number): Promise<Category[]> {
    try {
      return await this._syncroV2DbService.getRootCategoriesByRegex(idCatalog);
    } catch (error) {
      console.error('❌ Errore nel recupero delle root categories tramite regex:', error);
      return [];
    }
  }

  /**
   * Esegue una sincronizzazione completa delle categorie per un catalogo
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncCatalogCategories(
    idCatalog: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalCategoriesProcessed = 0;
    let totalPages = 0;

    try {
      // Segna l'inizio della sincronizzazione
      await this._syncroV2DbService.markSyncStart(idCatalog, startTime);

      // Sincronizza tutte le categorie
      await this._syncroV2Service.syncAllCategories(
        idCatalog,
        pageSize,
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          if (pageData.status === 'OK' && pageData.content) {
            // Validazione del contenuto e estrazione delle categorie
            let categories: Category[] = [];

            // Gestisce la nuova struttura con content.results
            if (pageData.content.results && Array.isArray(pageData.content.results)) {
              categories = pageData.content.results;
            } else if (Array.isArray(pageData.content)) {
              // Fallback per la struttura precedente
              categories = pageData.content;
            } else {
              console.warn(`⚠️ Pagina ${pageNumber}: content non ha una struttura valida`, pageData.content);
              return;
            }

            // Aggiorna totalPages se disponibile dall'API
            if (totalPagesFromAPI !== undefined) {
              totalPages = totalPagesFromAPI;
            } else {
              totalPages = pageNumber + 1;
            }

            console.log(`📦 Pagina ${pageNumber + 1}/${totalPages}: ricevute ${categories.length} categorie`);
            if (totalCountFromAPI !== undefined) {
              console.log(`📊 Progresso: ${totalCategoriesProcessed + categories.length}/${totalCountFromAPI} categorie totali`);
            }

            // Salva le categorie nel database
            await this._syncroV2DbService.saveCategoriesV2(
              categories,
              idCatalog,
              pageNumber,
              startTime
            );

            totalCategoriesProcessed += categories.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: totalCategoriesProcessed,
                deletedCategoriesProcessed: 0,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber}: risposta non valida`, pageData);
          }
        }
      );

      // Sincronizza i tag DC e la gerarchia dei settori dopo le categorie
      await this.syncTagsAndHierarchy(idCatalog);

      // Sincronizza i clienti per il filtraggio basato sui settori
      await this.syncCustomers();

      // Segna la fine della sincronizzazione
      const endTime = Date.now();
      await this._syncroV2DbService.markSyncComplete(idCatalog, endTime, totalPages);

      return {
        success: true,
        totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: endTime - startTime
      };

    } catch (error) {
      console.error('Errore durante la sincronizzazione delle categorie:', error);
      
      if (onProgress) {
        onProgress({
          currentPage: totalPages,
          categoriesProcessed: totalCategoriesProcessed,
          deletedCategoriesProcessed: 0,
          isComplete: true,
          error: error.message
        });
      }

      return {
        success: false,
        totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Esegue una sincronizzazione incrementale delle categorie cancellate
   * @param idCatalog ID del catalogo
   * @param fromTimestamp Timestamp di partenza (opzionale)
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncDeletedCategories(
    idCatalog: number,
    fromTimestamp?: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalDeletedCategoriesProcessed = 0;
    let totalPages = 0;

    try {
      // Se non è fornito un timestamp, usa l'ultimo timestamp di sincronizzazione
      if (!fromTimestamp) {
        fromTimestamp = await this._syncroV2DbService.getLastSyncTimestamp(idCatalog);
      }

      // Sincronizza tutte le categorie cancellate
      await this._syncroV2Service.syncAllDeletedCategories(
        idCatalog,
        fromTimestamp,
        pageSize,
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          if (pageData.status === 'OK' && pageData.content) {
            // Validazione del contenuto e estrazione delle categorie cancellate
            let deletedCategories: Category[] = [];

            // Gestisce la nuova struttura con content.results
            if (pageData.content.results && Array.isArray(pageData.content.results)) {
              deletedCategories = pageData.content.results;
            } else if (Array.isArray(pageData.content)) {
              // Fallback per la struttura precedente
              deletedCategories = pageData.content;
            } else {
              console.warn(`⚠️ Pagina ${pageNumber} (categorie cancellate): content non ha una struttura valida`, pageData.content);
              return;
            }

            // Aggiorna totalPages se disponibile dall'API
            if (totalPagesFromAPI !== undefined) {
              totalPages = totalPagesFromAPI;
            } else {
              totalPages = pageNumber + 1;
            }

            console.log(`🗑️ Pagina ${pageNumber + 1}/${totalPages}: ricevute ${deletedCategories.length} categorie cancellate`);
            if (totalCountFromAPI !== undefined) {
              console.log(`📊 Progresso cancellate: ${totalDeletedCategoriesProcessed + deletedCategories.length}/${totalCountFromAPI} categorie totali`);
            }

            // Gestisci le categorie cancellate
            await this._syncroV2DbService.handleDeletedCategoriesV2(
              deletedCategories,
              idCatalog,
              pageNumber
            );

            totalDeletedCategoriesProcessed += deletedCategories.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: 0,
                deletedCategoriesProcessed: totalDeletedCategoriesProcessed,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber} (categorie cancellate): risposta non valida`, pageData);
          }
        }
      );

      return {
        success: true,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed,
        totalPages,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('Errore durante la sincronizzazione delle categorie cancellate:', error);
      
      if (onProgress) {
        onProgress({
          currentPage: totalPages,
          categoriesProcessed: 0,
          deletedCategoriesProcessed: totalDeletedCategoriesProcessed,
          isComplete: true,
          error: error.message
        });
      }

      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Esegue una sincronizzazione completa (categorie + categorie cancellate)
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 10)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncComplete(
    idCatalog: number,
    pageSize: number = 10,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();

    try {
      // Prima sincronizza le categorie
      const categoriesResult = await this.syncCatalogCategories(
        idCatalog,
        pageSize,
        (progress) => {
          if (onProgress) {
            onProgress({
              ...progress,
              isComplete: false
            });
          }
        }
      );

      if (!categoriesResult.success) {
        return categoriesResult;
      }

      // Poi sincronizza le categorie cancellate
      const deletedResult = await this.syncDeletedCategories(
        idCatalog,
        undefined, // Usa l'ultimo timestamp automaticamente
        pageSize,
        (progress) => {
          if (onProgress) {
            onProgress({
              currentPage: progress.currentPage,
              categoriesProcessed: categoriesResult.totalCategoriesProcessed,
              deletedCategoriesProcessed: progress.deletedCategoriesProcessed,
              isComplete: false
            });
          }
        }
      );

      // Infine sincronizza i tag DC e la gerarchia dei settori
      await this.syncTagsAndHierarchy(idCatalog);

      // Combina i risultati
      const combinedResult: SyncResult = {
        success: categoriesResult.success && deletedResult.success,
        totalCategoriesProcessed: categoriesResult.totalCategoriesProcessed,
        totalDeletedCategoriesProcessed: deletedResult.totalDeletedCategoriesProcessed,
        totalPages: categoriesResult.totalPages + deletedResult.totalPages,
        duration: Date.now() - startTime,
        error: deletedResult.error || categoriesResult.error
      };

      // Notifica il completamento
      if (onProgress) {
        onProgress({
          currentPage: combinedResult.totalPages,
          categoriesProcessed: combinedResult.totalCategoriesProcessed,
          deletedCategoriesProcessed: combinedResult.totalDeletedCategoriesProcessed,
          isComplete: true,
          error: combinedResult.error
        });
      }

      return combinedResult;

    } catch (error) {
      console.error('Errore durante la sincronizzazione completa:', error);
      
      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalDeletedCategoriesProcessed: 0,
        totalPages: 0,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Sincronizza tutti i prodotti di un catalogo con paginazione automatica
   * @param idCatalog ID del catalogo
   * @param pageSize Dimensione della pagina (default: 100)
   * @param idRootCategory ID della categoria radice (opzionale)
   * @param onProgress Callback per il progresso
   * @returns Promise<SyncResult>
   */
  async syncCatalogProducts(
    idCatalog: number,
    pageSize: number = 100,
    idRootCategory?: string,
    onProgress?: (progress: SyncProgress) => void
  ): Promise<SyncResult> {
    const startTime = Date.now();
    let totalProductsProcessed = 0;
    let totalPages = 0;

    try {
      console.log(`🚀 Avvio sincronizzazione prodotti per catalogo ${idCatalog}${idRootCategory ? ` categoria ${idRootCategory}` : ''}`);

      // Segna l'inizio della sincronizzazione
      await this._syncroV2DbService.markSyncStart(idCatalog, startTime);

      // Ottieni tutte le pagine di prodotti
      const allResponses = await this._syncroV2Service.syncAllProducts(
        idCatalog,
        pageSize,
        Number.parseInt(idRootCategory),
        async (pageData: BasicResponse, pageNumber: number, totalPagesFromAPI?: number, totalCountFromAPI?: number) => {
          console.log(`📦 Processando pagina ${pageNumber} prodotti...`);

          if (totalPagesFromAPI !== undefined) {
            totalPages = totalPagesFromAPI;
          }

          if (pageData && pageData.status === 'OK' && pageData.content) {
            const products = Array.isArray(pageData.content) ? pageData.content : [];
            console.log(`📦 Pagina ${pageNumber}: ricevuti ${products.length} prodotti`);

            // Salva i prodotti nel database
            if (products.length > 0) {
              await this._syncroV2DbService.saveProductsV2(
                products,
                idCatalog,
                pageNumber,
                Date.now(),
                idRootCategory
              );
            }

            totalProductsProcessed += products.length;

            // Notifica il progresso
            if (onProgress) {
              onProgress({
                currentPage: pageNumber,
                categoriesProcessed: 0,
                productsProcessed: totalProductsProcessed,
                deletedCategoriesProcessed: 0,
                isComplete: false,
                totalPages: totalPages,
                totalCount: totalCountFromAPI
              });
            }
          } else {
            console.warn(`⚠️ Pagina ${pageNumber}: risposta non valida`, pageData);
          }
        }
      );

      // Sincronizza i tag DC e la gerarchia dei settori dopo i prodotti
      await this.syncTagsAndHierarchy(idCatalog);

      // Sincronizza i clienti per il filtraggio basato sui settori
      await this.syncCustomers();

      // Segna la fine della sincronizzazione
      const endTime = Date.now();
      await this._syncroV2DbService.markSyncComplete(idCatalog, endTime, totalPages);

      return {
        success: true,
        totalCategoriesProcessed: 0,
        totalProductsProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: endTime - startTime
      };

    } catch (error) {
      console.error('❌ Errore durante la sincronizzazione prodotti:', error);
      return {
        success: false,
        totalCategoriesProcessed: 0,
        totalProductsProcessed,
        totalDeletedCategoriesProcessed: 0,
        totalPages,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Sincronizza i tag DC e la gerarchia dei settori
   *
   * Questo metodo integra le API che precedentemente venivano chiamate direttamente dai componenti:
   * - getSectorHierarchy: per ottenere la gerarchia dei settori (Settori/Attività/Professioni)
   * - syncDCTags: per ottenere i tag assegnati alle categorie DataCol
   *
   * I dati vengono salvati nel database locale per permettere il funzionamento offline
   * e vengono utilizzati dai componenti tag-filter-panel e assign-tag-dialog.
   *
   * @param idCatalog ID del catalogo
   */
  private async syncTagsAndHierarchy(idCatalog: number): Promise<void> {
    try {
      console.log(`🏷️ [SYNC_TAGS] Avvio sincronizzazione tag e gerarchia per catalogo ${idCatalog}`);
      console.log('📝 [SYNC_TAGS] Le API getSectorHierarchy e syncDCTags sono ora integrate nel processo di sync');

      // Sincronizza la gerarchia dei settori (API: getSectorHierarchy)
      console.log('📊 [SYNC_TAGS] Sincronizzazione gerarchia settori...');
      const hierarchyResponse = await this._syncroV2Service.getSectorHierarchy(idCatalog);
      if (hierarchyResponse.status === 'OK') {
        await this.saveSectorHierarchyToDatabase(idCatalog, hierarchyResponse.content);
        console.log('✅ [SYNC_TAGS] Gerarchia settori sincronizzata e salvata nel database locale');
      } else {
        console.log('❌ [SYNC_TAGS] Errore nella sincronizzazione gerarchia settori:', hierarchyResponse.error);
      }

      // Sincronizza i tag DC (API: syncDCTags)
      console.log('🏷️ [SYNC_TAGS] Sincronizzazione tag DC delle categorie...');
      const tagsResponse = await this._syncroV2Service.syncDCTags(
        idCatalog,
        0,
        500,
        Date.now() - (30 * 24 * 60 * 60 * 1000) // Ultimi 30 giorni
      );
      if (tagsResponse.status === 'OK') {
        await this.saveDCTagsToDatabase(idCatalog, tagsResponse.content);
        console.log('✅ [SYNC_TAGS] Tag DC sincronizzati e salvati nel database locale');
      } else {
        console.log('❌ [SYNC_TAGS] Errore nella sincronizzazione tag DC:', tagsResponse.error);
      }

      console.log('🏷️ [SYNC_TAGS] Sincronizzazione completata - i componenti ora utilizzano solo i dati del database locale');

    } catch (error) {
      console.log('❌ [SYNC_TAGS] Errore durante la sincronizzazione di tag e gerarchia:', error);
      // Non bloccare la sincronizzazione principale per errori nei tag
    }
  }

  /**
   * Pulisce i dati di sincronizzazione obsoleti
   * @param idCatalog ID del catalogo
   * @param daysOld Numero di giorni di anzianità per considerare i dati obsoleti
   */
  async cleanupOldData(idCatalog: number, daysOld: number = 30): Promise<void> {
    const cutoffTimestamp = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
    await this._syncroV2DbService.cleanupOldSyncData(idCatalog, cutoffTimestamp);
  }

  /**
   * Salva la gerarchia dei settori nel database locale
   *
   * Questo metodo salva i dati dell'API getSectorHierarchy nella tabella 'sector_hierarchy'
   * per permettere ai componenti di funzionare offline senza chiamare direttamente l'API.
   *
   * @param idCatalog ID del catalogo
   * @param content Contenuto della gerarchia ricevuto dall'API getSectorHierarchy
   */
  private async saveSectorHierarchyToDatabase(idCatalog: number, content: TypologyContent): Promise<void> {
    try {
      console.log(`💾 Salvataggio gerarchia settori per catalogo ${idCatalog}...`);

      // Prima elimina i dati esistenti per questo catalogo
      await this.clearSectorHierarchy(idCatalog);

      const timestamp = Date.now();

      // Salva tutti i tag della gerarchia in modo ricorsivo
      if (content.tags && content.tags.length > 0) {
        await this.saveTags(content.tags, idCatalog, timestamp, null, 1);
      }

      // Salva il tag universale se presente
      if (content.universalTag) {
        await this.saveTag(content.universalTag, idCatalog, timestamp, null, 0);
      }

      console.log(`✅ Gerarchia settori salvata con successo per catalogo ${idCatalog}`);
    } catch (error) {
      console.error(`❌ Errore nel salvataggio della gerarchia settori per catalogo ${idCatalog}:`, error);
      throw error;
    }
  }

  /**
   * Salva i tag DC nel database locale
   *
   * Questo metodo salva i dati dell'API syncDCTags nella tabella 'datacol_category_tags'
   * per permettere ai componenti di funzionare offline senza chiamare direttamente l'API.
   *
   * @param idCatalog ID del catalogo
   * @param content Contenuto dei tag DC ricevuto dall'API syncDCTags
   */
  private async saveDCTagsToDatabase(idCatalog: number, content: any): Promise<void> {
    try {
      console.log(`💾 [SYNC_DC_TAGS] Salvataggio ${content?.length || 0} tag DC per catalogo ${idCatalog}`);

      if (!content || !Array.isArray(content)) {
        console.log(`⚠️ [SYNC_DC_TAGS] Nessun tag DC da salvare per catalogo ${idCatalog}`);
        return;
      }

      const timestamp = Date.now();
      let savedCount = 0;

      // Salva ogni tag DC nel database
      for (const dcTag of content) {
        if (dcTag.idSubCategory && dcTag.tags && Array.isArray(dcTag.tags)) {
          // Prima elimina i tag esistenti per questa categoria
          await this._hybridDbService.deleteRecord(
            'datacol_category_tags',
            [
              { key: 'idSubCategory', value: String(dcTag.idSubCategory) },
              { key: 'idCatalog', value: String(idCatalog) }
            ]
          );

          // Poi inserisce i nuovi tag
          for (const tag of dcTag.tags) {
            await this._hybridDbService.addRecord(
              'datacol_category_tags',
              ['idSubCategory', 'idCatalog', 'keyTag', 'description', 'lastSync'],
              [
                String(dcTag.idSubCategory),
                String(idCatalog),
                tag.keyTag || '',
                tag.description || '',
                String(timestamp)
              ]
            );
            savedCount++;
          }
        }
      }

      console.log(`✅ [SYNC_DC_TAGS] Salvati ${savedCount} tag DC per catalogo ${idCatalog}`);
    } catch (error) {
      console.log(`❌ [SYNC_DC_TAGS] Errore nel salvataggio tag DC per catalogo ${idCatalog}:`, error);
      throw error;
    }
  }

  /**
   * Elimina la gerarchia esistente per un catalogo
   * @param idCatalog ID del catalogo
   */
  private async clearSectorHierarchy(idCatalog: number): Promise<void> {
    try {
      await this._hybridDbService.deleteRecord('sector_hierarchy', [
        { key: 'idCatalog', value: String(idCatalog) }
      ]);
    } catch (error) {
      // Non bloccare il processo per errori di eliminazione
    }
  }

  /**
   * Salva ricorsivamente i tag della gerarchia
   * @param tags Array di tag da salvare
   * @param idCatalog ID del catalogo
   * @param timestamp Timestamp di sincronizzazione
   * @param parentIdTag ID del tag padre (null per i tag di primo livello)
   * @param level Livello nella gerarchia (1=Settore, 2=Attività, 3=Professione)
   */
  private async saveTags(tags: Tag[], idCatalog: number, timestamp: number, parentIdTag: number | null, level: number): Promise<void> {
    for (const tag of tags) {
      await this.saveTag(tag, idCatalog, timestamp, parentIdTag, level);

      // Salva ricorsivamente i sottotag
      if (tag.subTags && tag.subTags.length > 0) {
        await this.saveTags(tag.subTags, idCatalog, timestamp, tag.idTag, level + 1);
      }
    }
  }

  /**
   * Salva un singolo tag nel database
   * @param tag Tag da salvare
   * @param idCatalog ID del catalogo
   * @param timestamp Timestamp di sincronizzazione
   * @param parentIdTag ID del tag padre
   * @param level Livello nella gerarchia
   */
  private async saveTag(tag: Tag, idCatalog: number, timestamp: number, parentIdTag: number | null, level: number): Promise<void> {
    try {
      await this._hybridDbService.addRecord(
        'sector_hierarchy',
        ['idCatalog', 'idTag', 'keyTag', 'description', 'parentIdTag', 'level', 'lastSync'],
        [
          String(idCatalog),
          String(tag.idTag),
          tag.keyTag,
          tag.description,
          parentIdTag ? String(parentIdTag) : null,
          String(level),
          String(timestamp)
        ]
      );
    } catch (error) {
      console.error(`❌ Errore nel salvataggio del tag ${tag.keyTag}:`, error);
      throw error;
    }
  }

  /**
   * Sincronizza i clienti per il filtraggio basato sui settori
   *
   * Questo metodo integra l'API allcustomersbyuser per ottenere i dati dei clienti
   * con le informazioni sui settori (division, codiceAttivita, codiceProfessione)
   * necessarie per il filtraggio del catalogo.
   */
  private async syncCustomers(): Promise<void> {
    try {
      console.log('👥 [SYNC_CUSTOMERS] Avvio sincronizzazione clienti...');

      // Crea l'indice unico per la tabella customers se non esiste
      let forceDownload = false;
      try {
        await this._hybridDbService.createUniqueIndex('customers', ['uid']);
      } catch (error) {
        await this._hybridDbService.clearTable('customers');
        await this._hybridDbService.createUniqueIndex('customers', ['uid']);
        forceDownload = true;
        console.log('🔄 [SYNC_CUSTOMERS] Tabella clienti ricreata con indice unico');
      }

      // Chiama l'API per sincronizzare i clienti
      const response = await this._syncroV2Service.allCustomersByUser(
        forceDownload ? undefined : Date.now() - (30 * 24 * 60 * 60 * 1000) // Ultimi 30 giorni
      );

      if (response.status === 'OK' && response.content && Array.isArray(response.content)) {
        await this.saveCustomersToDatabase(response.content);
        console.log(`✅ [SYNC_CUSTOMERS] ${response.content.length} clienti sincronizzati e salvati nel database locale`);
      } else {
        console.log('❌ [SYNC_CUSTOMERS] Errore nella sincronizzazione clienti:', response.error);
      }

    } catch (error) {
      console.log('❌ [SYNC_CUSTOMERS] Errore durante la sincronizzazione clienti:', error);
      // Non bloccare la sincronizzazione principale per errori nei clienti
    }
  }

  /**
   * Salva i clienti nel database locale
   * @param customers Array di clienti da salvare
   */
  private async saveCustomersToDatabase(customers: any[]): Promise<void> {
    try {
      console.log(`💾 [SYNC_CUSTOMERS] Salvataggio ${customers.length} clienti...`);

      let savedCount = 0;
      let cancelledCount = 0;

      for (const customer of customers) {
        if (!customer.cancellationInstant) {
          // Salva il cliente attivo (prima elimina se esiste, poi inserisce)
          const columns = this.getCustomerColumns(customer);
          const values = this.getCustomerValues(customer, columns);

          // Elimina il record esistente se presente
          try {
            await this._hybridDbService.deleteRecord(
              'customers',
              [{ key: 'uid', value: customer.uid }]
            );
          } catch (error) {
            // Ignora errori di eliminazione (record potrebbe non esistere)
          }

          // Inserisce il nuovo record
          await this._hybridDbService.addRecord(
            'customers',
            columns,
            values
          );
          savedCount++;
        } else {
          // Elimina il cliente cancellato
          await this._hybridDbService.deleteRecord(
            'customers',
            [{ key: 'uid', value: customer.uid }]
          );
          cancelledCount++;
        }
      }

      console.log(`✅ [SYNC_CUSTOMERS] Salvati ${savedCount} clienti, eliminati ${cancelledCount} clienti cancellati`);

    } catch (error) {
      console.error('❌ [SYNC_CUSTOMERS] Errore nel salvataggio clienti:', error);
      throw error;
    }
  }

  /**
   * Ottiene le colonne per il salvataggio del cliente
   */
  private getCustomerColumns(customer: any): string[] {
    const columns = ['uid', 'name', 'active', 'catalog', 'division', 'codiceAttivita', 'codiceProfessione', 'industrialSector'];

    // Aggiungi dinamicamente le colonne presenti nel cliente
    Object.keys(customer).forEach(key => {
      if (!columns.includes(key)) {
        columns.push(key);
      }
    });

    return columns;
  }

  /**
   * Ottiene i valori per il salvataggio del cliente
   */
  private getCustomerValues(customer: any, columns: string[]): any[] {
    return columns.map(column => {
      const value = customer[column];

      // Gestisce i valori speciali
      if (value === null || value === undefined) {
        return null;
      }

      if (typeof value === 'object') {
        return JSON.stringify(value);
      }

      return String(value);
    });
  }
}
