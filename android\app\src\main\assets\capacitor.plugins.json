[{"pkg": "@capacitor-community/file-opener", "classpath": "com.ryltsov.alex.plugins.file.opener.FileOpenerPlugin"}, {"pkg": "@capacitor-community/http", "classpath": "com.getcapacitor.plugin.http.Http"}, {"pkg": "@capacitor-community/keep-awake", "classpath": "com.getcapacitor.community.keepawake.KeepAwakePlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/browser", "classpath": "com.capacitorjs.plugins.browser.BrowserPlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "capacitor-native-biometric", "classpath": "com.epicshaggy.biometric.NativeBiometric"}]