<?xml version='1.0' encoding='utf-8'?>
<widget version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
  <access origin="*" />
  
  <feature name="NativeSettings">
    <param name="ios-package" value="NativeSettings"/>
  </feature>

  <feature name="CordovaHttpPlugin">
    <param name="ios-package" value="CordovaHttpPlugin"/>
  </feature>

  <feature name="AES256">
    <param name="ios-package" value="AES256"/>
  </feature>

  <feature name="AppVersion">
    <param name="ios-package" value="AppVersion"/>
  </feature>

  <feature name="BackgroundMode">
    <param name="ios-package" value="APPBackgroundMode" onload="true"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="Device">
    <param name="ios-package" value="CDVDevice"/>
  </feature>

  <feature name="File">
    <param name="ios-package" value="CDVFile"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="NetworkStatus">
    <param name="ios-package" value="CDVConnection"/>
  </feature>

  <feature name="CDVOrientation">
    <param name="ios-package" value="CDVOrientation"/>
  </feature>

  <feature name="SocialSharing">
    <param name="ios-package" value="SocialSharing"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="Zip">
    <param name="ios-package" value="ZipPlugin"/>
  </feature>

  <feature name="SQLitePlugin">
    <param name="ios-package" value="SQLitePlugin"/>
  </feature>

  
</widget>