import { Injectable } from '@angular/core';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { Category } from '../data/category';
import { Product } from '../data/product';
import { Customer } from '../data/customer';
import { UserSetting } from '../data/user-settings';
import { Cart } from '../data/cart';
import { ProspectType } from '../data/prospect-type';
import { Favorite } from '../data/favorite';
import { Discount } from '../data/discount';
import { DatacolCategory } from '../data/datacol-category';
import { Subcategory } from '../data/subcategory';
import { SyncroV2DbService } from '../syncro-v2/syncro-v2-db.service';
import { ImageOfflineService } from '../syncro-v2/image-offline.service';

@Injectable({
  providedIn: 'root'
})
export class StartDbService {

  constructor(
    private _db: HybridDbService,
    private _syncroV2DbService: SyncroV2DbService
  ) { }

  async createDb() : Promise<any> {
    try {
      await this._db.createTable('customers', this.getColumnList(new Customer(), true), true);
      await this._db.createUniqueIndex('customers', ['uid']);
      await this._db.createTable('categories', this.getColumnList(new Category(), false), false);
      await this._db.createTable('subcategories', this.getColumnList(new Subcategory(), false), false);
      await this._db.createUniqueIndex('subcategories', ['idrootcategory','idsubcategory']);
      await this._db.createTable('datacolCategories', this.getColumnList(new DatacolCategory(), false), false);
      await this._db.createTable('products', this.getColumnList(new Product(), false), true);
      await this._db.createIndex('products', ['idSubCategory']);
      await this._db.createIndex('products', ['code']);
      await this._db.createTable('carts', this.getColumnList(new Cart(), true), true);
      await this._db.createTable('userSettings', this.getColumnList(new UserSetting(), false), false);
      await this._db.createUniqueIndex('userSettings', ['username','type']);
      await this._db.createTable('prospectTypes', this.getColumnList(new ProspectType(), false), true);
      await this._db.createUniqueIndex('prospectTypes', ['uid','name', 'catalog']);
      await this._db.createTable('favorites', this.getColumnList(new Favorite(), false), false);
      await this._db.createTable('discounts', this.getColumnList(new Discount(), false), true);

      // Inizializza il database per la sincronizzazione v2
      await this._syncroV2DbService.updateDatabaseSchema();

      console.log('✅ Database inizializzato con successo');
      return new Promise((resolve) => { resolve(null); });
    } catch (error: any) {
      console.error('❌ Errore durante l\'inizializzazione del database:', error);

      // Se è un errore di versione del database, prova a ricrearlo
      if (error.message && error.message.includes('Database version error')) {
        console.log('🔄 Tentativo di ricreazione del database...');
        await this._db.recreateDatabase();
        return new Promise((resolve) => { resolve(null); });
      }

      throw error;
    }
  }

  async clearDB() {
    this._db.clearTable('customers');
    this._db.clearTable('categories');
    this._db.clearTable('subcategories');
    this._db.clearTable('datacolCategories');
    this._db.clearTable('products');
    this._db.clearTable('carts');
    this._db.clearTable('userSettings');
    this._db.clearTable('prospectTypes');
    this._db.clearTable('favorites');
    this._db.clearTable('discounts');
    this._db.clearTable('files');
    return new Promise((resolve) => { resolve(null); });
  }

  private getColumnList(object, withAutomaticId) {
    let columnList = [];
    Object.entries(object).forEach(([key, value]) => {
      if(key !== 'id' || (key === 'id' && !withAutomaticId))
        columnList.push({columnName: key, columnType: 'TEXT'})
    });
    return columnList;
  }
}
