
.tb-content {
  height: 472px;
  display: flex;
  flex-direction: column;
  .tbar-btn {
    margin: 3px;
    height: 0px;
    width: 50px;
    background: transparent;
    outline: none;
    display: none;
    background-position: center center;
    background-repeat: no-repeat;
    &.visible {
      display: initial;
      height: 60px;
    }
    &:active {
        transform: translateY(5px);
    }
    &.search {
      background-image:url('../../../assets/svg/gray-search.svg');
    }
    &.home {
      background-image:url('../../../assets/svg/home.svg');
    }
    &.catalog.gray {
      background-image:url('../../../assets/svg/gray-catalog.svg');
    }
    &.catalog.red {
      background-image:url('../../../assets/svg/red-catalog.svg');
    }
    &.extras.gray {
      background-image:url('../../../assets/svg/gray-extra.svg');
    }
    &.extras.red {
      background-image:url('../../../assets/svg/red-extra.svg');
    }
    &.sync {
      background-image:url('../../../assets/svg/red-sync.svg');
    }
    &.favorites.gray {
      background-image:url('../../../assets/svg/gray-favorites.svg');
    }
    &.favorites.red {
      background-image:url('../../../assets/svg/red-favorites.svg');
    }
    &.customers {
      background-image:url('../../../assets/svg/gray-customer.svg');
    }
    &.back {
      background-image:url('../../../assets/svg/back.svg');
    }
  }
}
