.title {
    position: absolute;
    top: calc(0px + var(--ion-safe-area-top, 0));
    height: 44px;
    align-items: center;
    display: flex;
    color: white;
    z-index: 9999;
    text-transform: uppercase;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100vw - 280px);
    justify-content: flex-end;
}

.container {
    text-align: center;
    display: flex;
    flex-direction: column;
    margin-top: 15px;
    min-height: calc(100vh - 103px);
    background-color: var(--ion-dat-gray);
}

h1 {
    text-align: center;
    text-transform: uppercase;
    font-weight: bold;
}

.counter-container {
    width: 100%;
    text-align: center;
    margin-top: 2em;
    .cart-counter {
        margin: auto;
        font-size: 30px;
        background-image: url('../../../assets/svg/cart.svg');
        background-repeat: no-repeat;
        background-position: center;
        width: 70px;
        height: 70px;
        color: var(--ion-dat-red);
        padding-left: 7px;
        font-weight: bold;
    }
}

.button-bar {
    ion-button {
        float: right;
        height: 20px;
        margin: 20px;
        margin-right: 0;
        margin-top: 30px;
        margin-bottom: 10px;
        &:active {
            transform: translateY(5px);
        }
    }

    .transfered {
        --background: var(--ion-dat-success);
        opacity: 1;
        --box-shadow: none;
    }

    .sending {
        &:active {
            transform: translateY(0) !important;
        }
    }
}

.toolbar {
    width: 95%;
    max-width: 600px;
    min-width: 300px;
    padding-inline-end: 0px;
    margin-left: auto;
    margin-right: auto;
}

ion-list {
    width: 95%;
    max-width: 600px;
    min-width: 300px;
    padding-inline-end: 0px;
    margin-left: auto;
    margin-right: auto;
    .list-header {
        padding: 0px;
        --min-height: 0px;
        --inner-padding-end: 0px;
        .row {
            background-color: var(--ion-dat-black);
            color: var(--ion-dat-white);
            font-weight: bold;
            .order-row-number {
                color: var(--ion-dat-white);
            }
        }
    }
    .row {
        display: flex;
        width: 100%;
        align-items: center;
    }
}

@keyframes slidingAnimation {
    0% {
        transform: translate3d(-5px,0,0);
        -webkit-transform: translate3d(0,0,0);
    }
    50% {
        transform: translate3d(0px,0,0);
        -webkit-transform: translate3d(5px,0,0);
    }
    100% {
        transform: translate3d(-5px,0,0);
        -webkit-transform: translate3d(0,0,0);
    }
}