import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router, CanActivateChild, NavigationExtras } from '@angular/router';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { JWTTokenService } from '../service/jwttoken.service';
import { addRoutingStack, resetRoutingStack } from '../store/actions/routing-stack.actions';

@Injectable({
  providedIn: 'root'
})
export class PrivateAuthGuard implements CanActivate, CanActivateChild {

  constructor(private _router: Router, private jwtService: JWTTokenService, private _store: Store) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) : Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.jwtService.getIss() === 'datacol') {
      if (this.jwtService.isTokenExpired()) {
        this._store.dispatch(resetRoutingStack());
        this._router.navigate(['/login'], { queryParams: { previusUrl: state.url}});
        return false;
      } else {
        // return true;
        const isFirstAccess = !localStorage.getItem("isFirstUpdate") || localStorage.getItem("isFirstUpdate") === "true";
        if( isFirstAccess && !state.url.includes('syncro')) {
          this._router.navigate(['/private/syncro']);
          return true
        }
        else {
          if(state.url.includes('home'))
            this._store.dispatch(resetRoutingStack());
          this._store.dispatch(addRoutingStack({component: state.url}));
          return true;
        }
      }
    }
    // not logged in so redirect to login page with the return url
    this._store.dispatch(resetRoutingStack());
    this._router.navigate(['/login']);
    return false;
  }

  canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) : Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.canActivate(route, state);
  }
}
