import { ToastController } from "@ionic/angular";
import { Network } from "@capacitor/network";

export default class Utils {

    static async isActiveConnection(): Promise<boolean> {
        try {
            const networkState = await Network.getStatus();
            return networkState.connected;
        } catch (e) {
            return true;
        }
    }

    static objTo<PERSON>son(obj): any {
        try {
            return JSON.parse(obj);
        } catch (e) {
            return obj;
        }
    }

    static compareStrings(a: string, b: string) {
        // Assumiamo di fare una compare case-insensitive
        a = a.toLowerCase();
        b = b.toLowerCase();

        return (a < b) ? -1 : (a > b) ? 1 : 0;
    }

    static splitArray(records, size = 100){
      const chunk = (arr:any, size:any) => arr.reduce((acc:any, _:any, i:any) => (i % size) ? acc : [...acc, arr.slice(i, i + size)], [])
      return chunk(records, size);
    }

    static async showSnackWithColor(type: string = 'toast-custom-class', _toastController: ToastController, msg: string, closeText: string = 'OK', dismiss: boolean = false){
      const popover = await _toastController.getTop();
        if (popover)
            await popover.dismiss(null);
        const toast = await _toastController.create({
          message: msg,
          cssClass: type,
          buttons: [
            {
              text: closeText,
              role: 'cancel'
            }
          ],
          duration: 3000
        });
        toast.present();
    }

    static async showSnack(_toastController: ToastController, msg: string, closeText: string = 'OK', dismiss: boolean = false){
        this.showSnackWithColor('toast-custom-class', _toastController, msg, closeText, dismiss);
      }

      static async resolveImageUrl(_platform, imageDirectory, originalUrl) : Promise<string> {
        let resolvedImageUrl = null;
        if(!!originalUrl)
        {
          await _platform.ready();
          if (_platform.is('capacitor')) {
            const fileName = originalUrl.substring(originalUrl.lastIndexOf('/')+1).replace('jpg', 'webp');
            resolvedImageUrl = imageDirectory + fileName;
          }
        }
        return resolvedImageUrl;
      }

      /**
       * Risolve l'URL dell'immagine utilizzando prima il database offline, poi fallback al metodo precedente
       * @param imageOfflineService Servizio per le immagini offline
       * @param _platform Platform service
       * @param imageDirectory Directory delle immagini
       * @param originalUrl URL originale dell'immagine
       * @returns Promise<string | null> URL risolto dell'immagine
       */
      static async resolveImageUrlWithOffline(imageOfflineService: any, _platform: any, imageDirectory: string, originalUrl: string): Promise<string | null> {
        if (!originalUrl || originalUrl === 'null') {
          return null;
        }

        // Prova prima l'immagine offline dal database
        try {
          const offlineUrl = await imageOfflineService.resolveOfflineImageUrl(originalUrl);
          if (offlineUrl) {
            return offlineUrl;
          }
        } catch (error) {
          console.warn('⚠️ Errore durante la risoluzione dell\'immagine offline:', error);
        }

        // Fallback al metodo precedente per compatibilità
        const resolved = await Utils.resolveImageUrl(_platform, imageDirectory, originalUrl);
        return !!resolved ? resolved : originalUrl;
      }

      static async checkInternet() {
        const status = await Network.getStatus();
        return status.connected;
      }

      static async encryptPassword(plainText: string, secureKey: string, secureIV: string): Promise<string> {
        const enc = new TextEncoder();
        const data = enc.encode(plainText);
        const key = await crypto.subtle.importKey(
          "raw",
          enc.encode(secureKey),
          "AES-CBC",
          false,
          ["encrypt"]
        );
        const iv = enc.encode(secureIV);
      
        const encrypted = await crypto.subtle.encrypt(
          { name: "AES-CBC", iv },
          key,
          data
        );
        return btoa(String.fromCharCode(...new Uint8Array(encrypted)));
      }

      static async decryptPassword(encryptedText: string, secureKey: string, secureIV: string): Promise<string> {
        const enc = new TextEncoder();
        const key = await crypto.subtle.importKey(
          "raw",
          enc.encode(secureKey),
          "AES-CBC",
          false,
          ["decrypt"]
        );
        const iv = enc.encode(secureIV);
        const encryptedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));
        const decrypted = await crypto.subtle.decrypt(
          { name: "AES-CBC", iv },
          key,
          encryptedData
        );
        const dec = new TextDecoder();
        return dec.decode(decrypted);
      }
      

}
