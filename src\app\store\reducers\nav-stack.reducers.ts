import { createReducer, on } from "@ngrx/store";
import { Selection } from "src/app/shared/selection";
import { addSelection, removeLastSelection, resetNavStack, reverseSelection } from "../actions/nav-stack.actions";

export const navStackReducer = createReducer(
  [],
  on(resetNavStack, (state, actions) => {
    return [];
  }),
  on(addSelection, (state, actions) => {
    const filtered = state.filter(single => (single.item as Selection).id === actions.item.id || (single.item as Selection).name === actions.item.name);
    if(filtered.length > 0)
      return state;
    else
      return [...state, actions].slice(-10); // Tengo in storico al massimo le ultime 10 azioni
  }),
  on(removeLastSelection, (state, actions) => {
    let newState: any[] = [...state];
    newState.pop();
    return newState;
  }),
  on(reverseSelection, (state, actions) => {
    let newState: any[] = [...state];
    return newState.reverse();
  })
)
