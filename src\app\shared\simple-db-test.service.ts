import { Injectable, inject } from '@angular/core';
import { Platform } from '@ionic/angular';
import { HybridDbService } from './hybrid-db.service';

/**
 * Servizio di test semplificato per il sistema database unificato
 * Evita dipendenze circolari e fornisce test di base
 */
@Injectable({
  providedIn: 'root'
})
export class SimpleDbTestService {

  private platform = inject(Platform);
  private hybridDbService = inject(HybridDbService);

  constructor() {}

  /**
   * Test rapido del sistema database unificato
   */
  async quickTest(): Promise<{
    platform: string;
    databaseType: string;
    isWorking: boolean;
    errors: string[];
  }> {
    const result = {
      platform: this.platform.is('capacitor') ? 'mobile' : 'web',
      databaseType: this.platform.is('capacitor') ? 'SQLite' : 'IndexedDB',
      isWorking: false,
      errors: [] as string[]
    };

    console.log('🧪 Test rapido sistema database unificato...');

    try {
      // Test 1: Verifica disponibilità
      const isAvailable = await this.hybridDbService.isDatabaseAvailable();
      if (!isAvailable) {
        result.errors.push('Database non disponibile');
        return result;
      }

      // Test 2: Test CRUD di base
      const testTableName = 'quick_test_table';
      const testId = 'test_' + Date.now();

      // CREATE
      await this.hybridDbService.addRecord(
        testTableName,
        ['id', 'name', 'timestamp'],
        [testId, 'Test Record', Date.now().toString()]
      );

      // READ
      const records = await this.hybridDbService.getRecordsByANDCondition(
        testTableName,
        [{ key: 'id', value: testId }]
      );

      if (records.length === 0) {
        result.errors.push('Record non trovato dopo inserimento');
        return result;
      }

      // UPDATE
      await this.hybridDbService.updateRecord(
        testTableName,
        [{ key: 'id', value: testId }],
        [{ key: 'name', value: 'Test Updated' }]
      );

      // DELETE
      await this.hybridDbService.deleteRecord(
        testTableName,
        [{ key: 'id', value: testId }]
      );

      result.isWorking = true;
      console.log('✅ Test rapido completato con successo');

    } catch (error) {
      result.errors.push(`Errore durante test: ${error.message}`);
      console.error('❌ Test rapido fallito:', error);
    }

    return result;
  }

  /**
   * Test delle informazioni piattaforma
   */
  async testPlatformInfo(): Promise<{
    platform: string;
    isWeb: boolean;
    isMobile: boolean;
    databaseType: string;
    platformDetails: any;
  }> {
    const platformInfo = this.hybridDbService.getPlatformInfo();
    
    return {
      platform: platformInfo.platform,
      isWeb: platformInfo.isWeb,
      isMobile: platformInfo.isMobile,
      databaseType: platformInfo.isWeb ? 'IndexedDB' : 'SQLite',
      platformDetails: {
        isCapacitor: this.platform.is('capacitor'),
        isAndroid: this.platform.is('android'),
        isIOS: this.platform.is('ios'),
        isDesktop: this.platform.is('desktop'),
        isMobile: this.platform.is('mobile'),
        userAgent: navigator.userAgent
      }
    };
  }

  /**
   * Test operazioni database specifiche
   */
  async testDatabaseOperations(): Promise<{
    createTable: boolean;
    addRecord: boolean;
    getRecords: boolean;
    updateRecord: boolean;
    deleteRecord: boolean;
    errors: string[];
  }> {
    const result = {
      createTable: false,
      addRecord: false,
      getRecords: false,
      updateRecord: false,
      deleteRecord: false,
      errors: [] as string[]
    };

    const testTable = 'operation_test_table';
    const testId = 'op_test_' + Date.now();

    try {
      // Test CREATE TABLE
      await this.hybridDbService.createTable(testTable, [], false);
      result.createTable = true;
      console.log('✅ CREATE TABLE: OK');
    } catch (error) {
      result.errors.push(`CREATE TABLE failed: ${error.message}`);
      console.error('❌ CREATE TABLE failed:', error);
    }

    try {
      // Test ADD RECORD
      await this.hybridDbService.addRecord(
        testTable,
        ['id', 'data'],
        [testId, 'test data']
      );
      result.addRecord = true;
      console.log('✅ ADD RECORD: OK');
    } catch (error) {
      result.errors.push(`ADD RECORD failed: ${error.message}`);
      console.error('❌ ADD RECORD failed:', error);
    }

    try {
      // Test GET RECORDS
      const records = await this.hybridDbService.getAll([testTable]);
      result.getRecords = records.length >= 0; // Anche 0 record è OK
      console.log('✅ GET RECORDS: OK');
    } catch (error) {
      result.errors.push(`GET RECORDS failed: ${error.message}`);
      console.error('❌ GET RECORDS failed:', error);
    }

    try {
      // Test UPDATE RECORD
      await this.hybridDbService.updateRecord(
        testTable,
        [{ key: 'id', value: testId }],
        [{ key: 'data', value: 'updated data' }]
      );
      result.updateRecord = true;
      console.log('✅ UPDATE RECORD: OK');
    } catch (error) {
      result.errors.push(`UPDATE RECORD failed: ${error.message}`);
      console.error('❌ UPDATE RECORD failed:', error);
    }

    try {
      // Test DELETE RECORD
      await this.hybridDbService.deleteRecord(
        testTable,
        [{ key: 'id', value: testId }]
      );
      result.deleteRecord = true;
      console.log('✅ DELETE RECORD: OK');
    } catch (error) {
      result.errors.push(`DELETE RECORD failed: ${error.message}`);
      console.error('❌ DELETE RECORD failed:', error);
    }

    return result;
  }

  /**
   * Mostra report semplificato
   */
  async showSimpleReport(): Promise<string> {
    const quickTest = await this.quickTest();
    const platformInfo = await this.testPlatformInfo();
    const operations = await this.testDatabaseOperations();

    let report = `📊 REPORT SISTEMA DATABASE UNIFICATO\n`;
    report += `==========================================\n`;
    report += `Platform: ${platformInfo.platform} (${platformInfo.databaseType})\n`;
    report += `Sistema Funzionante: ${quickTest.isWorking ? '✅' : '❌'}\n\n`;

    report += `🔧 Test Operazioni:\n`;
    report += `- CREATE TABLE: ${operations.createTable ? '✅' : '❌'}\n`;
    report += `- ADD RECORD: ${operations.addRecord ? '✅' : '❌'}\n`;
    report += `- GET RECORDS: ${operations.getRecords ? '✅' : '❌'}\n`;
    report += `- UPDATE RECORD: ${operations.updateRecord ? '✅' : '❌'}\n`;
    report += `- DELETE RECORD: ${operations.deleteRecord ? '✅' : '❌'}\n\n`;

    const allErrors = [...quickTest.errors, ...operations.errors];
    if (allErrors.length > 0) {
      report += `❌ ERRORI:\n`;
      allErrors.forEach(error => {
        report += `- ${error}\n`;
      });
    } else {
      report += `🎉 NESSUN ERRORE - SISTEMA COMPLETAMENTE FUNZIONANTE!\n`;
    }

    console.log(report);
    return report;
  }

  /**
   * Test di compatibilità base per SyncroV2
   */
  async testBasicSyncroV2Compatibility(): Promise<boolean> {
    try {
      // Test aggiunta colonne v2
      await this.hybridDbService.addColumn('categories', 'lastSyncTimestamp', 'TEXT');
      await this.hybridDbService.addColumn('categories', 'syncedWithV2', 'TEXT');
      await this.hybridDbService.addColumn('products', 'lastSyncTimestamp', 'TEXT');
      await this.hybridDbService.addColumn('products', 'syncedWithV2', 'TEXT');

      console.log('✅ Colonne SyncroV2 aggiunte con successo');
      return true;
    } catch (error) {
      console.error('❌ Test compatibilità SyncroV2 fallito:', error);
      return false;
    }
  }
}
