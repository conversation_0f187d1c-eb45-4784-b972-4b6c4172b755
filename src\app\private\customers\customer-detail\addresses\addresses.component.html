<div class="container">
  <div class="row">
    <div class="column label"></div>
    <div class="column title">{{ 'CUSTOMER_DETAIL.ADDRESSES' | translate }}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.ADDRESSES_STATE' | translate }}</div>
    <div class="column">
      <div class="circle status-{{customer.customerStatus}}"></div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'A'">{{ 'CUSTOMER_DETAIL.CUSTOMER_CANCELED' | translate }}</div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'S'">{{ 'CUSTOMER_DETAIL.CUSTOMER_SUSPENDED' | translate }}</div>
    </div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.ADDRESSES_CUSTOMER_CODE' | translate }}</div>
    <div class="column value">{{customer.uid}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.ADDRESSES_BUSINESS_NAME' | translate }}</div>
    <div class="column value">{{customer.name}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.ADDRESSES_BILLING_ADDRESS' | translate }}</div>
    <div class="column value">{{filterBillingAddress(customer.addresses)}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.ADDRESSES_BILLING_PHONE' | translate }}</div>
    <div class="column value">{{filterBillingPhone(customer.addresses)}}</div>
  </div>
</div>