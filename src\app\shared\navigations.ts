import { Injectable } from "@angular/core";
import { Store } from "@ngrx/store";
import { Category } from "../service/data/category";
import { NavigationItem } from "../service/data/navigation-item";
import { setNavigationTree } from "../store/actions/navigation-tree.actions";
import Utils from "./utils";

@Injectable({providedIn: 'root'})
export default class Navigations {
  private navigationItem: NavigationItem[] = [];

  constructor(private _store: Store) {
  }

  public async setNavigationTree(categories:Category[]) {
    /* this.navigationItem = [];
    await this.setNavigationItems(categories, 0, null);
    this._store.dispatch(setNavigationTree({ items: this.navigationItem})) */
  }

  private async setNavigationItems( categories: Category[], level: number, parentId: string | null ) {
    /* try {
      if (!!categories)
        for await (let category of categories) {
            let item: NavigationItem = { level: level, id: category.id.toString(), categoryName: category.name, expandible: true, parent: !!parentId ? parentId.toString() : null, expanded: false };
            let subcategories: Category[] = Utils.objToJson(category.subcategories);
            if( subcategories.length > 1 ) {
              let sorted = [...subcategories];
              // Hanno chiesto di mantenere l'ordine da catalogo e non alfabetico <- sorted.sort((a, b) => !!a.name && !!b.name && a.name.localeCompare(b.name));
              subcategories = [...sorted];
            }
            if (!!subcategories && subcategories.length > 0) {
              let expandible = subcategories.filter((item: any) => !Utils.objToJson(item.isProduct) && Utils.objToJson(item.subcategories).length > 0).length > 0;
              item.expandible = expandible;
              this.navigationItem.push(item);
              if (expandible) {
                await this.setNavigationItems(subcategories, level + 1, category.id);
              }
            }
        }
    } catch (err) {
      console.error('setNavigation', err);
    } */
  }
}
