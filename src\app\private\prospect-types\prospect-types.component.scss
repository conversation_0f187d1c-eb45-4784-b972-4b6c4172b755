.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        height: 90vh;
        max-height: 600px;
        width: 90vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            .close {
                background-color: transparent;
                justify-content: end;
            }
        }
        .row {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-top: -77px;
            padding: 25px;
            background-color: var(--ion-dat-green);
            h1 {
                text-transform: uppercase;
                font-weight: bold;
                font-size: 19px;
                color: var(--ion-dat-white);
                margin-top: 0px;
            }
            .prospects {
                background-color: var(--ion-dat-white);
                width: 100%;
                height: 100%;
                overflow-y: scroll;
                ion-grid {
                    height: 100%;
                    width: 100%;
                    padding-inline-start: 0 !important;
                    padding-inline-end: 0 !important;
                    padding-top: 0 !important;
                    padding-bottom: 0 !important;
                    ion-col {
                        text-transform: uppercase;
                        font-weight: bold;
                        color: var(--ion-dat-black);
                        border-right: 1px solid var(--ion-dat-middle-gray);
                        border-bottom: 1px solid var(--ion-dat-middle-gray);
                        display: flex;
                        align-items: center;
                        padding: 15px;
                        &:active {
                            -moz-box-shadow:    inset 0 0 10px var(--ion-dat-gray);
                            -webkit-box-shadow: inset 0 0 10px var(--ion-dat-gray);
                            box-shadow:         inset 0 0 10px var(--ion-dat-gray);
                            div {
                                transform: translateY(2px);
                            }
                        }
                    }
                }
            }
        }
    }
}