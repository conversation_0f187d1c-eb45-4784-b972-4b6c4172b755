{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {}, "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": {"base": "www"}, "index": "src/index.html", "polyfills": ["src/polyfills.ts"], "tsConfig": "tsconfig.app.json", "assets": [{"glob": "**/*", "input": "src/assets", "output": "assets"}, {"glob": "**/*.svg", "input": "node_modules/ionicons/dist/ionicons/svg", "output": "./svg"}], "styles": ["src/theme/variables.scss", "src/theme/dat-variables.scss", "src/global.scss"], "scripts": [], "aot": false, "extractLicenses": false, "sourceMap": true, "optimization": false, "namedChunks": true, "browser": "src/main.ts"}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "aot": true, "extractLicenses": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "15mb"}]}, "develop": {"fileReplacements": [{"replace": "src/app/shared/db.service.ts", "with": "dev/db.service.ts"}], "optimization": false, "outputHashing": "all", "sourceMap": true, "namedChunks": true, "aot": true, "extractLicenses": true}, "ci": {"progress": false}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "app:build"}, "configurations": {"develop": {"buildTarget": "app:build:develop"}, "production": {"buildTarget": "app:build:production"}, "web": {"proxyConfig": "src/proxy.conf.json", "buildTarget": "app:build"}, "ci": {"progress": false}}, "defaultConfiguration": "develop"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "styles": [], "scripts": [], "assets": [{"glob": "favicon.ico", "input": "src/", "output": "/"}, {"glob": "**/*", "input": "src/assets", "output": "/assets"}]}, "configurations": {"ci": {"progress": false, "watch": false}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "app:serve"}, "configurations": {"production": {"devServerTarget": "app:serve:production"}, "ci": {"devServerTarget": "app:serve:ci"}}}}}}, "cli": {"cache": {"enabled": false}, "schematicCollections": ["@ionic/angular-toolkit"], "analytics": false}, "schematics": {"@ionic/angular-toolkit:component": {"styleext": "scss"}, "@ionic/angular-toolkit:page": {"styleext": "scss"}}}