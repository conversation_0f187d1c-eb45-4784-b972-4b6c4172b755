import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
@Injectable({
  providedIn: 'root'
})
export class NavigationService {

  private router = inject(Router)
  private store = inject(Store)

  navigationStack: string[] = [];

  constructor() { }

  goBack(){
    
    if(this.navigationStack.length > 0){
      const route = this.navigationStack.pop();
      console.log('goBack', route);
      this.router.navigate([route]);
    } else {
      this.goHome();
    }
  }

  goHome(){
    this.router.navigate(['/private/home']);
    this.resetNavigationStack();
  }

  resetNavigationStack(){
    console.log('resetNavigationStack');
    this.navigationStack = [];
  }

  navigateTo(route: string){
    if (route == 'home'){
      this.goHome();
    } else {
      const currentRoute = this.router.url;
      this.navigationStack.push(currentRoute);
      this.router.navigate([route]);
    }
  }

  navigateToRootCategory(rootCategoryIdApp: string){
    console.log('navigateToRootCategory', rootCategoryIdApp);
      this.router.navigate(['/private/catalog'], {
        queryParams: {
          rootId: rootCategoryIdApp
        }
      });
  }

  navigateToCategory(categoryIdRoot: string, categoryId?: string){
    console.log('navigateToCategory', categoryIdRoot, categoryId);
    if(categoryId) {
      this.router.navigate(['/private/catalog'], {
        queryParams: {
          rootId: categoryIdRoot,
          categoryId: categoryId
        }
      });
    } else {
      this.router.navigate(['/private/catalog'], {
        queryParams: {
          rootId: categoryIdRoot
        }
      });
    }
  }
}
