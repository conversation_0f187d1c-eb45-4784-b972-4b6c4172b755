import { HttpInter<PERSON>, HttpRequest, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpEvent } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { JWTTokenService } from '../service/jwttoken.service';

@Injectable()
export class ServiceInterceptor implements HttpInterceptor {

    constructor(private translate: TranslateService, private jwtService: JWTTokenService) { }

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        if (req.url.indexOf('assets') > -1) {
            return next.handle(req);
        }
        else {
            const language = this.translate.currentLang ? this.translate.currentLang : 'it';
            const token = this.jwtService.getToken();

            // Controlla se il header "skip" è presente e se è "true"
            const skipHeader = req.headers.get("skip");
            const shouldSkipAuth = skipHeader === "true";


            if (!shouldSkipAuth && token) {
                req = req.clone({
                    url: req.url,
                    setHeaders: {
                        Authorization: `Bearer ${token}`,
                        'pragma': 'no-cache',
                        'Cache-Control': 'no-cache',
                        'Accept-Language': language
                    }
                });
                console.log(`   ✅ Authorization header added`);
            } else {
                // Rimuovi l'header Authorization se presente
                const headers = req.headers.delete('Authorization');
                req = req.clone({
                    url: req.url,
                    headers
                });
                console.log(`   ❌ Authorization header skipped/removed`);
            }
            return next.handle(req);
        }
    }
}
