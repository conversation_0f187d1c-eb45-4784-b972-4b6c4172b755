import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { HybridDbService } from './hybrid-db.service';

/**
 * Componente di test rapido per il sistema database unificato
 * Aggiungi questo componente a qualsiasi pagina per testare il sistema
 */
@Component({
  selector: 'app-quick-db-test',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <ion-card>
      <ion-card-header>
        <ion-card-title>🧪 Test Database Unificato</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <p><strong>Piattaforma:</strong> {{ platformInfo?.platform || 'Caricamento...' }}</p>
        <p><strong>Database:</strong> {{ platformInfo?.databaseType || 'Caricamento...' }}</p>
        <p><strong>Stato:</strong> 
          <ion-badge [color]="isWorking ? 'success' : 'danger'">
            {{ isWorking ? 'Funzionante' : 'Errore' }}
          </ion-badge>
        </p>
        
        <ion-button 
          size="small" 
          fill="outline" 
          (click)="runQuickTest()"
          [disabled]="testing">
          {{ testing ? 'Testing...' : 'Test Rapido' }}
        </ion-button>
        
        <div *ngIf="lastResult" class="ion-margin-top">
          <h4>Ultimo Test:</h4>
          <pre style="font-size: 10px; background: var(--ion-color-light); padding: 8px; border-radius: 4px;">{{ lastResult }}</pre>
        </div>
        
        <div *ngIf="errors.length > 0" class="ion-margin-top">
          <h4 style="color: var(--ion-color-danger)">Errori:</h4>
          <ul>
            <li *ngFor="let error of errors" style="color: var(--ion-color-danger); font-size: 12px;">
              {{ error }}
            </li>
          </ul>
        </div>
      </ion-card-content>
    </ion-card>
  `
})
export class QuickDbTestComponent implements OnInit {
  platformInfo: any = null;
  isWorking = false;
  testing = false;
  lastResult = '';
  errors: string[] = [];

  constructor(private hybridDb: HybridDbService) {}

  async ngOnInit() {
    console.log('🔧 QuickDbTestComponent inizializzato');
    await this.loadPlatformInfo();
    await this.checkSystem();
  }

  private async loadPlatformInfo() {
    try {
      this.platformInfo = this.hybridDb.getPlatformInfo();
      console.log('📱 Platform Info:', this.platformInfo);
    } catch (error) {
      console.error('❌ Errore caricamento info piattaforma:', error);
      this.errors.push(`Info piattaforma: ${error.message}`);
    }
  }

  private async checkSystem() {
    try {
      this.isWorking = await this.hybridDb.isDatabaseAvailable();
      console.log('🔍 Database disponibile:', this.isWorking);
      
      if (!this.isWorking) {
        this.errors.push('Database non disponibile');
      }
    } catch (error) {
      console.error('❌ Errore verifica sistema:', error);
      this.errors.push(`Verifica sistema: ${error.message}`);
      this.isWorking = false;
    }
  }

  async runQuickTest() {
    this.testing = true;
    this.errors = [];
    console.log('🧪 Avvio test rapido...');

    try {
      const testTable = 'quick_test_' + Date.now();
      const testId = 'test_' + Date.now();

      // Test CREATE
      console.log('➕ Test CREATE...');
      await this.hybridDb.addRecord(
        testTable,
        ['id', 'name', 'timestamp'],
        [testId, 'Test Record', Date.now().toString()]
      );

      // Test READ
      console.log('🔍 Test READ...');
      const records = await this.hybridDb.getRecordsByANDCondition(
        testTable,
        [{ key: 'id', value: testId }]
      );

      if (records.length === 0) {
        throw new Error('Record non trovato dopo inserimento');
      }

      // Test UPDATE
      console.log('✏️ Test UPDATE...');
      await this.hybridDb.updateRecord(
        testTable,
        [{ key: 'id', value: testId }],
        [{ key: 'name', value: 'Test Updated' }]
      );

      // Test DELETE
      console.log('🗑️ Test DELETE...');
      await this.hybridDb.deleteRecord(
        testTable,
        [{ key: 'id', value: testId }]
      );

      this.lastResult = `✅ Test completato con successo!
Platform: ${this.platformInfo?.platform}
Database: ${this.platformInfo?.databaseType}
Operazioni: CREATE ✅ READ ✅ UPDATE ✅ DELETE ✅
Timestamp: ${new Date().toLocaleTimeString()}`;

      console.log('✅ Test rapido completato con successo!');

    } catch (error) {
      this.lastResult = `❌ Test fallito: ${error.message}
Timestamp: ${new Date().toLocaleTimeString()}`;
      
      this.errors.push(`Test rapido: ${error.message}`);
      console.error('❌ Test rapido fallito:', error);
    } finally {
      this.testing = false;
    }
  }
}

// Funzione helper per aggiungere il componente a qualsiasi pagina
export function addQuickDbTestToPage() {
  console.log(`
🧪 Per aggiungere il test rapido a una pagina:

1. Importa il componente:
   import { QuickDbTestComponent } from 'src/app/shared/quick-db-test.component';

2. Aggiungi agli imports del componente:
   imports: [..., QuickDbTestComponent]

3. Aggiungi al template:
   <app-quick-db-test></app-quick-db-test>

4. Oppure usa direttamente nel browser console:
   // Test rapido via console
   const hybridDb = document.querySelector('app-root')?._hybridDbService;
   if (hybridDb) {
     hybridDb.isDatabaseAvailable().then(result => console.log('DB Available:', result));
   }
  `);
}
