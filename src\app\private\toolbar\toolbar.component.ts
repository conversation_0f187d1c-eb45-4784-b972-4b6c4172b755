import { Component, EventEmitter, inject, Input, Output } from '@angular/core';
import { Router } from '@angular/router';
import { AlertController } from '@ionic/angular';
import { select, Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CatalogComponent } from '../catalog/catalog.component';
import { ProductComponent } from '../catalog/product/product.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NavigationService } from 'src/app/service/navigation/navigation.service';

@Component({
    selector: 'app-toolbar',
    templateUrl: './toolbar.component.html',
    styleUrls: ['./toolbar.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class ToolbarComponent {

  navigationService = inject(NavigationService)

  @Input() customerData: {uid: string, name: string, isProspect: boolean} = null;
  @Output() goToHome = new EventEmitter();
  @Output() goCustomGoBack = new EventEmitter();
  @Output() captureCurrentView = new EventEmitter();
  @Output() openSearch = new EventEmitter();
  items = {
    search: (this.hasRoute('catalog') || this.hasRoute('customers')),
    home: !this.hasRoute('home'),
    catalog: this.hasRoute('catalog') || this.hasRoute('product')  || this.hasRoute('idCart'),
    extras: this.hasRoute('product') || this.hasRoute('extras') || this.hasRoute('catalog'),
    sync: this.hasRoute('syncro'),
    favorites: (this.hasRoute('catalog') || this.hasRoute('howFavorites=true')) && !this.isProspect(),
    customers: this.hasRoute('catalog') || this.hasRoute('product'),
    goback: !this.hasRoute('home') && !this.hasRoute('customers')
  }
  catalogColor: 'red'|'gray' = ((this.hasRoute('catalog') && this.isCatalogOrigin() && !this.hasRoute('howFavorites=true')) ? 'red' :'gray');
  extrasColor: 'red'|'gray' = this.hasRoute('extras') ? 'red' :'gray';
  favoritesColor: 'red'|'gray' = this.hasRoute('showFavorites=true') ? 'red' :'gray';
  constructor(private _translate: TranslateService, private _alertController: AlertController, private _router: Router,
    private _store: Store<any>) {
      _router.events.subscribe((val) => {
          this.items = {
            search: (this.hasRoute('catalog') || this.hasRoute('customers')),
            home: !this.hasRoute('home'),
            catalog: this.hasRoute('catalog') || this.hasRoute('product')  || this.hasRoute('idCart'),
            extras: this.hasRoute('product') || this.hasRoute('extras') || this.hasRoute('catalog'),
            sync: this.hasRoute('syncro'),
            favorites: (this.hasRoute('catalog') || this.hasRoute('howFavorites=true')) && !this.isProspect(),
            customers: this.hasRoute('catalog') || this.hasRoute('product'),
            goback: !this.hasRoute('home') && !this.hasRoute('customers')
          }
          this.catalogColor = ((this.hasRoute('catalog') && this.isCatalogOrigin() && !this.hasRoute('howFavorites=true')) ? 'red' :'gray');
          this.extrasColor = this.hasRoute('extras') ? 'red' :'gray';
          this.favoritesColor = this.hasRoute('showFavorites=true') ? 'red' :'gray';
        }
      )
     }

  async toolbarGoTo(path) {
    console.log('toolbarGoTo', path);
    if(path === 'home')
    {
      if(this.hasRoute('catalog'))
      {
        const confirm = await this._alertController.create({
          header: this._translate.instant('CATALOG.SESSION_EXIT'),
          buttons: [
            {
              text: this._translate.instant('CATALOG.SESSION_YES'),
              role: 'confirm',
              handler: () => {
                this.goToHome.emit();
              },
            },
            {
              text: this._translate.instant('CATALOG.SESSION_NO'),
              role: 'cancel',
              handler: () => {},
            },
          ],
        });
        await confirm.present();
      } else if (this.hasRoute('extras')) {
        let routingStack = [];
        this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
        const lastRouting = routingStack[routingStack.length - 2];
        
        if(!lastRouting || lastRouting.component.includes('home'))
        {
          this.goToHome.emit();
        } else {
          const alert = await this._alertController.create({
            header: this._translate.instant('EXTRAS.SESSION_EXIT'),
            cssClass: 'custom-alert',
            buttons: [
              {
                text: this._translate.instant('EXTRAS.GO_CATALOG'),
                role: 'confirm',
                id: 'cancel-button',
                handler: (blah) => {
                  this.goCustomGoBack.emit();
                }
              }, {
                text: this._translate.instant('EXTRAS.GO_HOME'),
                id: 'confirm-button',
                role: 'cancel',
                handler: async () => {
                  this.goToHome.emit();
                }
              }
            ]
          });
          await alert.present();
        }
      }
      else {
        this.goToHome.emit();
      }
    }
    else if (path === 'sync')
    {
      this._router.navigate(['/private/syncro']);
    }
    else if (path === 'extras' && !this.hasRoute('extras'))
    {
      let routingStack = [];
      this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
      const currentRouting = routingStack[routingStack.length - 1];
      if(currentRouting.component.includes('catalog') && !currentRouting.component.includes('product')) {
        this.captureCurrentView.emit();
      }
      this._router.navigate(['/private/extras']);
    }
    else if (path === 'catalog')
    {
      if(!!this.customerData)
        this._router.navigate(['/private/catalog'], { queryParams: {
                                                    customerUid: !!this.customerData ? this.customerData.uid : null,
                                                    customerName: !!this.customerData ? this.customerData.name : null,
                                                    isProspect: !!this.customerData ? this.customerData.isProspect : true,
                                                    showFavorites: false,
                                                    reload: new Date().getTime()
                                                }});

    }
    else if (path === 'favorites')
      this._router.navigate(['/private/catalog'], { queryParams: {
        customerUid: this.customerData.uid,
        customerName: this.customerData.name,
        isProspect: false,
        showFavorites: true
    }});
    else if (path === 'customers')
    {
      const confirm = await this._alertController.create({
        header: this._translate.instant('CATALOG.SESSION_EXIT'),
        cssClass: 'custom-alert',
        buttons: [
          {
            text: this._translate.instant('CATALOG.SESSION_YES'),
            role: 'confirm',
            handler: () => {
              this._router.navigate(['/private/customers']);
            },
          },
          {
            text: this._translate.instant('CATALOG.SESSION_NO'),
            role: 'cancel',
            handler: () => {},
          },
        ],
      });
      await confirm.present();
    }
  }

  goTo(path: string){
    console.log('goTo', path);
    this.navigationService.navigateTo(path);
  }

  async goBackRefactor(){
    console.log('goBackRefactor');
    this.navigationService.goBack();
  }

  async goBackward(){
    if(this.hasRoute('cart'))
    {
      this.goCustomGoBack.emit();
    }
    else if(!this.hasRoute('catalog') && !this.hasRoute('catalog/product'))
    {
      this.goCustomGoBack.emit();
    }
    else {
      if(this.isCatalogOrigin() && !this.hasRoute('showFavorites=true'))
      {
        const confirm = await this._alertController.create({
          header: this._translate.instant('CATALOG.SESSION_EXIT'),
          cssClass: 'custom-alert',
          buttons: [
            {
              text: this._translate.instant('CATALOG.SESSION_YES'),
              role: 'confirm',
              handler: () => {
                this.goCustomGoBack.emit();
              },
            },
            {
              text: this._translate.instant('CATALOG.SESSION_NO'),
              role: 'cancel',
              handler: () => {},
            },
          ],
        });
        await confirm.present();
      } else {
        this.goCustomGoBack.emit();
      }
    }
  }

  hasRoute(route: string) {
    return this._router.url.includes(route);
  }

  isCatalogOrigin() {
    let navigationQueue = [];
    this._store.pipe(select('navStack')).subscribe(res => navigationQueue = res).unsubscribe();
    return (this.hasRoute('catalog') && navigationQueue.length === 0);
  }

  isProspect(){
    return this._router.url.includes('isProspect=true');
  }

  search() {
    this.openSearch.emit();
  }
}
