import { Injectable } from '@angular/core';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';

@Injectable({
  providedIn: 'root'
})
export class CartsService extends BasicService {

  // Invio carrello al middleware
  sendOrders(input:{salesman, entries}[]): Promise<BasicResponse> {
    return this.basicPost(input, '/users/sendOrders', false);
  }
}
