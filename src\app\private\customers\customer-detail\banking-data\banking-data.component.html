<div class="container">
  <div class="row">
    <div class="column label"></div>
    <div class="column title">{{ 'CUSTOMER_DETAIL.BANKING_DATA' | translate }}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_DATA_DATA_STATE' | translate }}</div>
    <div class="column">
      <div class="circle status-{{customer.customerStatus}}"></div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'A'">{{ 'CUSTOMER_DETAIL.CUSTOMER_CANCELED' | translate }}</div>
      <div class="status-descr status-{{customer.customerStatus}}" *ngIf="customer.customerStatus === 'S'">{{ 'CUSTOMER_DETAIL.CUSTOMER_SUSPENDED' | translate }}</div>
    </div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_DATA_CUSTOMER_CODE' | translate }}</div>
    <div class="column value">{{customer.uid}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_DATA_BUSINESS_NAME' | translate }}</div>
    <div class="column value">{{customer.name}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_DATA_NATION_BANK' | translate }}</div>
    <div class="column value">{{customer.bankName}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_DATA_FILIAL' | translate }}</div>
    <div class="column value">{{customer.bankBranch}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_ABI' | translate }}</div>
    <div class="column value">{{customer.abiCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_CAB' | translate }}</div>
    <div class="column value">{{customer.cabCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_IBAN' | translate }}</div>
    <div class="column value">{{customer.ibanCode}}</div>
  </div>
  <div class="row">
    <div class="column label">{{ 'CUSTOMER_DETAIL.BANKING_INVOICE_CALENDAR' | translate }}</div>
    <div class="column value">{{customer.invoiceCalendar}}</div>
  </div>
</div>