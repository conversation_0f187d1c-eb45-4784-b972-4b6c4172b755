import { Injectable } from '@angular/core';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';
import { Favorite } from '../data/favorite';

@Injectable({
  providedIn: 'root'
})
export class SyncroService extends BasicService {

  constructor() {
    super();
  }

  allcustomersbyuser(startInstant): Promise<BasicResponse> {
    const incr = !!startInstant ? '?fromTimestamp='+startInstant : '';
    return this.basicGet(`/customers/getsync/allcustomersbyuser${incr}`, false);
  }

  getsynccategories(startInstant): Promise<BasicResponse> {
    const language = localStorage.getItem('language');
    const incr = !!startInstant ? '&fromTimestamp='+startInstant : '';
    return this.basicGet(`/catalogs/getsync/categories?fields=CUSTOM&lang=${language}&recursive=false${incr}`, false);
  }

  productsbycategory(categoryId: string, startInstant): Promise<BasicResponse> {
    const language = localStorage.getItem('language');
    const incr = !!startInstant ? '&fromTimestamp='+startInstant : '';
    return this.basicGet(`/catalogs/getsync/productsbycategory?fields=CUSTOM_PROD&lang=${language}&categoryId=${categoryId}${incr}`, false);
  }

  subcategorybycategory(idRootCategory: string, startInstant): Promise<BasicResponse> {
    const language = localStorage.getItem('language');
    const incr = !!startInstant ? '&fromTimestamp='+startInstant : '';
    return this.basicGet(`/catalogs/getsync/subcategorybycategory?fields=EKR&lang=${language}&idRootCategory=${idRootCategory}${incr}`, false);
  }

  discountlist(salesOrganization, startInstant): Promise<BasicResponse> {
    const incr = !!startInstant ? '&fromTimestamp='+startInstant : '';
    return this.basicGet(`/catalogs/getsync/discountlist?salesOrganisation=${salesOrganization}${incr}`, false);
  }

  allprospectsbyuser(startInstant): Promise<BasicResponse> {
    const incr = !!startInstant ? '?fromTimestamp='+startInstant : '';
    return this.basicGet("/customers/getsync/allprospectsbyuser"+incr, false);
  }

  getCatalogImage(catalog: string, idRootCategory: string, imageName: string) {
    return this.basicGetMedia(`/multimedia/get/image/${catalog}/${idRootCategory}/${imageName}`, false);
  }

  getCatalogImageByCategory(catalog: string, idRootCategory: string, startInstant) {
    const incr = !!startInstant ? '?fromTimestamp='+startInstant : '';
    return this.basicGetMedia(`/multimedia/get/imageszip/${catalog}/${idRootCategory}${incr}`, false);
  }

  getFullCatalogImage(catalog: string) {
    return this.basicGetMedia(`/multimedia/get/imageszip/${catalog}`, false);
  }

  getCancelledImages(startInstant) {
    const incr = !!startInstant ? '?fromTimestamp='+startInstant : '';
    return this.basicGet(`/multimedia/get/cancelledimages${incr}`, false);
  }

  saveProducts(favorites: Favorite[]): Promise<BasicResponse> {
    return this.basicPost({favorites}, "/favorites/saveProducts", false);
  }

  getProducts(): Promise<BasicResponse> {
    return this.basicGet("/favorites/getProducts", false);
  }
}
