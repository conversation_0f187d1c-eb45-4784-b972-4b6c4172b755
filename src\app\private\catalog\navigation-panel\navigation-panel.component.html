<div class="navigator">
  <div class="header">
    <button class="close" (click)="hide()">
      <ion-icon name="close-outline" size="large"></ion-icon>
    </button>
  </div>
  <ng-container *ngFor="let item of navigationItems">
    <div class="level-{{item.level}}"
      [ngClass]="{'visible': item.expanded || item.level == 0 || parentIsExpanded((navigationItems), item),
      'selected' : (item.expanded && sonIsExpanded((navigationItems), item) ) || ((navigationQueue$ | async).length>0 && item.id === (navigationQueue$ | async)[(navigationQueue$ | async).length -1 ].item.id)}">
      <div class="icon-expand-collapse"
        [ngClass]="{'collapse': item.expanded, 'visible': item.expandible || item.level == 0 }"
        [ngStyle]="{'--expandible':item.expandible ? 'initial':'none'}" (click)="expandCollapse(item)"></div>
      <div class="icon-filler" [ngClass]="{'visible': !item.expandible}"></div>
      <div style="width:100%" [innerHTML]="item.categoryName" (click)="navToCategory(item)"></div>
    </div>
  </ng-container>
</div>
