.overlay {
    position: absolute;
    height: 100vh;
    width: 100vw;
    background-color: var(--ion-dat-popover);
    z-index: 9999;
    top: 0;
    text-align: center;
    overflow: hidden;
    &.simple-view {
        background-color: transparent;
        ion-searchbar {
            margin: initial;
            margin-top: 45px;
        }
        ion-list {
            margin: initial;
            max-height: 80vh;
            overflow-y: scroll;
            &::-webkit-scrollbar {
                width: 12px;
            }
            &::-webkit-scrollbar-thumb {
                background-color: var(--ion-dat-gray);
                border-radius: 20px;
            }
        }
    }
    h1 {
        text-transform: uppercase;
        color: var(--ion-dat-white);
        font-size: xx-large;
        font-weight: bold;
        margin: auto;
        margin-top: 80px;
    }
    ion-searchbar {
        margin: auto;
        margin-top: 40px;
        width: 80%;
        max-width: 500px;
        background-color: var(--ion-dat-red);
        --background: var(--ion-dat-white);
        text-align: left;
    }
    ion-list {
        margin: auto;
        width: 80%;
        max-height: 70vh;
        max-width: 500px;
        overflow-y: scroll;
        ion-item:active, ion-item:focus {
          --ion-item-background: var(--ion-dat-gray);
          transform: translateY(5px);
        }
        .image {
            margin-right: 5px;
        }
        .uid {
            width: fit-content;
            font-weight: bold;
        }
        .name {
            text-transform: uppercase;
            text-overflow: ellipsis;
            font-weight: bold;
            overflow:hidden;
            white-space: nowrap;
            padding-left: 5px;
        }
    }
}

@supports (-webkit-touch-callout: none) {
    /* CSS specific to iOS devices */
    .overlay {
        margin-top: 15px;
    }
}

.overlay.minified {
    height: min-content !important;
    width: 80% !important;
    max-width: 500px !important;
    margin-top: initial;
    ion-searchbar {
        background: transparent !important;
        max-width: 300px !important;
        padding-inline-start: 0px;
    }
    ion-list.search-results {
        margin-top: -10px !important;
        padding: 0px !important;
    }
}

@media screen and (orientation: portrait) {
    .overlay.minified {
        ion-searchbar {
            max-width: 200px !important;
        }
    }
}

img {
    max-width: inherit;
}