import { Component, inject } from '@angular/core';
import { CatalogCardComponent } from '../catalog-card.component';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { caretDownOutline, logoEuro, shareSocialOutline, star, starOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
import { IonIcon } from '@ionic/angular/standalone';

@Component({
    selector: 'app-small-catalog-card',
    templateUrl: './small-catalog-card.component.html',
    styleUrls: ['./small-catalog-card.component.scss'],
    standalone: true,
    imports: [
      IonIcon,
      TranslateModule,
      CommonModule,
      SafePipe
    ]
})
export class SmallCatalogCardComponent extends CatalogCardComponent {

  protected catalogService = inject(CatalogService);

  constructor() {
    super();
    addIcons({ caretDownOutline, logoEuro, shareSocialOutline, star, starOutline });
  }

}
