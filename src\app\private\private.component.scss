.ion-color-dark {
    --ion-color-base: #182128 !important;
    &.green-toolbar {
        --ion-color-base: var(--ion-dat-green) !important;
    }
    &.yellow-toolbar {
        --ion-color-base: var(--ion-dat-yellow) !important;
    }
}

.logo-datacol {
    width: 25vh;
    float: right;
}

@media screen and (orientation: portrait) {
    .logo-datacol {
      width: 30% !important;
    }
}

ion-footer {
    position: fixed;
    bottom: 0;
    ion-title {
        padding-inline: 65px;
    }
}
.footer-md::before {
    background: none !important;
}


.tb-btn {
    margin-left: 3px;
    margin-right: 20px;
    .counter-container {
        width: 100%;
        text-align: center;
        width: 30px;
        height: 30px;
        .cart-counter {
            margin: auto;
            font-size: 15px;
            background-image: url('../../assets/svg/white-cart.svg');
            background-size: 30px;
            background-repeat: no-repeat;
            background-position: center;
            padding-left: 4px;
            width: 30px;
            height: 30px;
            color: var(--ion-dat-white);
            font-weight: bold;
        }
    }
    .icon-settings {
        background: url('../../assets/svg/white-person.svg');
        width: 25px;
        height: 25px;
        background-size: contain;
        background-repeat: no-repeat;
    }
    .icon-logout {
        background: url('../../assets/svg/logout.svg');
        width: 25px;
        height: 25px;
        background-size: contain;
        background-repeat: no-repeat;
    }
}

.toolbar {
  width: 60px;
  height: 472px;
  position: fixed;
}

.content-with-toolbar {
    padding-left: 65px;
    padding-right: 65px;

    // Rimuovi padding per il componente test-syncro-v2 per permettere full height
    app-test-syncro-v2 {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        padding: 0;
        margin: 0;
        height: 100vh;
        width: 100vw;
    }
}

ion-header {
    ion-toolbar {
        padding-top: var(--ion-safe-area-top);
        padding-bottom: var(--ion-safe-area-bottom);
    }
}

ion-footer.footer-toolbar-padding ion-toolbar:last-of-type {
    padding-bottom: 0;
}

ion-footer {
   background-color: var(--ion-dat-white);
}

.footer-ios ion-toolbar:first-of-type {
    --border-width: 0 0 0;
}

@supports (-webkit-touch-callout: none) {
    ion-header {
        ion-toolbar {
            height: 60px;
        }
    }
    .utility-menu {
      top: 61px !important;
    }
}

.utility-menu {
  position: absolute;
  z-index: 9999;
  top: 45px;
  right: 85px;
  background: var(--ion-dat-white);
  -webkit-box-shadow: 5px 5px 10px -3px var(--ion-dat-middle-gray);
  -moz-box-shadow: 5px 5px 10px -3px var(--ion-dat-middle-gray);
  -o-box-shadow: 5px 5px 10px -3px var(--ion-dat-middle-gray);
  box-shadow: 5px 5px 10px -3px var(--ion-dat-middle-gray);
  visibility: hidden;
  &.show {
    visibility: visible;
  }
  div {
    padding: 10px;
    font-size: large;
    font-weight: bold;
    display: flex;
    align-items: center;
    object {
      margin-right: 20px;
      pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
    }
  }
}
