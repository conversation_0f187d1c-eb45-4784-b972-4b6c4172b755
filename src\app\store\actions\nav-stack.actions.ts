import { createAction, props } from "@ngrx/store";
import { Selection } from "src/app/shared/selection";

export const resetNavStack = createAction(
  '[NavigationQueue] reset'
);


export const addSelection = createAction(
  '[NavigationQueue] add',
  props<{ item: Selection }>()
);

export const removeLastSelection = createAction(
  '[NavigationQueue] removeLast'
);

export const reverseSelection = createAction(
  '[NavigationQueue] reverse'
);
