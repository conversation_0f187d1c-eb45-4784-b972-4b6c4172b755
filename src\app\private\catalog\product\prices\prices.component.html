
  <div class="popover">
    <div class="container" (click)="hideDiscount()">
      <div class="header">
        <button class="close" (click)="close()">
          <ion-icon name="close-outline" size="large"></ion-icon>
        </button>
      </div>
      <div class="row">
        <h1>{{datacolCategoryName}}</h1>
        <app-discounts *ngIf="isDiscountShown" [@inOutAnimation] [discountList]="selectedDiscountList" [productValueZR00]="selectedProductValueZR00" [productName]="selectedProductName" [productCode]="selectedProductCode" [salesOrganization]="selectedSalesOrganization" [productGp]="selectedProductGp"></app-discounts>
        <app-tabs (tabChanged)="selectDivision($event)">
          <app-tab [tabTitle]="tab.division" [active]="tab.active" *ngFor="let tab of infoDataList_agent">
            <div class="prices">
              <table>
                <thead>
                  <tr>
                    <th scope="col"></th>
                    <th scope="col" *ngIf="!catalogService.isProspect"></th>
                    <th scope="col"></th>
                    <th scope="col">{{"PRICES.ITEM" | translate}}</th>
                    <th scope="col">{{"PRICES.DESCRIPTION" | translate}}</th>
                    <th scope="col" class="price-list">{{"PRICES.L1" | translate}}</th>
                    <th scope="col" class="price-list">{{"PRICES.L2" | translate}}</th>
                    <th scope="col" class="price-list">{{"PRICES.L3" | translate}}</th>
                    <th scope="col" class="price-list">{{"PRICES.L4" | translate}}</th>
                  </tr>
                </thead>
                <tbody *ngIf="!!(productsMatrix | filterReferences:tab.division)">
                  <tr *ngFor="let item of (productsMatrix | filterReferences:tab.division)">
                    <td longPress class="fix cart" data-label="" (click)="addOneToCart(item.product)" (mouseLongPress)="showMultipleQuantity(item.product)">
                      <div *ngIf="(cart|async | productQuantity:item.product.code) > 0" class="product-quantity-in-cart">{{ cart|async | productQuantity:item.product.code }}</div>
                      <div *ngIf="(cart|async | productQuantity:item.product.code) == 0" class="product-min-quantity">{{ item.product.minimumDeliveryQuantity }}</div>
                      <object *ngIf="(cart|async | inCart:item.product.code)" type="image/svg+xml" data="assets/svg/green-cart.svg">
                        <img src='../../assets/svg/green-cart.svg' #svg/>
                      </object>
                      <object *ngIf="!(cart|async | inCart:item.product.code)" type="image/svg+xml" data="assets/svg/cart.svg">
                        <img src='../../assets/svg/cart.svg' #svg/>
                      </object>
                    </td>
                    <td *ngIf="!catalogService.isProspect" class="fix quantity" data-label="">
                      <button (click)="removeQuantity(item.product)" >
                        <ion-icon name="remove-outline"></ion-icon>
                      </button>
                      <button (click)="addOneToCart(item.product)" >
                        <ion-icon name="add-outline"></ion-icon>
                      </button>
                    </td>
                    <td class="fix product-image" data-label="">
                      <img [src]="item.product.image | safe" width="60" height="60" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
                    </td>
                    <td class="fix art bold" data-label="Art." [ngClass]="{'focusProd': item.product.focus === 'S'}" >
                      {{item.product.code | zeroRemover}}
                      <div *ngIf="item.product.divisionStatusCode === 'Z3'" class="z3"></div>
                    </td>
                    <td class="fix" data-label="Descrizione">{{item.product.name}}</td>
                    <td data-label="L1" (click)="showDiscount($event, 'L1', item.product, item.L1.gp, item.L1.priceValue, tab)"><div class="price-block"><div class="price">{{item.L1.price}}</div><div class="other"><div class="value">{{item.L1.gp}}</div><div class="unitFactor">{{item.L1.unitFactor}}</div></div></div></td>
                    <td data-label="L2" (click)="showDiscount($event, 'L2', item.product, item.L2.gp, item.L2.priceValue, tab)"><div class="price-block"><div class="price">{{item.L2.price}}</div><div class="other"><div class="value">{{item.L2.gp}}</div><div class="unitFactor">{{item.L2.unitFactor}}</div></div></div></td>
                    <td data-label="L3" (click)="showDiscount($event, 'L3', item.product, item.L3.gp, item.L3.priceValue, tab)"><div class="price-block"><div class="price">{{item.L3.price}}</div><div class="other"><div class="value">{{item.L3.gp}}</div><div class="unitFactor">{{item.L3.unitFactor}}</div></div></div></td>
                    <td data-label="L4" (click)="showDiscount($event, 'L4', item.product, item.L4.gp, item.L4.priceValue, tab)"><div class="price-block"><div class="price">{{item.L4.price}}</div><div class="other"><div class="value">{{item.L4.gp}}</div><div class="unitFactor">{{item.L4.unitFactor}}</div></div></div></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </app-tab>
        </app-tabs>
      </div>
    </div>
  </div>
