import { Injectable, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { HybridDbService } from '../../shared/hybrid-db.service';
import { SyncroV2OrchestratorService } from '../syncro-v2/syncro-v2-orchestrator.service';
import { SyncroV2DbService } from '../syncro-v2/syncro-v2-db.service';
import { Platform } from '@ionic/angular';

/**
 * Servizio di esempio che dimostra l'utilizzo del sistema database unificato
 * 
 * Questo servizio mostra come:
 * 1. Il database viene selezionato automaticamente in base alla piattaforma
 * 2. Le operazioni CRUD funzionano trasparentemente su entrambe le piattaforme
 * 3. La sincronizzazione v2 aggiorna automaticamente gli store NgRx
 * 4. Il sistema gestisce sia IndexedDB (web) che SQLite (mobile)
 */
@Injectable({
  providedIn: 'root'
})
export class UnifiedDatabaseExampleService {

  private _hybridDbService = inject(HybridDbService);
  private _syncroV2Orchestrator = inject(SyncroV2OrchestratorService);
  private _syncroV2DbService = inject(SyncroV2DbService);
  private _store = inject(Store<any>);
  private _platform = inject(Platform);

  constructor() {
    console.log('🔧 UnifiedDatabaseExampleService inizializzato');
    this.logPlatformInfo();
  }

  /**
   * Mostra informazioni sulla piattaforma e database attivo
   */
  private logPlatformInfo(): void {
    const platformInfo = this._hybridDbService.getPlatformInfo();
    console.log('📱 Informazioni Piattaforma:', {
      platform: platformInfo.platform,
      isWeb: platformInfo.isWeb,
      isMobile: platformInfo.isMobile,
      databaseType: platformInfo.isWeb ? 'IndexedDB' : 'SQLite'
    });
  }

  /**
   * Esempio 1: Operazioni CRUD unificate
   * Dimostra come le stesse operazioni funzionano su entrambe le piattaforme
   */
  async demonstrateCRUDOperations(): Promise<void> {
    console.log('📝 Dimostrazione operazioni CRUD unificate...');

    try {
      // CREATE - Inserimento dati
      console.log('➕ CREATE: Inserimento categoria di test...');
      const testCategory = {
        id: 'test_category_' + Date.now(),
        name: 'Categoria Test Unificata',
        description: 'Categoria creata per dimostrare il sistema unificato',
        isRootCategory: 'false',
        lastSyncTimestamp: Date.now().toString(),
        syncedWithV2: 'true'
      };

      await this._hybridDbService.addRecord(
        'categories',
        Object.keys(testCategory),
        Object.values(testCategory)
      );
      console.log('✅ Categoria inserita:', testCategory.id);

      // READ - Lettura dati
      console.log('🔍 READ: Lettura categoria inserita...');
      const readResult = await this._hybridDbService.getRecordsByANDCondition(
        'categories',
        [{ key: 'id', value: testCategory.id }]
      );
      console.log('✅ Categoria letta:', readResult[0]);

      // UPDATE - Aggiornamento dati
      console.log('✏️ UPDATE: Aggiornamento categoria...');
      await this._hybridDbService.updateRecord(
        'categories',
        [{ key: 'id', value: testCategory.id }],
        [{ key: 'name', value: 'Categoria Test Aggiornata' }]
      );
      console.log('✅ Categoria aggiornata');

      // DELETE - Eliminazione dati
      console.log('🗑️ DELETE: Eliminazione categoria...');
      await this._hybridDbService.deleteRecord(
        'categories',
        [{ key: 'id', value: testCategory.id }]
      );
      console.log('✅ Categoria eliminata');

      console.log('🎉 Operazioni CRUD completate con successo!');
    } catch (error) {
      console.error('❌ Errore durante le operazioni CRUD:', error);
      throw error;
    }
  }

  /**
   * Esempio 2: Sincronizzazione con aggiornamento automatico store
   * Dimostra come la sincronizzazione aggiorna automaticamente gli store NgRx
   */
  async demonstrateSyncWithStoreUpdate(catalogId: number = 1): Promise<void> {
    console.log('🔄 Dimostrazione sincronizzazione con aggiornamento store...');

    try {
      // Inizializza lo schema del database
      console.log('🔧 Inizializzazione schema database...');
      await this._syncroV2DbService.updateDatabaseSchema();

      // Esegui sincronizzazione categorie
      console.log('📂 Sincronizzazione categorie...');
      const categoriesResult = await this._syncroV2Orchestrator.syncCatalogCategories(
        catalogId,
        50, // pageSize
        (progress) => {
          console.log(`📊 Progresso categorie: ${progress.categoriesProcessed} processate`);
        }
      );

      console.log('✅ Sincronizzazione categorie completata:', {
        totalCategoriesProcessed: categoriesResult.totalCategoriesProcessed,
        totalPagesProcessed: categoriesResult.totalPagesProcessed
      });

      // Verifica che gli store siano stati aggiornati
      console.log('🔍 Verifica aggiornamento store NgRx...');
      
      // Gli store vengono aggiornati automaticamente dal SyncroV2DbService
      // dopo ogni operazione di salvataggio
      
      console.log('🎉 Sincronizzazione con aggiornamento store completata!');
    } catch (error) {
      console.error('❌ Errore durante la sincronizzazione:', error);
      throw error;
    }
  }

  /**
   * Esempio 3: Verifica compatibilità piattaforma
   * Dimostra come verificare che il sistema funzioni correttamente
   */
  async demonstratePlatformCompatibility(): Promise<{
    platform: string;
    databaseType: string;
    isCompatible: boolean;
    features: string[];
  }> {
    console.log('🧪 Verifica compatibilità piattaforma...');

    const platformInfo = this._hybridDbService.getPlatformInfo();
    const isAvailable = await this._hybridDbService.isDatabaseAvailable();
    
    const result = {
      platform: platformInfo.platform,
      databaseType: platformInfo.isWeb ? 'IndexedDB' : 'SQLite',
      isCompatible: isAvailable,
      features: [] as string[]
    };

    // Test funzionalità disponibili
    try {
      // Test creazione tabella
      await this._hybridDbService.createTable('test_compatibility', [], false);
      result.features.push('Creazione tabelle');

      // Test operazioni CRUD
      await this._hybridDbService.addRecord('test_compatibility', ['id'], ['test']);
      result.features.push('Operazioni CRUD');

      // Test query complesse
      await this._hybridDbService.getAll(['test_compatibility']);
      result.features.push('Query complesse');

      // Cleanup
      await this._hybridDbService.deleteRecord('test_compatibility', [{ key: 'id', value: 'test' }]);

      console.log('✅ Test compatibilità completato:', result);
    } catch (error) {
      console.error('❌ Errore durante test compatibilità:', error);
      result.isCompatible = false;
    }

    return result;
  }

  /**
   * Esempio 4: Gestione errori e fallback
   * Dimostra come il sistema gestisce gli errori
   */
  async demonstrateErrorHandling(): Promise<void> {
    console.log('⚠️ Dimostrazione gestione errori...');

    try {
      // Test con dati non validi
      console.log('🧪 Test inserimento dati non validi...');
      
      try {
        await this._hybridDbService.addRecord('nonexistent_table', ['id'], [null]);
      } catch (error) {
        console.log('✅ Errore gestito correttamente:', error.message);
      }

      // Test disponibilità database
      console.log('🧪 Test disponibilità database...');
      const isAvailable = await this._hybridDbService.isDatabaseAvailable();
      console.log('📊 Database disponibile:', isAvailable);

      // Test informazioni piattaforma
      console.log('🧪 Test informazioni piattaforma...');
      const platformInfo = this._hybridDbService.getPlatformInfo();
      console.log('📊 Informazioni piattaforma:', platformInfo);

      console.log('✅ Test gestione errori completato');
    } catch (error) {
      console.error('❌ Errore durante test gestione errori:', error);
    }
  }

  /**
   * Esegue tutti gli esempi in sequenza
   */
  async runAllExamples(): Promise<void> {
    console.log('🚀 Avvio di tutti gli esempi del sistema unificato...');

    try {
      await this.demonstrateCRUDOperations();
      await this.demonstratePlatformCompatibility();
      await this.demonstrateErrorHandling();
      
      // La sincronizzazione viene eseguita per ultima perché richiede connessione
      console.log('ℹ️ Per testare la sincronizzazione, chiamare demonstrateSyncWithStoreUpdate() separatamente');
      
      console.log('🎉 Tutti gli esempi completati con successo!');
    } catch (error) {
      console.error('❌ Errore durante l\'esecuzione degli esempi:', error);
      throw error;
    }
  }

  /**
   * Ottiene statistiche del database corrente
   */
  async getDatabaseStats(): Promise<{
    platform: string;
    databaseType: string;
    tablesCount: number;
    categoriesCount: number;
    productsCount: number;
  }> {
    const platformInfo = this._hybridDbService.getPlatformInfo();
    
    try {
      const [categories, products] = await Promise.all([
        this._hybridDbService.getAll(['categories']),
        this._hybridDbService.getAll(['products'])
      ]);

      return {
        platform: platformInfo.platform,
        databaseType: platformInfo.isWeb ? 'IndexedDB' : 'SQLite',
        tablesCount: 2, // Semplificato per l'esempio
        categoriesCount: categories.length,
        productsCount: products.length
      };
    } catch (error) {
      console.error('❌ Errore durante il recupero delle statistiche:', error);
      return {
        platform: platformInfo.platform,
        databaseType: platformInfo.isWeb ? 'IndexedDB' : 'SQLite',
        tablesCount: 0,
        categoriesCount: 0,
        productsCount: 0
      };
    }
  }
}
