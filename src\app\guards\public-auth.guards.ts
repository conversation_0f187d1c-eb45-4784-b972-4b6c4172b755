import { Injectable } from '@angular/core';
import { CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot, UrlTree, Router, CanActivateChild, NavigationExtras } from '@angular/router';
import { Observable } from 'rxjs';
import { JWTTokenService } from '../service/jwttoken.service';

@Injectable({
  providedIn: 'root'
})
export class PublicAuthGuard implements CanActivate, CanActivateChild {

  constructor(private _router: Router, private jwtService: JWTTokenService) { }

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) : Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    if (this.jwtService.getIss() === 'datacol') {
      if (this.jwtService.isTokenExpired()) {
        this._router.navigate(['/login'], { queryParams: { previusUrl: state.url}});
        return false;
      } else {
        // return true;
        const isFirstAccess = !localStorage.getItem("isFirstUpdate") || localStorage.getItem("isFirstUpdate") === "true";
        if( isFirstAccess && !state.url.includes('syncro')) {
          this._router.navigate(['/private/syncro']);
          return true
        }
        else
          return true;
      }
    }
    // not logged in so redirect to login page with the return url
    this._router.navigate(['/login']);
    return false;
  }

  canActivateChild(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) : Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    return this.canActivate(route, state);
  }
}
