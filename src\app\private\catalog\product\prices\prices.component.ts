import { animate, style, transition, trigger } from '@angular/animations';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { PriceRow, Product } from 'src/app/service/data/product';
import Utils from 'src/app/shared/utils';
import { IonicModule, LoadingController, Platform, PopoverController, ToastController } from '@ionic/angular';
import { LocalStorageService } from 'src/app/service/local-storage.service';
import { InfoDataList as AgentInfoDataList } from 'src/app/service/data/login-response';
import { InfoDataList as CustomerInfoDataList } from 'src/app/service/data/customer';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { select, Store } from '@ngrx/store';
import { BehaviorSubject } from 'rxjs';
import { DbService } from 'src/app/shared/db.service';
import { Cart } from 'src/app/service/data/cart';
import { resetCart, setCart } from 'src/app/store/actions/cart.actions';
import { QuantityPopoverComponent } from 'src/app/private/quantity-popover/quantity-popover.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { DiscountsComponent } from './discounts/discounts.component';
import { TabsComponent } from '../tabs/tabs.component';
import { TabComponent } from '../tabs/tab/tab.component';
import { FilterReferencesPipe } from 'src/app/shared/filter-references-pipe';
import { ProductQuantityPipe } from 'src/app/shared/product-quantity';
import { InCartPipe } from 'src/app/shared/in-cart';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';
import { ImageOfflineService } from 'src/app/service/syncro-v2/image-offline.service';

@Component({
    selector: 'app-prices',
    templateUrl: './prices.component.html',
    styleUrls: ['./prices.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [
        trigger('inOutAnimation', [
            transition(':enter', [
                style({ height: 0, opacity: 0 }),
                animate('1s ease-out', style({ height: 150, opacity: 1 }))
            ]),
            transition(':leave', [
                style({ height: 150, opacity: 1 }),
                animate('1s ease-in', style({ height: 0, opacity: 0 }))
            ])
        ])
    ],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      DiscountsComponent,
      TabsComponent,
      TabComponent,
      FilterReferencesPipe,
      ProductQuantityPipe,
      InCartPipe,
      ZeroRemoverPipe,
      SafePipe
    ]   
})
export class PricesComponent implements OnInit {

  catalogService = inject(CatalogService);

  @Input() datacolCategoryName: string;
  @Input() products: Product[];
  @Output() closePopover = new EventEmitter();
  @Output() addToCart = new EventEmitter();
  cart: BehaviorSubject<Cart> = new BehaviorSubject<Cart>(null);
  isDiscountShown: boolean = false;
  selectedDiscountList: string;
  selectedProductValueZR00: string;
  selectedProductCode: string;
  selectedProductName: string;
  selectedProductGp: string;
  selectedSalesOrganization: string;
  salesOrganization = !!this._lsService.get("catalogName") ? this._lsService.get("catalogName").substring(2, 6) : null;
  infoDataList_agent: { distributionChannel: string, division: string, salesOrganization: string, active: boolean }[] =
    Array.from(new Set(((!!this._lsService.get("infoDataList_agent") ? JSON.parse(this._lsService.get("infoDataList_agent")) : null) as AgentInfoDataList[]).map(x => { return { distributionChannel: x.distributionChannel, division: x.division, salesOrganization: x.salesOrganization, active: false } }).map(item => JSON.stringify(item))))
      .map(item => JSON.parse(item));
  infoDataList_customer: CustomerInfoDataList[] = (!!this._lsService.get("infoDataList_customer") ? JSON.parse(this._lsService.get("infoDataList_customer")) : null) as CustomerInfoDataList[];
  productsMatrix:
    {
      tab: string, product: Product,
      L1: { price: string, priceValue: string, gp: string, unitFactor: string },
      L2: { price: string, priceValue: string, gp: string, unitFactor: string },
      L3: { price: string, priceValue: string, gp: string, unitFactor: string },
      L4: { price: string, priceValue: string, gp: string, unitFactor: string }
    }[] = [];
  imageDirectory: string = localStorage.getItem("imageDirectory");
  // divisions: {title:string, active:boolean}[] = this.infoDataList_agent.map(x => {return {title: x.division, active: false}}); 
  // ['CO', 'IM', 'LE', 'ME', 'AP', 'NA', 'SA', 'CH', 'CA', 'AL'];

  constructor(private _platform: Platform, private _lsService: LocalStorageService, private _dbService: DbService,
    private _cdr: ChangeDetectorRef,
    private _toastController: ToastController, private _translate: TranslateService, private _store: Store<any>,
    private popoverController: PopoverController, public _loadingController: LoadingController,
    private _imageOfflineService: ImageOfflineService) {
      addIcons({ closeOutline });
    }

  async ngOnInit() {
    
    this.products = await Promise.all((this.products as Product[]).map(async (item) => {
      if (!!item.image) {
        item.image = await Utils.resolveImageUrlWithOffline(
          this._imageOfflineService,
          this._platform,
          this.imageDirectory,
          item.image
        );
      }
      else
        item.image = null;
      return item;
    }));
    this.productsMatrix = [];
    this.infoDataList_agent = Array.from(new Set(((!!this._lsService.get("infoDataList_agent") ? JSON.parse(this._lsService.get("infoDataList_agent")) : null) as AgentInfoDataList[]).map(x => { return { distributionChannel: x.distributionChannel, division: x.division, salesOrganization: x.salesOrganization, active: false } }).map(item => JSON.stringify(item)))).map(item => JSON.parse(item));
    this.infoDataList_customer = (!!this._lsService.get("infoDataList_customer") ? JSON.parse(this._lsService.get("infoDataList_customer")) : null) as CustomerInfoDataList[];
    await this.loadCart();

    this.setMatrix();
  }

  setMatrix() {
    
    if (this.infoDataList_agent.length === 0) {
      Utils.showSnackWithColor('toast-error-class', this._toastController, this._translate.instant('CARTS.CARTS_NOT_ALL_TRANSFERED'));
      return;
    }
    this.infoDataList_agent[0].active = true; // attiviamo di default il primo
    if (!!this.products)
      this.products.forEach((product) => {
        let row: {
          tab: string | null, product: Product,
          L1: { price: string, priceValue: string, gp: string, unitFactor: string },
          L2: { price: string, priceValue: string, gp: string, unitFactor: string },
          L3: { price: string, priceValue: string, gp: string, unitFactor: string },
          L4: { price: string, priceValue: string, gp: string, unitFactor: string }
        } = this.inizilizeRow(product);
        let priceList4SingleCustomer = false;
        let priceList4Group = false;

        const default_infoDataList_agent = this.infoDataList_agent[0];

        // Verifico se ho un listino personalizzato per il cliente
        const priceListBase: PriceRow[] = this.pricelistByCustomer(product).filter((item: PriceRow) => item.conditionType === 'ZR00');
        // const priceListGp: PriceRow[] =  this.pricelistByCustomer(product).filter((item:PriceRow) =>item.conditionType === 'ZGP0');
        const priceListGp: PriceRow[] = Utils.objToJson(product.priceRows).filter((item: PriceRow) =>
          item.salesOrganization === default_infoDataList_agent.salesOrganization &&
          item.division === default_infoDataList_agent.division &&
          item.distributionChannel === default_infoDataList_agent.distributionChannel &&
          item.conditionType === 'ZGP0' && item.priceList === 'L1');
        priceList4SingleCustomer = (!!priceListBase && !!priceListGp && priceListBase.length > 0 && priceListGp.length > 0);
        if (priceList4SingleCustomer) {
          const currentDivision = default_infoDataList_agent.division;
          this.infoDataList_agent = this.infoDataList_agent.slice(0, 1);
          row.tab = currentDivision;
          // Se ho un listino personalizzato per cliente setto L1, non setterò L2, L3, L4
          row.L1.price = priceListBase[0].formattedValue;
          row.L1.priceValue = priceListBase[0].value;
          row.L1.gp = priceListGp[0].gp;
          row.L1.unitFactor = priceListBase[0].unitFactor;
        }
        else {
          // Altrimenti, verifico se il cliente appartiene ad un gruppo di acquisto
          const customerGroup = this.checkMatchCustomerGroup(default_infoDataList_agent);
          if (!!product.priceRows && customerGroup.length > 0 && !!customerGroup[0].userPriceGroupCode) {
            // cerca listini personalizzati per gruppo cliente
            const priceListBase: PriceRow[] = Utils.objToJson(product.priceRows).filter((item: PriceRow) =>
              item.salesOrganization === default_infoDataList_agent.salesOrganization &&
              item.distributionChannel === default_infoDataList_agent.distributionChannel &&
              item.conditionType === 'ZR00' &&
              item.customerPriceList === customerGroup[0].userPriceGroupCode);
            const priceListGp: PriceRow[] = Utils.objToJson(product.priceRows).filter((item: PriceRow) =>
              item.salesOrganization === default_infoDataList_agent.salesOrganization &&
              item.division === default_infoDataList_agent.division &&
              item.distributionChannel === default_infoDataList_agent.distributionChannel &&
              item.conditionType === 'ZGP0' && item.priceList === 'L1');
            if (!!priceListBase && priceListBase.length > 0) // in questo caso il listino è solo L1, non ci sono gli altri.
            {
              this.infoDataList_agent = this.infoDataList_agent.slice(0, 1);
              priceList4Group = true;
              row.tab = default_infoDataList_agent.division;
              row.L1.price = priceListBase[0].formattedValue;
              if (row.L1.price !== '-') {
                row.L1.priceValue = priceListBase[0].value;
                row.L1.gp = priceListGp[0].gp;
                row.L1.unitFactor = priceListBase[0].unitFactor;
              }
            }
          }
        }
        if (!priceList4SingleCustomer && !priceList4Group) {
          // Se non ho listino personalizzato per singolo cliente e il cliente non appartiene a un gruppo clienti
          // carico tutte le division dell'agente
          // Carico solo il primo tab
          const currentDivision = this.infoDataList_agent[0].division;
          let row = this.populateDivision(currentDivision, product, this.infoDataList_agent[0]);
          this.productsMatrix = [...this.productsMatrix, row]; // Adding an element
        }
      });
      this._cdr.markForCheck();
  }

  private inizilizeRow(product: Product) {
    return {
      tab: null, product: product,
      L1: { price: '-', priceValue: '0', gp: '-', unitFactor: '-' },
      L2: { price: '-', priceValue: '0', gp: '-', unitFactor: '-' },
      L3: { price: '-', priceValue: '0', gp: '-', unitFactor: '-' },
      L4: { price: '-', priceValue: '0', gp: '-', unitFactor: '-' }
    };
  }

  private populateDivision(division: string, product: Product, infoDataList_agent: AgentInfoDataList) {
    let row = this.inizilizeRow(product);
    row.tab = division;
    row.L1.price = this.price(product, 'L1', infoDataList_agent, true);
    if (row.L1.price !== '-') {
      row.L1.priceValue = this.value(product, 'L1', infoDataList_agent, true);
      row.L1.gp = this.gp(product, 'L1', infoDataList_agent, true);
      row.L1.unitFactor = this.unitFactor(product, 'L1', infoDataList_agent, true);
    }

    row.L2.price = this.price(product, 'L2', infoDataList_agent, true);
    if (row.L2.price !== '-') {
      row.L2.priceValue = this.value(product, 'L2', infoDataList_agent, true);
      row.L2.gp = this.gp(product, 'L2', infoDataList_agent, true);
      row.L2.unitFactor = this.unitFactor(product, 'L2', infoDataList_agent, true);
    }

    row.L3.price = this.price(product, 'L3', infoDataList_agent, true);
    if (row.L3.price !== '-') {
      row.L3.priceValue = this.value(product, 'L3', infoDataList_agent, true);
      row.L3.gp = this.gp(product, 'L3', infoDataList_agent, true);
      row.L3.unitFactor = this.unitFactor(product, 'L3', infoDataList_agent, true);
    }

    row.L4.price = this.price(product, 'L4', infoDataList_agent, true);
    if (row.L4.price !== '-') {
      row.L4.priceValue = this.value(product, 'L4', infoDataList_agent, true);
      row.L4.gp = this.gp(product, 'L4', infoDataList_agent, true);
      row.L4.unitFactor = this.unitFactor(product, 'L4', infoDataList_agent, true);
    }
    return row;
  }

  adjustPopoverPosition(keyboardHeight: number) {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', `--offset-y: -${keyboardHeight / 2}px !important;`);
    }
  }

  resetPopoverPosition() {
    const popoverElement = document.querySelector('.quantity-popover');
    if (popoverElement) {
      popoverElement.setAttribute('style', '');
    }
  }


  private async loadCart() {
    if (!!this.catalogService.userUid && !this.catalogService.isProspect) {
      await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data) => {
        if (!!data && data.length > 0 && !!data[0]) {
          var cart = data[0];
          cart.products = JSON.parse(cart.products);
          this.cart.next({ ...cart });
        } else {
          this.cart.next(null);
        }
      });
    }
  }

  close() {
    this.productsMatrix = []
    this.closePopover.emit();
  }

  price(item: Product, type: string, infoDataList_agent: AgentInfoDataList, force: boolean = false) {
    let price = '-';
    try {
      if (!!item.priceRows && (force || this.checkMatchBase(infoDataList_agent))) {
        //  Se trovato il match tra agente e customer, si prosegue il confronto a livello di price, se non corrisponde si ritorna "-"
        const priceList: PriceRow[] = Utils.objToJson(item.priceRows).filter((item: PriceRow) => item.salesOrganization === infoDataList_agent.salesOrganization && item.priceList === type && item.division === infoDataList_agent.division && item.distributionChannel === infoDataList_agent.distributionChannel && item.conditionType === 'ZR00');
        if (!!priceList && priceList.length > 0) {
          price = priceList[0].formattedValue;
        }
      }
    } catch (err) {
      console.log(err)
    }
    return price
  }

  value(item: Product, type: string, infoDataList_agent: AgentInfoDataList, force: boolean = false) {
    let price = '-';
    if (!!item.priceRows && (force || this.checkMatchBase(infoDataList_agent))) {
      //  Se trovato il match tra agente e customer, si prosegue il confronto a livello di price, se non corrisponde si ritorna "-"
      const priceList: PriceRow[] = Utils.objToJson(item.priceRows).filter((item: PriceRow) => item.salesOrganization === infoDataList_agent.salesOrganization && item.priceList === type && item.division === infoDataList_agent.division && item.distributionChannel === infoDataList_agent.distributionChannel && item.conditionType === 'ZR00');
      if (!!priceList && priceList.length > 0) {
        price = priceList[0].value;
      }
    }
    return price
  }

  private checkMatchBase(infoDataList_agent: AgentInfoDataList) {
    // Si confronta il dato infoDataList a livello di agente (il primo della lista ritornato dal servizio login)
    //  con quello a livello di customer (ritornato dal servizio allCustomerByUser)
    if (!!this.infoDataList_customer) {
      const match = this.infoDataList_customer.filter(x => x.distributionChannel === infoDataList_agent.distributionChannel &&
        x.division === infoDataList_agent.division &&
        x.salesOrganization === infoDataList_agent.salesOrganization);
      return ((!!this.infoDataList_agent) && (!!this.infoDataList_customer) && !!match && match.length > 0);
    }
    else // in caso di prospect si procede col resto dei controlli dando questo per ok
      return true;
  }

  private pricelistByCustomer(item: Product) {
    // cerca listini personalizzati per cliente
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();
    return Utils.objToJson(item.priceRows).filter((item: PriceRow) => !!item.b2bUnitCode && item.b2bUnitCode === currentCustomer.uid);
  }

  private checkMatchCustomerGroup(infoDataList_agent: AgentInfoDataList): CustomerInfoDataList[] {
    if (!!this.infoDataList_customer) {
      const match = this.infoDataList_customer.filter(x => x.distributionChannel === infoDataList_agent.distributionChannel &&
        x.division === infoDataList_agent.division &&
        x.salesOrganization === infoDataList_agent.salesOrganization);

      return match
    }
    else
      return [];
  }

  unitFactor(item: Product, type: string, infoDataList_agent: AgentInfoDataList, force: boolean = false) {
    let unitFactor = '-';
    if (!!item.priceRows && (force || this.checkMatchBase(infoDataList_agent))) {
      //  Se trovato il match tra agente e customer, si prosegue il confronto a livello di price, se non corrisponde si ritorna "-"
      const priceList: PriceRow[] = Utils.objToJson(item.priceRows).filter((item: PriceRow) => item.salesOrganization === infoDataList_agent.salesOrganization && item.priceList === type && item.division === infoDataList_agent.division && item.distributionChannel === infoDataList_agent.distributionChannel && item.conditionType === 'ZR00');
      if (!!priceList && priceList.length > 0) {
        unitFactor = priceList[0].unitFactor;
      }
    }
    return unitFactor;
  }

  gp(item: Product, type: string, infoDataList_agent: AgentInfoDataList, force: boolean = false) {
    let gp = '-';
    if (!!item.priceRows && (force || this.checkMatchBase(infoDataList_agent))) {
      //  Se trovato il match tra agente e customer, si prosegue il confronto a livello di price, se non corrisponde si ritorna "-"
      const priceList: PriceRow[] = Utils.objToJson(item.priceRows).filter((item: PriceRow) => item.salesOrganization === infoDataList_agent.salesOrganization && item.priceList === type && item.division === infoDataList_agent.division && item.distributionChannel === infoDataList_agent.distributionChannel && item.conditionType === 'ZGP0');
      if (!!priceList && priceList.length > 0 && !!priceList[0].gp) {
        gp = priceList[0].gp;
      }
    }
    return gp;
  }


  async showMultipleQuantity(product: Product) {
    if (document.querySelector('.quantity-popover') == null) {
      const popover = await this.popoverController.create({
        component: QuantityPopoverComponent,
        event: event,
        cssClass: 'quantity-popover',
        translucent: true,
        componentProps: { product }
      });
      popover.onDidDismiss().then((data) => {
        if (!!data.data && !!data.data.newQuantity)
          this.addOneToCart(product, +data.data.newQuantity);
        this.addToCart.emit();
      });

      return await popover.present();
      this.addToCart.emit();
    }
  }

  async addOneToCart(product: Product, multiQuantity?: number) {
    if (!this.catalogService.isProspect) {

      var cart: Cart = Object.assign({}, this.cart.getValue() as Cart);
      if (!!cart && !!cart.products) {
        cart.products = Utils.objToJson(cart.products);
        const index = cart.products.findIndex(item => item.idProduct === product.code);
        if (index < 0) {
          cart.products.push({ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: +product.minimumDeliveryQuantity });
        } else {
          cart.products[index].quantity = multiQuantity ? multiQuantity : (cart.products[index].quantity + +product.minimumDeliveryQuantity);
        }
        await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(_ => {
          this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
          this.cart.next({ ...cart });
          this.addToCart.emit();
        });
      } else {
        var productsInCart = [{ idProduct: product.code, quantity: (multiQuantity ? multiQuantity : +product.minimumDeliveryQuantity), description: product.name, image: product.image, minimumDeliveryQuantity: product.minimumDeliveryQuantity }];
        await this._dbService.insertOrReplace("carts", [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }],
          ['id', 'agentCode', 'idCustomer', 'products'],
          [this._lsService.get("customerAgentCode"), this.catalogService.userUid, JSON.stringify(productsInCart)]).then(async _ => {
            var cartId = -1;
            await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data: Cart[]) => {
              if (!!data[0] && !!data[0]['id']) {
                this.cart.next({ ...data[0] });
                const rows = data[0];
                cartId = rows.id;
              }
            });
            if (cartId > 0)
              this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: cartId, currentQuantityInCart: (productsInCart.length) } }));
      
            this.addToCart.emit();
          }
          );
      }
      this.presentToast(this._translate.instant("PRODUCT.ITEM_ADDED") + product.code.replace(/^0+/, ''));
      this.addToCart.emit();

    }
  }

  private async presentToast(msg: string) {
    Utils.showSnack(this._toastController, msg, this._translate.instant("SETTINGS.DONE"));
  }

  async removeQuantity(product: Product) {
    var cart: Cart = this.cart.getValue() as Cart;
    cart.products = Utils.objToJson(cart.products);
    const index = cart.products.findIndex(item => item.idProduct === product.code);
    if (index >= 0) {
      const toDelete = +product.minimumDeliveryQuantity === cart.products[index].quantity;
      if (toDelete)
        this.removeProduct(product);
      else {
        if (cart.products[index].quantity > 0) {
          cart.products[index].quantity = cart.products[index].quantity - +product.minimumDeliveryQuantity;
          await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(cart.products) }]).then(_ => {
            this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: +cart.id, currentQuantityInCart: (cart.products.length) } }));
            this.cart.next({ ...cart });
            this.addToCart.emit();
            this._cdr.markForCheck();
          });
        }
      }
      this._cdr.markForCheck();
    }
  }

  async removeProduct(product: Product) {
    var cart: Cart = this.cart.getValue() as Cart;
    cart.products = Utils.objToJson(cart.products);
    const tmpProducts = cart.products.filter((item) => item.idProduct !== product.code);
    if (tmpProducts.length > 0) {
      await this._dbService.updateRecord('carts', [{ key: 'id', value: cart.id }], [{ key: 'products', value: JSON.stringify(tmpProducts) }]).then(async _ => {
        cart.products = tmpProducts;
        await this._dbService.getRecordsByANDCondition('carts', [{ key: 'agentCode', value: this._lsService.get("customerAgentCode") }, { key: 'idCustomer', value: this.catalogService.userUid }]).then(async (data: Cart[]) => {
          if (!!data[0] && !!data[0]['id']) {
            const rows = data[0];
            this._store.dispatch(setCart({ cart: { idCustomer: this.catalogService.userUid, idCart: rows.id, currentQuantityInCart: (tmpProducts.length) } }));
            this.cart.next({ ...cart });
            this.addToCart.emit();
          }
          this._cdr.markForCheck();
        });
      });
    } else {
      this.deleteCart(cart.id);
      this._cdr.markForCheck();
    }
  }

  private async deleteCart(cartId: number) {
    await this._dbService.deleteRecord('carts', [{ key: 'id', value: cartId }]).then(_ => {
      Utils.showSnack(this._toastController, this._translate.instant('CARTS.CART_EMPTY'));
      this._store.dispatch(resetCart());
      this.cart.next(null);
      this.addToCart.emit();
      this._cdr.markForCheck();
    });
  }

  showDiscount(event, discountList: string, item: Product, gp: string, value: string, infoDataList_agent: AgentInfoDataList) {
    event.stopPropagation();
    if (!!value && value !== "-" && !!gp && gp !== "-") {
      this.selectedDiscountList = discountList;
      this.selectedProductName = item.name;
      this.selectedProductCode = item.code;
      this.selectedProductValueZR00 = value;
      this.selectedProductGp = gp;
      this.selectedSalesOrganization = infoDataList_agent.salesOrganization;
      this.isDiscountShown = true;
    } else {
      Utils.showSnack(this._toastController, this._translate.instant('PRICES.NO_DISCOUNT'), 'OK', true);
    }
    this._cdr.markForCheck();
  }

  hideDiscount() {
    this.isDiscountShown = false;
  }

  selectDivision = async (division) => {
    this.infoDataList_agent.filter(x => x.division === division)[0].active = true;
    const infoDataList_agent = this.infoDataList_agent.filter(x => x.division === division)[0];

    let loading = await this._loadingController.create({
      message: this._translate.instant('GENERICS.WAIT'),
    });
    await loading.present();

    // i dati vengono calcolati al cambio tab
    if (this.productsMatrix.filter(item => item.tab === infoDataList_agent.division).length === 0) {
      this.products.forEach((product) => {
        const row = this.populateDivision(infoDataList_agent.division, product, infoDataList_agent)
        this.productsMatrix = [...this.productsMatrix, row];
      });
    }
    await loading.dismiss();
    this._cdr.markForCheck();
  }
}
