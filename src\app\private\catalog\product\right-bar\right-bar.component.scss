.right-bar {
    width: 65px;
    display: flex;
    flex-direction: column;
    margin-top: 5px;
    position: absolute;
    right: 0;
    .cell {
        height: 70px;
        padding: 3px;
        justify-content: center;
        align-items: baseline;
        display: flex;
        button {
            background-color: transparent;
            &:active {
                transform: translateY(5px);
            }
            object {
                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
            }
            &.on object svg {
                stroke: var(--ion-dat-red);
                fill: var(--ion-dat-red);
            }
        }
    }
}
