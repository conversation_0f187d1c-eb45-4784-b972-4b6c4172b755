import { Component } from '@angular/core';
import { IonApp, IonRouterOutlet } from '@ionic/angular/standalone';
import { TranslateService } from '@ngx-translate/core';
import { register } from 'swiper/element/bundle';

register();

@Component({
    selector: 'app-root',
    templateUrl: 'app.component.html',
    styleUrls: ['app.component.scss'],
    standalone: true,
    imports: [
      IonApp,
      IonRouterOutlet,
    ]
})
export class AppComponent {

  constructor(private _translate: TranslateService) {
    this._translate.addLangs(['en', 'fr', 'de', 'es', 'pl', 'it']);
    this._translate.setDefaultLang('it');
  
    const savedLang = localStorage.getItem('language');
    const browserLang = this._translate.getBrowserLang();
  
    if (savedLang) {
      this._translate.use(savedLang);
    } else {
      const langToUse = browserLang?.match(/en|it|fr|es|pl|de/) ? browserLang : 'it';
      this._translate.use(langToUse);
    }
  }
}
