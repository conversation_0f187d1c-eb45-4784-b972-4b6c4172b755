import { Directive, ElementRef, EventEmitter, Output } from '@angular/core';
import { fromEvent, merge, of, Subscription, timer } from 'rxjs';
import { filter, map, switchMap, takeUntil } from 'rxjs';

@Directive({
    selector: '[longPress]',
    standalone: true
})
export class LongPressDirective {

  private eventSubscribe: Subscription;
  private isEmitted: boolean = false; // Flag to track if event was emitted
  threshold = 500;

  @Output()
  mouseLongPress = new EventEmitter();

  constructor(private elementRef: ElementRef) {
    const mousedown = fromEvent<MouseEvent>(
      elementRef.nativeElement,
      'mousedown'
    ).pipe(
      filter((event) => event.button == 0), // Only allow left button (Primary button)
      map((event) => true) // turn on threshold counter
    );
    const touchstart = fromEvent(elementRef.nativeElement, 'touchstart').pipe(
      map(() => true)
    );
    const touchEnd = fromEvent(elementRef.nativeElement, 'touchend').pipe(
      map(() => false)
    );
    const mouseup = fromEvent<MouseEvent>(window, 'mouseup').pipe(
      filter((event) => event.button == 0), // Only allow left button (Primary button)
      map(() => false) // reset threshold counter
    );

    const cancelEvents = merge(mouseup, touchEnd);

    this.eventSubscribe = merge(mousedown, touchstart)
      .pipe(
        switchMap((state) => {
          if (state) {
            this.isEmitted = false; // Reset the emitted flag on start
            return timer(this.threshold).pipe(
              takeUntil(cancelEvents)
            );
          } else {
            return of(null);
          }
        }),
        filter(() => !this.isEmitted)
      )
      .subscribe(() => {
        this.mouseLongPress.emit();
        this.isEmitted = true; // Set flag to true after emitting
      });
  }

  ngOnDestroy(): void {
    if (this.eventSubscribe) {
      this.eventSubscribe.unsubscribe();
    }
  }
}
