<ng-container *ngIf="{ myProducts: products$ | async } as productData">
  <div class="container" (click)="showNavigator = false" >
    <div class="row category">
      <div class="col">
        <swiper-container #slides pager="true">
          <swiper-slide>
            <div class="product-image" (click)="openImageViewer(resolvedDatacolCategoryImage)">
              <div style="display: none;">{{resolvedDatacolCategoryImage}}</div>
              <pinch-zoom [doubleTap]="false" [draggableImage]="false">
                <img [src]="resolvedDatacolCategoryImage | safe" width="370" height="370" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_740x740.png'">
              </pinch-zoom>
            </div>
            <div class="image-desc" *ngIf="false">F340 374 o 50 mm</div>
          </swiper-slide>
          <ng-container *ngIf="loadOtherImages && !!datacolCategoryImage">
            <swiper-slide *ngFor="let media of datacolCategoryImage">
              <div class="product-image" (click)="openImageViewer(media.url)">
                <pinch-zoom [doubleTap]="false" [draggableImage]="false">
                  <img [src]="media.url | safe" width="370" height="370" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_740x740.png'">
                </pinch-zoom>
              </div>
              <div class="image-desc" *ngIf="!!media.shortCaption">{{media.shortCaption}}</div>
            </swiper-slide>
          </ng-container>
        </swiper-container>
      </div>
      <div class="col">
        <ng-container *ngIf="!isInfoShown">
          <div class="name" [innerHTML]="datacolCategory.name"></div>
          <div class="products">
            <table>
              <thead>
                <tr>
                  <th scope="col" width="50px"></th>
                  <th scope="col"></th>
                  <th scope="col">{{"PRODUCT.ITEM" | translate}}</th>
                  <th scope="col"></th>
                  <th scope="col">{{"PRODUCT.DESCRIPTION" | translate}}</th>
                </tr>
              </thead>
              <tbody *ngIf="!!productData.myProducts">
                <tr *ngFor="let item of productData.myProducts; let i = index; trackBy: identify">
                  <td longPress data-label="" (click)="addOneToCart(item)" (mouseLongPress)="showMultipleQuantity(item)">
                    <div class="product-min-quantity"
                      [ngClass]="{'product-quantity-in-cart' : item.inCart}">
                      {{ !item.inCart ? item.minimumDeliveryQuantity : (cart|async | productQuantity:item.code) }}
                    </div>
                    <svg xmlns="http://www.w3.org/2000/svg" width="61.894" height="61.281" viewBox="0 0 61.894 61.281"
                    [ngClass]="{'inCart': (item.inCart)}">
                      <g id="Raggruppa_5225" data-name="Raggruppa 5225" transform="translate(1.701 -0.462)">
                        <path id="Tracciato_4685" data-name="Tracciato 4685" d="M37.084,8,29.1,23.581Z" transform="translate(15.018 3.884)" fill="none"/>
                        <g id="Raggruppa_4838" data-name="Raggruppa 4838" transform="translate(-1.701 0.462)">
                          <path id="Tracciato_4817" data-name="Tracciato 4817" d="M49.559,34.8,61.894,22.848V9.842a3.174,3.174,0,1,0-6.331,0V19.919l-8.514,8.2H21.4L11.789,0H3.166A3.29,3.29,0,0,0,0,3.4,3.29,3.29,0,0,0,3.166,6.8H7.532l8.842,26.012L10.916,48.392h41.59a3.407,3.407,0,0,0,0-6.8H20.085l2.292-6.913H49.559Z" fill="#9c9d9f"/>
                          <ellipse id="Ellisse_1" data-name="Ellisse 1" cx="5.24" cy="5.624" rx="5.24" ry="5.624" transform="translate(12.226 50.032)" fill="#9c9d9f"/>
                          <ellipse id="Ellisse_2" data-name="Ellisse 2" cx="5.24" cy="5.624" rx="5.24" ry="5.624" transform="translate(42.354 50.032)" fill="#9c9d9f"/>
                        </g>
                      </g>
                    </svg>
                  </td>
                  <td data-label="" (click)="showArticle(item)">
                    <div style="display: none;">{{productsImage[i]}}</div>
                    <img [src]="productsImage[i] | safe" width="60" height="60" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
                  </td>
                  <td [attr.data-label]="itemLabel" (click)="openArticleWithAttributes(item)" [ngClass]="{'focusProd': item.focus === 'S'}">
                    <div>{{item.code | zeroRemover}}</div>
                    <div *ngIf="item.divisionStatusCode === 'Z3'" class="z3"></div>
                  </td>
                  <td data-label="" class="buttons">
                    <button *ngIf="loadOtherImages && !!item.mediaVideoURL && item.mediaVideoURL !== 'null' && item.mediaVideoURL.length > 0" (click)="openVideo(item)" >
                      <object type="image/svg+xml" data="../../assets/svg/video.svg">
                        <img src="../../assets/svg/video.svg" />
                      </object>
                    </button>
                    <button *ngIf="item.documents.length > 0" (click)="openPdf(item)">
                      <object type="image/svg+xml" data="../../assets/svg/pdf.svg">
                        <img src="../../assets/svg/pdf.svg" />
                      </object>
                    </button>
                    <button (click)="share(item)">
                      <object type="image/svg+xml" data="../../assets/svg/black-share.svg">
                        <img src="../../assets/svg/black-share.svg" />
                      </object>
                    </button>
                  </td>
                  <td [attr.data-label]="descriptionLabel" [innerHTML]="item.name"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </ng-container>
        <ng-container *ngIf="isInfoShown">
          <div class="name" [innerHTML]="datacolCategory.name"></div>
          <div class="description" [innerHTML]="datacolCategory.description"></div>
        </ng-container>
      </div>
    </div>
    <div class="row references" *ngIf="{ myReferences: references$ | async, myReferencesTabs: referencesTabs$ | async } as data">
      <app-tabs *ngIf="!!data.myReferencesTabs && data.myReferencesTabs.length > 0">
        <app-tab [tabTitle]="tab" *ngFor="let tab of data.myReferencesTabs">
          <swiper-container #slides class="sliding" [options]="slideOpts" >
            <swiper-slide class="suggested-product" *ngFor="let item of data.myReferences | filterReferences:tab ; let i = index;" (click)="goToReference(item)">
              <div class="box" [ngClass]="{'focusProd': item.focus === 'S'}">
                <img [src]="referencesImage[i] | safe" width="100" height="100" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
                <br/><span [innerHTML]="item.name"></span>
                <div *ngIf="item.divisionStatusCode === 'Z3'" class="z3"></div>
              </div>
            </swiper-slide>
          </swiper-container>
        </app-tab>
      </app-tabs>
    </div>
  </div>
</ng-container>