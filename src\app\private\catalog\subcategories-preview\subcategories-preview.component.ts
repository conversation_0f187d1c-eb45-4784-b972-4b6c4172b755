import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
    selector: 'app-subcategories-preview',
    templateUrl: './subcategories-preview.component.html',
    styleUrls: ['./subcategories-preview.component.scss'],
    standalone: true
})
export class SubcategoriesPreviewComponent {
  @Input() subcategories4Preview:{ idParent:string, name: string, id: string }[] = [];
  @Output() close = new EventEmitter();
  @Output() open = new EventEmitter();

  closePreview(){
    this.close.emit();
  }

  openSubcategory(item){
    this.open.emit(item);
  }

}
