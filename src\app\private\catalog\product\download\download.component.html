<div class="popover">
  <div class="container">
    <div class="header">
      <button class="close" (click)="close()">
        <ion-icon name="close-outline" size="large"></ion-icon>
      </button>
    </div>
    <div class="row">
      <h1>{{articleCode}}</h1>
      <div class="documents">
        @for(document of documents; track $index) {
        <div class="single">
          <div class="document" (click)="openPdf(document)">
            <div>
              <object type="image/svg+xml" data="../../assets/svg/pdf.svg">
                <img src="../../assets/svg/pdf.svg" />
              </object>
            </div>
            <div class="fileName">
              {{document.documentName}}
            </div>
           </div>
        </div>
        }
      </div>
    </div>
  </div>
</div>
