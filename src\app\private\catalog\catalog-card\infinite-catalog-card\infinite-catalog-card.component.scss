@import '../_common.scss';

.card.favorite {
    outline: 4px solid var(--ion-dat-yellow);
}

#z3.zetatre {
    position: absolute;
    background-image: url('../../../../../assets/icon/icona_attenzione.png');
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-color: white;
    width: 20px;
    height: 20px;
    top: 30px;
    right: 3px;
    z-index: 100;
}

#focusProd.focusprodotto {
    position: absolute;
    background-image: url('../../../../../assets/svg/focus.svg');
    background-repeat: no-repeat;
    background-position-x: center;
    background-position-y: center;
    background-color: white;
    width: 20px;
    height: 20px;
    top: 5px;
    right: 3px;
    z-index: 100;
}

.actions-card {
    display: flex;
    flex-direction: row !important;
    justify-content: space-between;
    gap: 8px;
    padding: 8px;
    background-color: var(--ion-dat-middle-gray);
    ;

    ion-icon {
        font-size: 40px;
        width: 40px;
        background: var(--ion-dat-white);
        padding: 5px;
        border-radius: 3px;
        color: var(--ion-dat-middle-gray);
        display: block;
    }
}

.infinite>div {
    width: 180px;
    height: 180px;
    gap: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;

    .icon {
        padding: 0 !important;
        max-width: 100%;
        max-height: 100%;
        height: 123px !important;
        width: 180px !important;
        display: flex;
        justify-content: center;
    }

    >.detail {
        display: flex;
        flex-direction: column;
        height: 45px;
        padding: 0 !important;

        #buttons.buttons {
            max-height: 30px !important;
            display: flex;
            gap: 4px;
        }

        .name {
            display: flex;
            justify-content: center !important;
            align-items: baseline;
            text-align: center !important;
            width: 180px;
            height: 45px !important;
            vertical-align: middle;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            padding: 0 !important;
            text-transform: none !important;
            line-height: 15px;
        }
    }
}

.uppercase {
    text-transform: uppercase;
}

.category {
    padding: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
}

.small {
    height: 25px;
    font-size: 20px;
    padding: 0px !important
}

ion-icon {
    height: 30px;
    font-size: 25px;
    background: var(--ion-dat-middle-gray);
    padding: 5px;
    border-radius: 3px;
    color: var(--ion-dat-white);
    display: block;
}

.card.white-card div div.detail .buttons {
    display: flex;
    flex: 1;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-end;
    height: min-content !important;

    ion-icon {
        font-size: 30px;
        background: none;
        color: var(--ion-dat-middle-gray);
    }

    ion-icon[name='star-outline'] {
        font-size: 30px;
        background: none;
        color: var(--ion-dat-middle-gray);
    }

    ion-icon[name='star'] {
        font-size: 30px;
        background: none;
        color: var(--ion-dat-yellow);
    }
}

.card-arrow {
    display: flex !important;
    height: 50px !important;

}

.divider {
    display: flex !important;
    align-items: center !important;
    text-align: center !important;
    margin: 60px 0px 20px 0 !important;
}

.divider::before,
.divider::after {
    content: '' !important;
    flex: 1 !important;
    border-bottom: 2px solid var(--ion-dat-red) !important;
}

.divider:not(:empty)::before {
    margin-right: 0.5em !important;
}

.divider:not(:empty)::after {
    margin-left: 0.5em !important;
}

.divider-title {
    padding: 0.5em 1em;
    font-weight: bold;
    font-size: 150%;
}

img#img {
    width: auto !important;
    height: 100% !important;
}


.card::after {
    content: '';
    background-color: #f0f0f0;
    /* Placeholder color */
    height: 100px;

}

.detail .name {
    height: 35px !important;
}

.popover {
    display: flex;
    flex-direction: row;
    justify-content: space-evenly;
    gap: 8px;
    padding: 8px;
}

ion-popover {
    --width: 150px;
}