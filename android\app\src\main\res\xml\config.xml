<?xml version='1.0' encoding='utf-8'?>
<widget version="1.0.0" xmlns="http://www.w3.org/ns/widgets" xmlns:cdv="http://cordova.apache.org/ns/1.0">
  <access origin="*" />
  
  <feature name="NativeSettings">
    <param name="android-package" value="com.phonegap.plugins.nativesettings.NativeSettings"/>
  </feature>

  <feature name="CordovaHttpPlugin">
    <param name="android-package" value="com.silkimen.cordovahttp.CordovaHttpPlugin"/>
  </feature>

  <feature name="AES256">
    <param name="android-package" value="com.ideas2it.aes256.AES256"/>
  </feature>

  <feature name="AppVersion">
    <param name="android-package" value="uk.co.whiteoctober.cordova.AppVersion"/>
  </feature>

  <feature name="BackgroundMode">
    <param name="android-package" value="de.appplant.cordova.plugin.background.BackgroundMode"/>
  </feature>
  <feature name="BackgroundModeExt">
    <param name="android-package" value="de.appplant.cordova.plugin.background.BackgroundModeExt"/>
  </feature>

  <feature name="Device">
    <param name="android-package" value="org.apache.cordova.device.Device"/>
  </feature>

  <feature name="File">
    <param name="android-package" value="org.apache.cordova.file.FileUtils"/>
    <param name="onload" value="true"/>
  </feature>

  <feature name="NetworkStatus">
    <param name="android-package" value="org.apache.cordova.networkinformation.NetworkManager"/>
  </feature>

  <feature name="CDVOrientation">
    <param name="android-package" value="cordova.plugins.screenorientation.CDVOrientation"/>
  </feature>

  <feature name="SocialSharing">
    <param name="android-package" value="nl.xservices.plugins.SocialSharing"/>
  </feature>

  <feature name="Zip">
    <param name="android-package" value="org.apache.cordova.Zip"/>
  </feature>

  <feature name="SQLitePlugin">
    <param name="android-package" value="io.sqlc.SQLitePlugin"/>
  </feature>

  
</widget>