import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { closeOutline } from 'ionicons/icons';

@Component({
    selector: 'app-image-viewer',
    templateUrl: './image-viewer.component.html',
    styleUrls: ['./image-viewer.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
      SafePipe
    ]
})
export class ImageViewerComponent {
  @Input() image: string;
  @Output() closePopover = new EventEmitter();
  fullScreenOn: boolean = false;

  constructor() {
    addIcons({ closeOutline });
  }

  close() {
    this.closePopover.emit();
  }

  changeSize(){
    this.fullScreenOn = !this.fullScreenOn;
  }
}
