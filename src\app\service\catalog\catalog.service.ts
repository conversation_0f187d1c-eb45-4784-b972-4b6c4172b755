import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { CatalogCard } from '../data/catalog-card';

export enum ViewType {
  INFINITE = 'infinite',
  PAGINATED = 'paginated',
}

export enum CardType {
  SMALL = 'small',
  MEDIUM = 'medium',
  LARGE = 'large',
  INFINITE = 'infinite'
}

export enum UserType {
  PROSPECT = 'prospect',
  CUSTOMER = 'customer'
}

@Injectable({
  providedIn: 'root'
})
export class CatalogService {

  private currentCategory: CatalogCard | null = null;
  private nextCategory: any;
  private navigationStackStrings: string[] = [];
  private activeViewType: ViewType = ViewType.PAGINATED;
  private activeCardView = new BehaviorSubject<CardType>(CardType.SMALL);
  private activeShowFavorites: boolean = false;
  private activeUserType: UserType = UserType.CUSTOMER;
  private activeUserUid: string = '';
  private activeLongPress: boolean = false;
  private activeIntoSubcategory: boolean = false;
  private activeRootView: boolean = false;
  private activeCustomerName: string = '';
  private catalogsLoaded: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

  goToNextCategory() {
    console.log(this.nextCategory);
  }

  get isInfiniteView(): boolean {
    return this.activeViewType === ViewType.INFINITE;
  }

  get isPaginatedView(): boolean {
    return this.activeViewType === ViewType.PAGINATED;
  }

  get isRootView(): boolean {
    return this.activeRootView;
  }

  get isSmallCardView(): boolean {
    return this.activeCardView.getValue() === CardType.SMALL;
  }

  get isMediumCardView(): boolean {
    return this.activeCardView.getValue() === CardType.MEDIUM;
  }

  get isLargeCardView(): boolean {
    return this.activeCardView.getValue() === CardType.LARGE;
  }

  get cardView(): BehaviorSubject<CardType> {
    return this.activeCardView;
  }

  get isShowFavorites(): boolean {
    return this.activeShowFavorites;
  }

  get isProspect(): boolean {
    return this.activeUserType === UserType.PROSPECT;
  }

  get isCustomer(): boolean {
    return this.activeUserType === UserType.CUSTOMER;
  }

  get userUid(): string {
    return this.activeUserUid;
  }

  get longPressEnabled(): boolean {
    return this.activeLongPress;
  }

  get intoSubcategory(): boolean {
    return this.activeIntoSubcategory;
  }

  get catalogsAreLoaded(): BehaviorSubject<boolean> {
    return this.catalogsLoaded;
  }

  get currentCategoryData(): CatalogCard | null {
    return this.currentCategory;
  }

  
  get customerName(): string {
    return this.activeCustomerName;
  }

  setCardType(cardType: CardType) {
    this.activeCardView.next(cardType);
    localStorage.setItem('cardViewType', cardType);
  }

  setViewType(viewType: ViewType) {
    this.activeViewType = viewType;
  }

  setRootView(rootView: boolean) {
    console.log(`Setting root view to: ${rootView}`);
    this.activeRootView = rootView;
  }

  setCustomerUid(customerUid: string) {
    this.activeUserUid = customerUid;
  }

  setCustomerName(customerName: string) {
    this.activeCustomerName = customerName;
  }

  setIsProspect(isProspect: boolean) {
    this.activeUserType = isProspect ? UserType.PROSPECT : UserType.CUSTOMER;
  }

  setShowFavorites(showFavorites: boolean) {
    this.activeShowFavorites = showFavorites;
  }

  setIntoSubcategory(intoSubcategory: boolean) {
    this.activeIntoSubcategory = intoSubcategory;
  }

  setCatalogsLoaded(loaded: boolean) {
    this.catalogsLoaded.next(loaded);
  }

  setActiveCategory(category: CatalogCard) {
    this.currentCategory = category;
    this.navigationStackStrings.push(category.idApp);
  }

  testIsRoot(idApp: string): boolean {
    const regex = /\b(?:it;)(\d+)\b(?!;[\d])/;
    return regex.test(idApp);
  }
}
