import { Component, Input } from '@angular/core';
import { IonicModule, PopoverController } from '@ionic/angular';
import { Product } from 'src/app/service/data/product';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
    selector: 'app-quantity-popover',
    templateUrl: './quantity-popover.component.html',
    styleUrls: ['./quantity-popover.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
    ]
})
export class QuantityPopoverComponent {
  @Input() product: Product;

  constructor(private popoverController: PopoverController) { }

  multiplier(input) {
    if (+input === 0)
      return;
    // Controlla se il valore è già un multiplo di quantitaMinimaOrdinabile
    let multi = +this.product.minimumDeliveryQuantity;
    if (+input % +this.product.minimumDeliveryQuantity === 0) {
      multi = +input;
    } else {
      // Calcola il multiplo superiore
      multi = Math.ceil(+input / +this.product.minimumDeliveryQuantity) * +this.product.minimumDeliveryQuantity;
    }
    this.popoverController.dismiss({
      newQuantity: multi
    });
  }
}
