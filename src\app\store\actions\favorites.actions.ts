import { createAction, props } from "@ngrx/store";
import { CatalogCard } from "src/app/service/data/catalog-card";
import { Favorite } from "src/app/service/data/favorite";

export const setFavorites = createAction(
  '[Favorites] set',
  props<{ items: Favorite[] }>()
);

export const addFavorite = createAction(
  '[Favorites] add',
  props<{ idCategory: string, customerUid: string; catalogCard: CatalogCard }>()
);

export const removeFavorite = createAction(
  '[Favorites] remove',
  props<{ idCategory: string, customerUid: string; }>()
);
