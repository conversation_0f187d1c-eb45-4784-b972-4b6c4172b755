.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        height: 90vh;
        max-height: 600px;
        width: 90vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            position: relative;
            .close {
                background-color: transparent;
                justify-content: end;
                position: relative;
                z-index: 9999;
            }
        }
        .row {
            position: relative;
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-top: -99px;
            padding: 25px;
            h1 {
                text-transform: uppercase;
                font-weight: bold;
                font-size: 19px;
                color: var(--ion-dat-black);
            }
            .prices {
                overflow: auto;
                height: calc(100% - 66px);
                &::-webkit-scrollbar {
                    height: 7px;
                    width: 7px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: var(--ion-dat-gray);
                    border-radius: 20px;
                }
                table {
                    border: 1px solid var(--ion-dat-middle-gray);
                    border-collapse: collapse;
                    margin: 0;
                    padding: 0;
                    width: 100%;
                    thead tr {
                        position: sticky;
                        top: 0;
                        z-index: 1;
                    }
                    tr {
                        background-color: var(--ion-dat-black);
                        border: 1px solid var(--ion-dat-middle-gray);
                        padding: .35em;
                        &:nth-child(odd) td{
                            background-color: var(--ion-dat-white);
                        }
                        &:nth-child(even) td{
                            background-color: var(--ion-dat-gray);
                        }

                        th, td {
                            padding: 0px;
                            text-align: center;
                        }
                        th {
                            font-size: .85em;
                            letter-spacing: .1em;
                            text-transform: uppercase;
                            color: var(--ion-dat-white);
                            &.price-list {
                                width: 10%;
                                min-width: 100px;
                            }
                        }
                        td {
                            position: relative;
                            &.fix {
                                padding: 5px;
                            }
                            &.cart {
                                width: 44px;
                                .product-quantity-in-cart, .product-min-quantity 
                                {
                                    margin-left: 0px;
                                    font-size: 14px;
                                    width: 35px;
                                    text-align: center;
                                    font-weight: bold;
                                    position: absolute;
                                    margin-top: -6px;
                                }
                                .product-quantity-in-cart
                                {
                                    color: var(--ion-dat-red);
                                }
                            }
                            &.quantity {
                                white-space: nowrap;
                                button {
                                    background: var(--ion-dat-dark-gray);
                                    width: 25px;
                                    height: 25px;
                                    color: var(--ion-dat-white);
                                    border-radius: 3px;
                                    padding: 0;
                                    &:active {
                                        transform: translateY(5px);
                                    }
                                    ion-icon {
                                        --ionicon-stroke-width: 83px;
                                    }
                                    &:first-child{
                                        margin-right: 10px;
                                    }
                                }
                            }
                            object {
                                width: 30px;
                                height: 30px;
                                pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
                            }
                            &.product-image {
                                padding-top:7px;
                                width: 70px;
                                img {
                                    width: 60px !important;
                                    max-width: 60px !important;
                                    height: 60px !important;
                                    max-height: 60px !important;
                                }
                            }
                            button {
                                background-color: transparent;
                            }
                            .bold {
                                font-weight: bold;
                            }
                            .price-block {
                                display: flex;
                                flex-direction: column;
                                height: 60px; /* TODO: Stessa altezza dell'img */
                                .price {
                                    font-weight: bold;
                                    height: 85%;
                                    display: flex;
                                    justify-content: center;
                                    align-items: center;
                                }
                                .other {
                                    display: flex;
                                    flex-direction: row;
                                    width: 100%;
                                    justify-content: space-evenly;
                                    align-items: end;
                                    border-top: 1px solid var(--ion-dat-middle-gray);
                                    div {
                                        width: 50%;
                                        height: 16px;
                                       &:first-child  {
                                            border-right: 1px solid var(--ion-dat-middle-gray);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }


    }
}

.focusProd {
  background-image: url('../../../../../assets/svg/focus.svg');
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: 2px;
}

.z3 {
    background-image: url('../../../../../assets/icon/icona_attenzione.png');
    background-repeat: no-repeat;
    background-position-x: right;
    background-position-y: 2px;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 0;
}

app-tabs {
    display: contents;
    overflow: hidden;
    app-tab {
        width: 100%;
        display: contents;
        .pane {
            height: calc(100% - 43px);
        }
    }
}

app-discounts {
    position: absolute;
    z-index: 9999;
    bottom: 7px;
    width: calc(90vw - 55px);
}