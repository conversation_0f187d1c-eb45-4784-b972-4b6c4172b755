package io.ionic.starter;

import android.os.Bundle;

import com.capacitorjs.plugins.app.AppPlugin;
import com.capacitorjs.plugins.browser.BrowserPlugin;
import com.capacitorjs.plugins.keyboard.KeyboardPlugin;
import com.capacitorjs.plugins.splashscreen.SplashScreenPlugin;
import com.epicshaggy.biometric.NativeBiometric;
import com.getcapacitor.BridgeActivity;
import com.getcapacitor.Plugin;
import com.ryltsov.alex.plugins.file.opener.FileOpenerPlugin;

import java.util.ArrayList;

public class MainActivity extends BridgeActivity {
  @Override
  public void onCreate(Bundle savedInstanceState) {
    super.onCreate(savedInstanceState);
    registerPlugin(NativeBiometric.class);

    // Initializes the Bridge
    this.init(savedInstanceState, new ArrayList<Class<? extends Plugin>>() {{
      // Additional plugins you've installed go here
      // Ex: add(TotallyAwesomePlugin.class);
      add(NativeBiometric.class);
      add(BrowserPlugin.class);
      add(SplashScreenPlugin.class);
      add(FileOpenerPlugin.class);
      add(AppPlugin.class);
      add(KeyboardPlugin.class);
    }});
  }
}
