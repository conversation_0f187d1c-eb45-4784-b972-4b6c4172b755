import { createReducer, on } from "@ngrx/store";
import { addNavHistory, removeLastNavHistory, resetNavHistory } from "../actions/nav-history.actions";

export const navHistoryReducer = createReducer(
  [],
  on(resetNavHistory, (state, actions) => {
    return [];
  }),
  on(addNavHistory, (state, actions) =>{
    if(state.length === 0)
      return [...state, actions];
    else {
        const last = state[state.length-1];
        if(JSON.stringify(last.item) === JSON.stringify(actions.item))
          return state;
        else
          return [...state, actions].slice(-10); // Tengo in storico al massimo le ultime 10 azioni
    }
  }),
  on(removeLastNavHistory, (state, actions) => {
    let newState: any[] = [...state];
    newState.pop();
    return newState;
  }),
)
