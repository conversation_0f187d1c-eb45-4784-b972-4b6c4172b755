import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';

@Component({
    selector: 'app-catalog-choice',
    templateUrl: './catalog-choice.component.html',
    styleUrls: ['./catalog-choice.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class CatalogChoiceComponent {
  @Output() hideCatalogChoice = new EventEmitter();
  @Output() startCustomerSession = new EventEmitter();
  @Output() startProspectSession = new EventEmitter();

  close(event) {
    event.stopPropagation();
    this.hideCatalogChoice.emit();
  }

  customerSession(event) {
    event.stopPropagation();
    this.startCustomerSession.emit();
  }

  prospectSession(event) {
    event.stopPropagation();
    this.startProspectSession.emit();
  }

}
