.nav-header {
    height: 30px;
    width: 100%;
    padding: 3px;
    border-bottom: 2px dotted var(--ion-dat-middle-gray);
    ul {
        margin-top: 0;
        margin-bottom: 10px;
        padding: 0;
        li {
            float: left;
            margin-bottom: -1px;
            display: list-item;
            text-align: -webkit-match-parent;
            position: relative;
            position: relative;
            display: block;
            padding: 2px 15px;
            font-weight: bold;
            margin-right: 3px;
            background-color: var(--ion-dat-middle-gray);
            &.active {
                background-color: var(--ion-dat-white);
            }
            div:active {
                transform: translateY(5px);
            }
        }
    }
}