.title {
    position: absolute;
    top: calc(0px + var(--ion-safe-area-top, 0));
    height: 44px;
    align-items: center;
    display: flex;
    color: white;
    z-index: 9999;
    text-transform: uppercase;
    font-weight: bolder;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100vw - 280px);
    justify-content: flex-end;
}
.container {
    width: 100%;
    &:after{
        content: "";
        display: table;
        clear: both;
    }
    .column{
        float: left;
        width: 100%;
    }
    .overlay {
        background-color: var(--ion-dat-popover);
        opacity: 0.5;
        width: 100%;
        height: 100%;
        left:0;
        top: 0;
        bottom: 0;
        right: 0;
        position: absolute;
        z-index: 8000;
    }
    .detail {
        float: left;
        width: 500px;
        height: calc(100vh - 96px);
        position: absolute;
        right: 0;
        z-index: 9999;
        background-color: var(--ion-dat-white);
    }
}

.cards {
    height: 90vh;
    swiper-container {
        height: calc(100% - 38px);
        swiper-slide {
            align-items: flex-start;
            padding: 15px;
            .slider-grid {
                /*height: 100%;*/
                width: 100%;
                display: grid;
                grid-template-columns: calc(33.33% - 6px) calc(33.33% - 6px) 33.33%;
                grid-gap: 6px;
            }
        }
    }
}
.pagination {
    width: calc(100% - 130px);
    display: flex;
    justify-content: center;
    bottom: 38px;
    position: absolute;
    z-index: 9999;
    margin: auto;
    .letter {
        padding: 4px;
        margin: 4px;
        span {
            padding: 4px;
            font-size: 12px;
        }
        .current {
            font-weight: bold;
            background: var(--ion-dat-red);
            padding: 4px;
            border-radius: 30px;
            color: var(--ion-dat-white);
        }
        .empty {
            color: var(--ion-dat-gray)
        }
    }
    flex-wrap: wrap;
}

.cards.col-1 swiper-container swiper-slide .slider-grid {
    grid-template-columns: 100%;
}

.cards.col-2 swiper-container swiper-slide .slider-grid {
    grid-template-columns: calc(50% - 6px) 50%;
}

.cards.col-3 swiper-container swiper-slide .slider-grid {
    grid-template-columns: calc(33.33% - 6px) calc(33.33% - 6px) 33.33%
}

.cards.col-4 swiper-container swiper-slide .slider-grid {
    grid-template-columns: calc(25% - 6px) calc(25% - 6px) calc(25% - 6px) 25%;
}

.cards.col-5 swiper-container swiper-slide .slider-grid {
    grid-template-columns: calc(20% - 6px) calc(20% - 6px) calc(20% - 6px) calc(20% - 6px) 20%;
}


.cards.col-6 swiper-container swiper-slide .slider-grid {
    grid-template-columns: calc(17% - 6px) calc(17% - 6px) calc(17% - 6px) calc(17% - 6px) calc(16% - 6px) 16%;
}
/*
.cards.col-7 swiper-container swiper-slide .slider-grid {
    grid-template-columns: 250px 250px 250px 250px 250px 250px 250px;
}

.cards.col-8 swiper-container swiper-slide .slider-grid {
    grid-template-columns: 250px 250px 250px 250px 250px 250px 250px 250px;
}

.cards.col-9 swiper-container swiper-slide .slider-grid {
    grid-template-columns: 250px 250px 250px 250px 250px 250px 250px 250px 250px;
}

.cards.col-10 swiper-container swiper-slide .slider-grid {
    grid-template-columns: 250px 250px 250px 250px 250px 250px 250px 250px 250px 250px;
}*/

@mixin cardsrow($num) {
  .cards.row-#{$num} swiper-container swiper-slide .slider-grid {
  $single: calc(10000 / $num);
  $query: "";
  @for $i from 1 through $num - 1 {
    $query: $query + "calc(#{$single}% - 6px) ";
  }
  $query: $query + #{$single} + "%";
  grid-template-rows: #{$query};
  }
}
@for $i from 1 through 10 {
  @include cardsrow($i);
}


app-customer-card {
  height: 115px;
}
