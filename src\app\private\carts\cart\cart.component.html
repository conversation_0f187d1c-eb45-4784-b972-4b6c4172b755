<div #title id="title" class="title">
  {{ 'CARTS.TITLE' | translate }}
</div>
<div class="container">
  <h1><object type="image/svg+xml" data="../../../assets/svg/person.svg">
    <img src="../../../assets/svg/person.svg" />
  </object><span [innerHTML]="customerName"></span></h1>
  <div class="counter-container">
    <div class="cart-counter">{{(!!cart && !!cart.products) ? cart.products.length : 0}}</div>
  </div>
  <div class="toolbar" *ngIf="!!cart && !!cart.products && cart.products.length > 0">
    <div class="button-bar">
      <ion-button *ngIf="!!cart && !!cart.products && cart.products.length > 0" (click)="transferCarts()" 
        [disabled]="sending" class="tb-btn"
        [ngClass]="{'sending': sending}">
        {{ 'CARTS.TRANSFER_CART' | translate }}
      </ion-button>
      <ion-button *ngIf="!!cart && !!cart.products && cart.products.length == 0" 
      class="tb-btn transfered" disabled="true" >
        {{ 'CARTS.CART_TRANSFERED' | translate }}
      </ion-button>
    </div>
    <app-search [customStyle]="'minified'" [viewType]="'catalog'" [simpleView]="true" (closeSearch)="closeProductSearch($event)"></app-search>
  </div>
  <ion-list *ngIf="!!cart && !!cart.products && cart.products.length > 0">
    <ion-item class="list-header">
      <div class="row">
        <div class="cart-number"></div>
        <div class="product-image"></div>
        <div class="article">{{ 'CARTS.ARTICLE' | translate }}</div>
        <div class="description">{{ 'CARTS.DESCRIPTION' | translate }}</div>
        <div class="quantity">{{ 'CARTS.QUANTITY' | translate }}</div>
        <div class="none"></div>
      </div>
    </ion-item>
    <ion-item *ngFor="let item of cart.products; let i = index">
      <div class="row">
        <div class="cart-number">{{i+1}}</div>
        <div class="product-image">
          <img [src]="item.image | safe" width="60" height="60" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
        </div>
        <div class="article">{{item.idProduct | zeroRemover}}</div>
        <div class="description">{{item!.description}}</div>
        <div class="quantity">
          <div (click)="addQuantity(item.idProduct)">
            <div class="product-quantity-in-cart">{{ item.quantity }}</div>
            <object type="image/svg+xml" data="assets/svg/green-cart.svg">
              <img src='../../assets/svg/green-cart.svg' #svg/>
            </object>
          </div>
        </div>
        <div class="none">
          <input #multy type="number" inputmode="numeric" class="multiplier" value="" />
          <ion-button (click)="multiplier(item.idProduct, multy.value)" class="item-btn">
            <ion-icon name="calculator-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="removeQuantity(item.idProduct, item.quantity === item.minimumDeliveryQuantity)"  class="item-btn">
            <ion-icon name="remove-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="addQuantity(item.idProduct)" class="item-btn">
            <ion-icon name="add-outline"></ion-icon>
          </ion-button>
          <ion-button (click)="removeProduct(item.idProduct)" class="item-btn">
            <ion-icon name="trash-outline"></ion-icon>
          </ion-button>
        </div>
      </div>
    </ion-item>
  </ion-list>
</div>
