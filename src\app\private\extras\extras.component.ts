import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ExtraService } from 'src/app/service/extra/extra.service';
import { BasicResponse } from 'src/app/service/data/basic-response';
import { Extra } from 'src/app/service/data/extra';
import Utils from 'src/app/shared/utils';
import { LoadingController, Platform, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { select, Store } from '@ngrx/store';
import { Router } from '@angular/router';
import { removeLastNavHistory } from 'src/app/store/actions/nav-history.actions';
import { Network } from '@capacitor/network';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { ExtraCardComponent } from './extra-card/extra-card.component';
@Component({
    selector: 'app-extras',
    templateUrl: './extras.component.html',
    styleUrls: ['./extras.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule,
      ExtraCardComponent
    ]
})
export class ExtrasComponent implements OnInit {
  public appIsOnline$: Observable<boolean> = undefined;
  extras: Extra[] = [];
  isFinished: boolean = false;

  constructor(private _router: Router, private _platform: Platform,
    private cdref: ChangeDetectorRef, public _loadingController: LoadingController,
    private _toastController: ToastController, private _translate: TranslateService,
    private _service: ExtraService, private _store: Store<any>) { }

  ngOnInit() {
    this._platform.ready().then(() => {
      if (this._platform.is('capacitor')) {
        Network.getStatus().then((status) => {
          if(status.connected){
            this.loadData();
          }
        });
      } else {
        this.loadData();
      }
    });
    Utils.checkInternet().then(isOnline => {
      if(isOnline && this.extras.length === 0)
        this.loadData();
    });
  }

  private async loadData() {
    if (Utils.isActiveConnection()) {
      const loading = await this._loadingController.create({
        message: this._translate.instant('GENERICS.WAIT')
      });
      loading.present();
      this._service.extra().then(async (data:BasicResponse)=> {
        if(!data)
          console.error("data is undefined")
        else if(!!data && data.status === 'KO') {
          console.error(data.error.details)
        } else {
          if( typeof data.content === 'object' && data.content.length > 0)
          {
            this.extras = data.content;
          }
        }
      }).finally(async () => {
        this.isFinished = true;
        this.cdref.detectChanges();
        loading.dismiss();
      });
    } else {
      this.isFinished = true;
      Utils.showSnack(this._toastController,this._translate.instant('GENERICS.ERROR_NO_NETWORK'));
    }
  }

  extraGoComponentGoBack() {
    let routingStack = [];
    this._store.pipe(select('routingStack')).subscribe(res => routingStack = [...res]).unsubscribe();
    const lastRouting = routingStack[routingStack.length - 2];
    let navHistory = [];
    this._store.pipe(select('navHistory')).subscribe(res => navHistory = res).unsubscribe();
    const lastHistory = navHistory[navHistory.length - 1];
    let currentCustomer = null;
    this._store.pipe(select('currentCustomer')).subscribe(res => currentCustomer = res).unsubscribe();

    if(lastRouting.component.includes('catalog') && !lastRouting.component.includes('product'))
    {
      this._store.dispatch(removeLastNavHistory()); // tolgo l'ultimo che serve solo ai fini del torna indietro
      this._router.navigate(['/private/catalog'], { queryParams: {
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isProspect: lastRouting.component.includes('isProspect=true'),
        showFavorites: false,
        clickedNavarId: !!lastHistory && !!lastHistory.item && !!lastHistory.item.datacolCategory && !!lastHistory.item.datacolCategory.category ? lastHistory.item.datacolCategory.category.id : null,
        turnAround: true,
        comeFromDC: !!lastHistory && !!lastHistory.item && !!lastHistory.item.datacolCategory && !!lastHistory.item.datacolCategory.category ? lastHistory.item.datacolCategory.category.id : null
      }});
    } else if (lastRouting.component.includes('product')) {
      this._router.navigate([`/private/catalog/products-carousel/${lastHistory.item.id}`], { queryParams: {
        idRootCategory: lastHistory.item.datacolCategory.category.idRootCategory,
        current: JSON.stringify(lastHistory.item.datacolCategory.category),
        customerUid: currentCustomer.uid,
        customerName: currentCustomer.name,
        isFavorite: lastHistory.item.isFavorite,
        isProspect: lastRouting.component.includes('isProspect=true'),
      }});
    }
    else if(!!lastRouting.component)
      this._router.navigate([lastRouting.component]);
  }
}
