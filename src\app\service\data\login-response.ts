export interface Language {
    active: string;
    isocode: string;
    name: string;
}

export interface InfoDataList {
    distributionChannel: string;
    division: string;
    salesOrganization: string;
}

export interface LoginResponse {
    name: string;
    uid: string;
    customerId: string;
    email: string;
    language: Language;
    token: string;
    agentCode: string;
    catalogName: string;
    infoDataList: InfoDataList[];
}
