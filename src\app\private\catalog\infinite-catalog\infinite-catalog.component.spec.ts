import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { IonicModule } from '@ionic/angular';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { InfiniteCatalogComponent } from './infinite-catalog.component';
import { CatalogCard } from 'src/app/service/data/catalog-card';

describe('InfiniteCatalogComponent', () => {
  let component: InfiniteCatalogComponent;
  let fixture: ComponentFixture<InfiniteCatalogComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      imports: [IonicModule.forRoot(), InfiniteCatalogComponent]
    }).compileComponents();

    fixture = TestBed.createComponent(InfiniteCatalogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit loadMoreItems event when onIonInfinite is called', () => {
    spyOn(component.loadMoreItems, 'emit');

    const mockEvent = {
      target: {
        complete: jasmine.createSpy('complete')
      }
    };

    component.onIonInfinite(mockEvent, 'down');

    expect(component.loadMoreItems.emit).toHaveBeenCalledWith('down');
  });

  it('should emit goToNextCategory event when onGoToNextCategory is called', () => {
    spyOn(component.goToNextCategory, 'emit');

    component.onGoToNextCategory();

    expect(component.goToNextCategory.emit).toHaveBeenCalled();
  });

  it('should return true for containsItems when items array has elements', () => {
    const mockItems: CatalogCard[] = [
      new CatalogCard('1', '1', 'Test Item', '', false, 'PRODUCT', [], false, '', 'N', null, '', '1')
    ];

    component.items = mockItems;

    expect(component.containsItems).toBe(true);
  });

  it('should return false for containsItems when items array is empty', () => {
    component.items = [];

    expect(component.containsItems).toBe(false);
  });

  it('should complete infinite scroll event after timeout', (done) => {
    const mockEvent = {
      target: {
        complete: jasmine.createSpy('complete')
      }
    };

    component.onIonInfinite(mockEvent, 'up');

    setTimeout(() => {
      expect(mockEvent.target.complete).toHaveBeenCalled();
      done();
    }, 600);
  });
});
