import { createReducer, on } from "@ngrx/store";
import { Favorite } from "src/app/service/data/favorite";
import { addFavorite, removeFavorite, setFavorites } from "../actions/favorites.actions";

export const favoritesReducer = createReducer(
  [],
  on(setFavorites, (state, actions) => {
    return actions.items
  }),
  on(addFavorite, (state, actions) => {
    const filtered = state.filter(single => (single as Favorite).idCategory === actions.idCategory && (single as Favorite).customerUid === actions.customerUid)
    if(filtered.length > 0)
      return state;
    else
      return [...state, actions];
  }),
  on(removeFavorite, (state, actions) => {
    let newState: any[] = [...state];
    newState = newState.filter(item => item.idCategory.toString() !== actions.idCategory.toString());
    return newState;
  }),
)
