import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { select, Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
@Component({
    selector: 'app-navigation-queue',
    templateUrl: './navigation-queue.component.html',
    styleUrls: ['./navigation-queue.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
      TranslateModule,
      CommonModule
    ]
})
export class NavigationQueueComponent {
  @Input() isFavorites: boolean = false;
  @Input() customerName: string = '';
  @Output() goToCategory = new EventEmitter();
  navigationQueue$ = this._store.pipe(select('navStack'));

  constructor(private _store: Store<any>) { }

  goTo(id?: string) {
    this.goToCategory.emit({ idCategory: id });
  }
}
