<ion-content #infiniteContent (ionScroll)="onInfiniteScroll($event)" scrollEvents="true"
  (ionScrollStart)="onScrollStart()" (ionScrollEnd)="onScrollEnd()" class="infinite-content">
  @if(containsItems && showTopInfiniteLoader && hasMoreItemsUp) {
  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event, 'up')" position="top" class="grey"
    [threshold]="infiniteScrollThreshold">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>
  }

  <ion-grid #infiniteGrid class="grey z-1000" [ngStyle]="{'--columns': cardDimension.columns}">
    <ion-row class="ion-infinite-row">
      @for(item of loadedItems; track trackById($index, item)) {
      <ion-col class="grey"
        [ngClass]="{'ion-infinite-col-small': item.type == 'CATEGORY', 'center': item.type == 'PRODUCT'}"
        [size]="item.type == 'CATEGORY' ? '12' : columnsOccupancy" [attr.data-id]="item.id">
        <app-infinite-catalog-card [ngClass]="{'category-card': item.type == 'CATEGORY'}" [item]="item">
        </app-infinite-catalog-card>
      </ion-col>
      }
    </ion-row>
  </ion-grid>

  @if(catalogService.isInfiniteView && showNextCategoryButton) {
  <div class="category-navigation-infinite">
    <div (click)="onGoToNextCategory()">
      <span [innerHTML]="nextRootCategory?.next?.name || ''"></span><ion-icon name="caret-forward-circle"></ion-icon>
    </div>
  </div>
  }

  @if(containsItems && showBottomInfiniteLoader && hasMoreItemsDown) {
  <ion-infinite-scroll (ionInfinite)="onIonInfinite($event, 'down')" position="bottom" class="grey"
    [threshold]="infiniteScrollThreshold">
    <ion-infinite-scroll-content></ion-infinite-scroll-content>
  </ion-infinite-scroll>
  }
</ion-content>