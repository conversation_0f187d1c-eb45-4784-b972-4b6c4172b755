import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Customer } from 'src/app/service/data/customer';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';  
@Component({
    selector: 'app-banking-data',
    templateUrl: './banking-data.component.html',
    styleUrls: ['./banking-data.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      FormsModule,
      ReactiveFormsModule,
      TranslateModule
    ]
})
export class BankingDataComponent {
  @Input() customer: Customer = null;

  constructor() { }

}
