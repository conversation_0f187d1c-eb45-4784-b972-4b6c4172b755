.card {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--ion-dat-gray);
  width: 100%;
  height: 80%;
  min-height: 50px;
  padding: 5px;
  border-radius: 3px;
  font-weight: bold;
  -webkit-box-shadow: 5px 5px 10px -3px #c5c5c5;
  -moz-box-shadow: 5px 5px 10px -3px #c5c5c5;
  -o-box-shadow: 5px 5px 10px -3px #c5c5c5;
  box-shadow: 5px 5px 10px -3px #c5c5c5;
  &:focus, &:active {
      -webkit-box-shadow: none;
      -moz-box-shadow: none;
      -o-box-shadow: none;
      box-shadow: none;
  }
  div {
      display: flex;
      //height: 100%;
      width: 100%;
      align-items: center;
      font-size: medium;

      div.icon, div.name {
          padding-left: 5px;
          padding-top: 5px;
      }
      div.name {
        display: flex;
        justify-content: center;
      }
      div.icon {
          width: 100px;
          height: 100px;
      }
      div.icon img {
          display: inline-block;
          width: 100px;
      }
      div.detail {
          display: flex;
          flex-direction: column;
          width: 100%;
          padding-left: 20px;
          color: var(--ion-dat-black);
          text-align: center;
          .open-category {
              display: none;
              height: 15px;
          }
      }
  }

  &.white-card {
      position: relative;
      background-color: var(--ion-dat-white);
      div div.detail {
          .name {
              color: var(--ion-dat-black);
              text-transform: uppercase;
              text-align: left;
              justify-content: left;
          }
          .open-category {
              display: block;
              text-align: right;
              color: var(--ion-dat-red);
          }
      }
  }

  &.favorites {
      border: 1px solid var(--ion-dat-yellow);
      div {
          padding-left: 0px !important;
          justify-content: left !important;
          .icon {
              width: 80px !important;

              ion-icon[name='star'] {
                  font-size: 80px;
                  background: none;
                  color: var(--ion-dat-yellow);
              }
          }
          .detail {
              width: 100% !important;
              padding-left: 15px !important;
              .favorites-description {
                  text-transform: uppercase;
                  text-align: center !important;
              }
              .name {
                  text-transform: uppercase;
                  font-weight: bold;
                  text-align: center !important;
                  display: inline-block;
              }
          }
      }
  }
}

.card.category.red-arrow {
  background: transparent;
  display: flex;
  justify-items: center;
  box-shadow: none;
  padding: 0;
  --parent-height: $height;
  .name {
    width: 100% !important;
    height: 80% !important;
    background-color: var(--ion-dat-red);
    color: var(--ion-dat-white);
    border: 1px solid var(--ion-dat-red);
    /* border-top-left-radius: 3px;
       border-bottom-left-radius: 3px; */
    justify-content: center;
    text-transform: uppercase;
    font-weight: bold;
    font-size: large;
    -webkit-box-shadow: 5px 5px 10px -3px #c5c5c5;
    -moz-box-shadow: 5px 5px 10px -3px #c5c5c5;
    -o-box-shadow: 5px 5px 10px -3px #c5c5c5;
    box-shadow: 5px 5px 10px -3px #c5c5c5;
    -webkit-transform: skew(-20deg);
    -moz-transform: skew(-20deg);
    -o-transform: skew(-20deg);
    padding: 8px;
    text-align: center;
  }
}

.card.product {
  div .detail table {
    width: 100%;
    height: 100%;
    text-align: left;
    text-transform: uppercase;
    tr:nth-child(2)  {
      height: 30px;
    }
    td {
      width: 30%;
      .name {
        display: -webkit-box;
        max-width: 100%;
        height: 100% !important;
        margin: 0 auto;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -ms-word-break: break-all;
        word-break: break-all;
        word-break: break-word;
        height: 44px;
        font-size: 14px;
        padding-top: 0px;
        padding-bottom: 0px;
      }
      ion-icon {
        height: 30px;
        font-size: 25px;
        background: var(--ion-dat-middle-gray);
        padding: 5px;
        border-radius: 3px;
        color: var(--ion-dat-white);
        display: block;
      }
      ion-icon[name='star-outline'] {
        font-size: 30px;
        background: none;
        color: var(--ion-dat-middle-gray);
      }
      ion-icon[name='star'] {
        font-size: 30px;
        background: none;
        color: var(--ion-dat-yellow);
      }
      &:nth-child(1) ion-icon {
        margin-left: 0;
      }
      &:nth-child(2) ion-icon {
        margin: auto;
      }
      &:nth-child(3) ion-icon {
        margin-right: 0;
        float: right;
      }
    }
  }
}

.card div div.to-be-continued {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  margin-top: 7px;
  margin-bottom: 20px;
  height: min-content !important;
  color: var(--ion-dat-red);
  height: min-content !important;
}

.focusProd {
  background-image: url('../../../../assets/svg/focus.svg');
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: 2px;
}

.z3::before {
  content: '';
  display: inline-block;
  width: 20px;
  height: 20px;
  background-image: url('../../../../assets/icon/icona_attenzione.png');
  background-repeat: no-repeat;
  background-position-x: right;
  background-position-y: 2px;
  position: absolute;
  right: 5px;
  top: 25px;
}



@supports (-webkit-touch-callout: none) {
  /* CSS specific to iOS devices */
  .name {
    height: 48px !important;
  }
}
