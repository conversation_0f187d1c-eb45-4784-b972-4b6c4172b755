// src/app/app.config.ts

import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideRouter } from '@angular/router';
import { HttpClient, provideHttpClient, withInterceptorsFromDi, HTTP_INTERCEPTORS } from '@angular/common/http';
import { IonicRouteStrategy, provideIonicAngular } from '@ionic/angular/standalone';
import { RouteReuseStrategy } from '@angular/router';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideStore } from '@ngrx/store';
import { routes } from './app.routes'; // convertiamo app-routing.module.ts in app.routes.ts
import { CapacitorSQLite, SQLiteConnection } from '@capacitor-community/sqlite';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { TranslateModule } from '@ngx-translate/core';
import { TranslateLoader } from '@ngx-translate/core';
import { catalogByProductReducer } from './store/reducers/catalog-by-product.reducers';
import { catalogViewReducer } from './store/reducers/catalog-view.reducers';
import { productsReducer } from './store/reducers/products.reducers';
import { cartReducer } from './store/reducers/cart.reducers';
import { categoriesReducer } from './store/reducers/categories.reducers';
import { subcategoriesReducer } from './store/reducers/subcategories.reducers';
import { dcpropertyReducer } from './store/reducers/dcproperty.reducers';
import { favoritesReducer } from './store/reducers/favorites.reducers';
import { tagFilterReducer, filteredCategoriesReducer } from './store/reducers/tag-filter.reducers';
import { navHistoryReducer } from './store/reducers/nav-history.reducers';
import { navStackReducer } from './store/reducers/nav-stack.reducers';
import { navigationTreeReducer } from './store/reducers/navigation-tree.reducers';
import { ServiceInterceptor } from './shared/service-interceptor';
import { rootCategoriesReducer } from './store/reducers/root-categories.reducers';

export function httpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http);
}

export function sqliteConnectionFactory() {
  return new SQLiteConnection(CapacitorSQLite);
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideHttpClient(withInterceptorsFromDi()),
    provideAnimations(),
    provideIonicAngular(), // supporto Ionic

    // HTTP Interceptors
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ServiceInterceptor,
      multi: true
    },

    provideStore({
      catalogByProduct: catalogByProductReducer,
      catalogView: catalogViewReducer,
      products: productsReducer,
      cart: cartReducer,
      categories: categoriesReducer,
      rootCategories: rootCategoriesReducer, // Assuming root categories are part of the same reducer
      subcategories: subcategoriesReducer,
      dcProperty: dcpropertyReducer,
      favorites: favoritesReducer,
      navStack: navStackReducer,
      navigationTree: navigationTreeReducer,
      navHistory: navHistoryReducer,
      tagFilter: tagFilterReducer,
      filteredCategories: filteredCategoriesReducer,
    }),

    importProvidersFrom([TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: httpLoaderFactory,
        deps: [HttpClient],
      },
    })]),
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    { provide: SQLiteConnection, useFactory: sqliteConnectionFactory }
  ],
};