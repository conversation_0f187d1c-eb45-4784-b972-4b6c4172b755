import { createReducer, on } from "@ngrx/store";
import { addRoutingStack, removeLastRoutingStack, resetRoutingStack } from "../actions/routing-stack.actions";

export const routingStackReducer = createReducer(
  [],
  on(resetRoutingStack, (state, actions) => {
    return [];
  }),
  on(addRoutingStack, (state, actions) =>{
    if(state.length === 0)
      return [...state, actions];
    else {
        const last = state[state.length-1];
        if(last.component === actions.component)
          return state;
        else
          return [...state, actions].slice(-10); // Tengo in storico al massimo le ultime 10 azioni
    }
  }),
  on(removeLastRoutingStack, (state, actions) => {
    let newState: any[] = [...state];
    newState.pop();
    return newState;
  }),
)
