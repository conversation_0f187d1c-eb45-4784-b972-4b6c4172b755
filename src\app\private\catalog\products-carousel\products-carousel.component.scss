.customer-name {
    position: fixed;
    bottom: 15px;
    font-weight: bold;
    color: var(--ion-dat-dark-gray);
    text-transform: uppercase;
    z-index: 9999;
    img {
        margin-right: 10px;
    }
}

app-navigation-panel {
    display: none;
    &.visible {
      display: inherit;
    }
}

app-right-bar {
    position: absolute;
    top: 65px;
    right: 0;
}

swiper-container {
    height: calc(100% - var(--margin-nav));
    .swiper-wrapper {
        margin: auto !important;
    }
    swiper-slide {
        align-items: flex-start;
        padding: 15px;
        width: 100% !important;
        .slider-grid {
            width: 100%;
            height: 100%;
        }
    }
}