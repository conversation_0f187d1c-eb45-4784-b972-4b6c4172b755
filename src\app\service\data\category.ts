export class Image {
    altText: string = null;
    format: string = null;
    imageType: string = null;
    galleryIndex: string = null;
    positionMc: string = null;
    shortCaption: string = null;
    url: string = null;
    imageName: string = null;
}

export class Media {
    code: string = null;
    image: string = null;
    imageUrl: string = null;
    position: string = null;
}

export class Category {
    id: string = null;
    name: string = null;
    description: string = null;
    image: string = null;
    isProduct: boolean = false;
    level: string = null;
    media: Media[] = null;
    thumbnail: string = null;
    subcategories: string = null;
    hierarchyCodes: string = null;
    images: Image[] = null;
    cancellationInstant: string = null;

    // Nuovi campi dalla risposta syncCategories V2
    idCatalog?: number = null;
    idApp?: string = null;
    idParent?: string = null;
    idRootCategory?: string = null;
    idSubCategory?: string = null;
    idImage?: number = null;
    imageUrl?: string = null;
    isRootCategory?: boolean = null;
    isRootTree?: boolean = null;
    modifiedInstant?: number = null;
    productsCount?: number = null;
    focus?: boolean = null;
    divisionStatusCode?: string = null;
    subcategoriesCount?: number = null;

    // Campo per identificare le root categories tramite regex durante la sincronizzazione v2
    isRootCategoryByRegex?: boolean = null;
}
