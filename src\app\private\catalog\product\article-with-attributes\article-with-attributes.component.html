<div class="popover">
  <div class="container">
    <div class="header">
      <button class="close" (click)="close()">
        <ion-icon name="close-outline" size="large"></ion-icon>
      </button>
    </div>
    <div class="row">
      <h1>{{datacolCategory.name}}</h1>
      <div class="products">
        <div class="table-container">
          <table>
            <tr>
              <th scope="col" *ngIf="!isProspect"></th>
              <th scope="col"></th>
              <th scope="col"></th>
              <th scope="col">{{"PRODUCT.ITEM" | translate}} - {{"PRODUCT.DESCRIPTION" | translate}}</th>
              <th scope="col" *ngFor="let item of featureColumns">{{item}}</th>
            </tr>
            <tr *ngFor="let item of products">
              <td longPress data-label="" (click)="addOneToCart(item)" (mouseLongPress)="showMultipleQuantity(item)">
                <div class="product-min-quantity"
                  [ngClass]="{'product-quantity-in-cart' : (cart|async | productQuantity:item.code) > 0}">
                  {{ (cart|async | productQuantity:item.code) == 0 ? item.minimumDeliveryQuantity : (cart|async | productQuantity:item.code)}}
                </div>
                  <svg xmlns="http://www.w3.org/2000/svg" width="61.894" height="61.281" viewBox="0 0 61.894 61.281"
                  [ngClass]="{'inCart': (cart|async | productQuantity:item.code) > 0}">
                    <g id="Raggruppa_5225" data-name="Raggruppa 5225" transform="translate(1.701 -0.462)">
                      <path id="Tracciato_4685" data-name="Tracciato 4685" d="M37.084,8,29.1,23.581Z" transform="translate(15.018 3.884)" fill="none"/>
                      <g id="Raggruppa_4838" data-name="Raggruppa 4838" transform="translate(-1.701 0.462)">
                        <path id="Tracciato_4817" data-name="Tracciato 4817" d="M49.559,34.8,61.894,22.848V9.842a3.174,3.174,0,1,0-6.331,0V19.919l-8.514,8.2H21.4L11.789,0H3.166A3.29,3.29,0,0,0,0,3.4,3.29,3.29,0,0,0,3.166,6.8H7.532l8.842,26.012L10.916,48.392h41.59a3.407,3.407,0,0,0,0-6.8H20.085l2.292-6.913H49.559Z" fill="#9c9d9f"/>
                        <ellipse id="Ellisse_1" data-name="Ellisse 1" cx="5.24" cy="5.624" rx="5.24" ry="5.624" transform="translate(12.226 50.032)" fill="#9c9d9f"/>
                        <ellipse id="Ellisse_2" data-name="Ellisse 2" cx="5.24" cy="5.624" rx="5.24" ry="5.624" transform="translate(42.354 50.032)" fill="#9c9d9f"/>
                      </g>
                    </g>
                  </svg>
              </td>
              <td *ngIf="!isProspect" class="fix quantity" data-label="">
                <button (click)="removeQuantity(item)" >
                  <ion-icon name="remove-outline"></ion-icon>
                </button>
                <button (click)="addOneToCart(item)" >
                  <ion-icon name="add-outline"></ion-icon>
                </button>
              </td>
              <td data-label="">
                <div style="display: none;">{{item | json}}</div>
                <img [src]="item.image | safe" width="60" height="60" onerror="this.onerror=null;this.src='../../../../assets/icon/placeholder_100x100.png'">
              </td>
              <td [attr.data-label]="itemLabel" class="item-description" [ngClass]="{'focusProd': item.focus === 'S' }" >
                {{item.code | zeroRemover}}<br/>
                <div innerHTML="{{item.name}}"></div>
                <div *ngIf="item.divisionStatusCode === 'Z3'" class="z3"></div>
              </td>
              <td *ngFor="let col of featureColumns; let i = index" [attr.data-label]="col"><div innerHTML="{{getValue(item, col)}}"></div></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
