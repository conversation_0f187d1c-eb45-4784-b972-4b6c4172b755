import { createAction, props } from "@ngrx/store";
import { CatalogCard } from "src/app/service/data/catalog-card";

export const resetNavHistory = createAction(
  '[NavHistory] reset'
);

export const addNavHistory = createAction(
  '[NavHistory] add',
  props<{ item: { type: 'CATEGORY' | 'PRODUCT', id: string, path: string[], datacolCategory: null | { category: CatalogCard, isFavorite: boolean } } }>()
);

export const removeLastNavHistory = createAction(
  '[NavHistory] removeLast'
);
