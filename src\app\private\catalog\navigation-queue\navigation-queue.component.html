<div #title id="title" class="title" [ngClass]="{favorites: isFavorites}">
  <ng-container *ngIf="!isFavorites" ><span (click)="goTo()">{{ 'CATALOG.CATALOG' | translate }}</span><span class="subcat" *ngFor="let subcategory of (navigationQueue$ | async)">&nbsp;/&nbsp;<span (click)="goTo(subcategory.item.id)" [innerHTML]="subcategory.item.name"></span></span></ng-container>
  <ng-container *ngIf="isFavorites" >{{ 'GENERICS.FAVORITES' | translate}} - <span [innerHTML]="customerName"></span></ng-container>
</div>
