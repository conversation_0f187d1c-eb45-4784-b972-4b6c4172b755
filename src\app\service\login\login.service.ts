import { Injectable } from '@angular/core';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';
import { LoginRequest } from '../data/login-request';

@Injectable({
  providedIn: 'root'
})
export class LoginService extends BasicService {

  //LOGIN
  login(input:LoginRequest): Promise<BasicResponse> {
    return this.basicPost(input, "/auth/login", true);
  }

}
