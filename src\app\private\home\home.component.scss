.welcome {
    text-align: center;
    h1 {
        font-weight: bold;
    }
}

ion-button#datacolButton {
    --background: var(--ion-dat-red);
    --color: var(--ion-dat-white);
}

.container {
    max-width: 600px;
    margin: auto;
    .grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        grid-gap: 0.5rem;
        > div.cell {
            background: var(--ion-dat-gray);
            padding: 1rem;
            display: flex;
            place-items: center;
            &:active {
                transform: translateY(5px);
            }
            &.special{
                background-color: var(--ion-dat-red);
                ion-icon {
                    color: var(--ion-dat-white) !important;
                }
                .card-title {
                    color: var(--ion-dat-white) !important;
                }
            }
            &::before {
                // for apsect ratio
                content: "";
                display: block;
                padding-bottom: 100%;
                grid-area: 1 / 1 / 2 / 2;
            }
            .card-content {
                text-align: center;
                display: block;
                margin: auto;
                .card-title {
                    text-transform: uppercase;
                    font-size: 1.1em;
                    margin-bottom: 1.2em;
                    margin-top: .7em;
                    font-weight: bold;
                    color: var(--ion-dat-dark-gray);
                };
                ion-icon {
                    font-size: 70px;
                    color: var(--ion-dat-red);
                }
                ion-button {
                    height: 1.8em;
                    width: 12em;
                }
            }
        }
    }
}

/* TABLET ORIZZONTALE */
@media screen and (max-height: 700px) and (orientation: landscape) {
    .container {
        margin-top: 40px;
        max-width: 460px;
    }
}

.counter-container {
    width: 100%;
    text-align: center;
    .cart-counter {
        margin: auto;
        font-size: 30px;
        background-image: url('../../../assets/svg/red-cart.svg');
        background-repeat: no-repeat;
        background-position: center;
        width: 70px;
        height: 70px;
        color: var(--ion-dat-dark-gray);
        padding-left: 7px;
        font-weight: bold;
    }
}
