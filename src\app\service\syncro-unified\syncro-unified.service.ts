import { Injectable, inject } from '@angular/core';
import { BasicService } from '../basic.service';
import { BasicResponse } from '../data/basic-response';
import { SyncroV2Service } from '../syncro-v2/syncro-v2.service';
import { SyncroV2DbService } from '../syncro-v2/syncro-v2-db.service';
import { HybridDbService } from '../../shared/hybrid-db.service';
import { TagFilterService } from '../../private/services/tag-filter.service';
import { Category } from '../data/category';
import { Product } from '../data/product';
import { ProspectType } from '../data/prospect-type';
import { Discount } from '../data/discount';
import { Favorite } from '../data/favorite';
import { Customer } from '../data/customer';
import Utils from '../../shared/utils';
import { Store, select } from '@ngrx/store';

export interface UnifiedSyncProgress {
  currentStep: string;
  currentPage?: number;
  totalPages?: number;
  totalCount?: number;
  categoriesProcessed: number;
  productsProcessed: number;
  prospectsProcessed: number;
  discountsProcessed: number;
  favoritesProcessed: number;
  customersProcessed: number;
  tagsProcessed: number;
  hierarchyProcessed: number;
  isComplete: boolean;
  error?: string;
}

export interface UnifiedSyncResult {
  success: boolean;
  totalCategoriesProcessed: number;
  totalProductsProcessed: number;
  totalProspectsProcessed: number;
  totalDiscountsProcessed: number;
  totalFavoritesProcessed: number;
  totalCustomersProcessed: number;
  duration: number;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class SyncroUnifiedService extends BasicService {

  private _syncroV2Service = inject(SyncroV2Service);
  private _syncroV2DbService = inject(SyncroV2DbService);
  private _hybridDbService = inject(HybridDbService);
  private _store = inject(Store<any>);
  private _tagFilterService = inject(TagFilterService);

  constructor() {
    super();
  }

  /**
   * Helper per creare oggetti UnifiedSyncProgress con valori di default
   */
  private createProgress(
    currentStep: string,
    categoriesProcessed: number = 0,
    productsProcessed: number = 0,
    prospectsProcessed: number = 0,
    discountsProcessed: number = 0,
    favoritesProcessed: number = 0,
    customersProcessed: number = 0,
    tagsProcessed: number = 0,
    hierarchyProcessed: number = 0,
    isComplete: boolean = false,
    currentPage?: number,
    totalPages?: number,
    totalCount?: number,
    error?: string
  ): UnifiedSyncProgress {
    return {
      currentStep,
      currentPage,
      totalPages,
      totalCount,
      categoriesProcessed,
      productsProcessed,
      prospectsProcessed,
      discountsProcessed,
      favoritesProcessed,
      customersProcessed,
      tagsProcessed,
      hierarchyProcessed,
      isComplete,
      error
    };
  }

  /**
   * Esegue una sincronizzazione completa unificata
   * @param salesOrganization Organizzazione di vendita per gli sconti
   * @param pageSize Dimensione della pagina per la paginazione (default: 10)
   * @param startSyncInstant Timestamp di inizio sincronizzazione incrementale
   * @param onProgress Callback per il progresso
   * @returns Promise<UnifiedSyncResult>
   */
  async syncComplete(
    salesOrganization: string,
    pageSize: number = 100,
    startSyncInstant?: number,
    onProgress?: (progress: UnifiedSyncProgress) => void
  ): Promise<UnifiedSyncResult> {
    const startTime = Date.now();
    let totalCategoriesProcessed = 0;
    let totalProductsProcessed = 0;
    let totalProspectsProcessed = 0;
    let totalDiscountsProcessed = 0;
    let totalFavoritesProcessed = 0;
    let totalCustomersProcessed = 0;

    try {
      // Inizializza il database V2 se necessario
      await this._syncroV2DbService.updateDatabaseSchema();

      // Fase 0: Ottieni l'ID del catalogo dalla lingua dell'utente
      if (onProgress) {
        onProgress(this.createProgress('Recupero informazioni catalogo'));
      }

      const catalogId = await this.getCatalogIdFromLanguage();
      if (!catalogId) {
        return {
          success: false,
          totalCategoriesProcessed: 0,
          totalProductsProcessed: 0,
          totalProspectsProcessed: 0,
          totalDiscountsProcessed: 0,
          totalFavoritesProcessed: 0,
          totalCustomersProcessed: 0,
          duration: Date.now() - startTime,
          error: 'Impossibile ottenere l\'ID del catalogo dalla lingua dell\'utente'
        };
      }

      console.log(`📚 ID Catalogo ottenuto: ${catalogId}`);

      // Fase 1: Sincronizza le categorie con V2
      if (onProgress) {
        onProgress(this.createProgress('Sincronizzazione categorie'));
      }

      const categoriesResult = await this.syncCategories(
        catalogId,
        pageSize,
        startSyncInstant,
        (progress) => {
          if (onProgress) {
            onProgress(this.createProgress(
              'Sincronizzazione categorie',
              progress.categoriesProcessed,
              0, 0, 0, 0, 0, 0, 0, false,
              progress.currentPage,
              progress.totalPages,
              progress.totalCount
            ));
          }
        }
      );

      if (!categoriesResult.success) {
        return {
          success: false,
          totalCategoriesProcessed: 0,
          totalProductsProcessed: 0,
          totalProspectsProcessed: 0,
          totalDiscountsProcessed: 0,
          totalFavoritesProcessed: 0,
          totalCustomersProcessed: 0,
          duration: Date.now() - startTime,
          error: categoriesResult.error
        };
      }

      totalCategoriesProcessed = categoriesResult.totalCategoriesProcessed;

      // Fase 2: Estrai e salva i prodotti dalle categorie sincronizzate
      if (onProgress) {
        onProgress(this.createProgress(
          'Elaborazione prodotti dalle categorie',
          totalCategoriesProcessed
        ));
      }

      const productsResult = await this.extractAndSaveProductsFromCategories(
        startSyncInstant,
        (processed) => {
          if (onProgress) {
            onProgress(this.createProgress(
              'Elaborazione prodotti dalle categorie',
              totalCategoriesProcessed,
              processed
            ));
          }
        }
      );

      totalProductsProcessed = productsResult;

      // Fase 3: Sincronizza i prospect types
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione prospect types',
          totalCategoriesProcessed,
          totalProductsProcessed
        ));
      }

      const prospectsResult = await this.syncProspectTypes(startSyncInstant, (processed) => {
        if (onProgress) {
          onProgress(this.createProgress(
            'Sincronizzazione prospect types',
            totalCategoriesProcessed,
            totalProductsProcessed,
            processed
          ));
        }
      });

      totalProspectsProcessed = prospectsResult;

      // Fase 4: Sincronizza gli sconti
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione sconti',
          totalCategoriesProcessed,
          totalProductsProcessed,
          totalProspectsProcessed
        ));
      }

      const discountsResult = await this.syncDiscounts(salesOrganization, startSyncInstant, (processed) => {
        if (onProgress) {
          onProgress(this.createProgress(
            'Sincronizzazione sconti',
            totalCategoriesProcessed,
            totalProductsProcessed,
            totalProspectsProcessed,
            processed
          ));
        }
      });

      totalDiscountsProcessed = discountsResult;

      // Fase 5: Sincronizza i preferiti
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione preferiti',
          totalCategoriesProcessed,
          totalProductsProcessed,
          totalProspectsProcessed,
          totalDiscountsProcessed
        ));
      }

      const favoritesResult = await this.syncFavorites(startSyncInstant, (processed) => {
        if (onProgress) {
          onProgress(this.createProgress(
            'Sincronizzazione preferiti',
            totalCategoriesProcessed,
            totalProductsProcessed,
            totalProspectsProcessed,
            totalDiscountsProcessed,
            processed
          ));
        }
      });

      totalFavoritesProcessed = favoritesResult;

      // Fase 6: Sincronizza i clienti
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione clienti',
          totalCategoriesProcessed,
          totalProductsProcessed,
          totalProspectsProcessed,
          totalDiscountsProcessed,
          totalFavoritesProcessed
        ));
      }

      const customersResult = await this.syncCustomers(startSyncInstant, (processed) => {
        if (onProgress) {
          onProgress(this.createProgress(
            'Sincronizzazione clienti',
            totalCategoriesProcessed,
            totalProductsProcessed,
            totalProspectsProcessed,
            totalDiscountsProcessed,
            totalFavoritesProcessed,
            processed
          ));
        }
      });

      totalCustomersProcessed = customersResult;

      // Fase 7: Sincronizza tag e gerarchia settori
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione tag e gerarchia settori',
          totalCategoriesProcessed,
          totalProductsProcessed,
          totalProspectsProcessed,
          totalDiscountsProcessed,
          totalFavoritesProcessed,
          totalCustomersProcessed
        ));
      }

      const tagsResult = await this.syncTagsAndHierarchy(catalogId, (tagsProcessed, hierarchyProcessed) => {
        if (onProgress) {
          onProgress(this.createProgress(
            'Sincronizzazione tag e gerarchia settori',
            totalCategoriesProcessed,
            totalProductsProcessed,
            totalProspectsProcessed,
            totalDiscountsProcessed,
            totalFavoritesProcessed,
            totalCustomersProcessed,
            tagsProcessed,
            hierarchyProcessed
          ));
        }
      });

      // Completamento
      if (onProgress) {
        onProgress(this.createProgress(
          'Sincronizzazione completata',
          totalCategoriesProcessed,
          totalProductsProcessed,
          totalProspectsProcessed,
          totalDiscountsProcessed,
          totalFavoritesProcessed,
          totalCustomersProcessed,
          tagsResult.tagsProcessed,
          tagsResult.hierarchyProcessed,
          true
        ));
      }

      return {
        success: true,
        totalCategoriesProcessed,
        totalProductsProcessed,
        totalProspectsProcessed,
        totalDiscountsProcessed,
        totalFavoritesProcessed,
        totalCustomersProcessed,
        duration: Date.now() - startTime
      };

    } catch (error) {
      console.error('Errore durante la sincronizzazione unificata:', error);

      return {
        success: false,
        totalCategoriesProcessed,
        totalProductsProcessed,
        totalProspectsProcessed,
        totalDiscountsProcessed,
        totalFavoritesProcessed,
        totalCustomersProcessed,
        duration: Date.now() - startTime,
        error: error.message
      };
    }
  }

  /**
   * Sincronizza le categorie utilizzando il servizio V2 con paginazione completa
   */
  private async syncCategories(
    catalogId: number,
    pageSize: number,
    startSyncInstant?: number,
    onProgress?: (progress: { currentPage: number; totalPages?: number; totalCount?: number; categoriesProcessed: number }) => void
  ): Promise<{ success: boolean; totalCategoriesProcessed: number; error?: string }> {
    try {
      console.log(`🔄 Avvio sincronizzazione categorie per catalogo ${catalogId} con pageSize ${pageSize}`);

      let totalCategoriesProcessed = 0;

      // Segna l'inizio della sincronizzazione
      await this._syncroV2DbService.markSyncStart(catalogId, Date.now());

      // Utilizza il metodo syncAllCategories del SyncroV2Service per gestire automaticamente tutte le pagine
      const allResponses = await this._syncroV2Service.syncAllCategories(
        catalogId,
        pageSize,
        (pageData: BasicResponse, pageNumber: number, totalPages?: number, totalCount?: number) => {
          // Estrai le categorie dalla struttura della risposta
          let categories: Category[] = [];

          // Gestisce la nuova struttura con content.results
          if (pageData.content && pageData.content.results && Array.isArray(pageData.content.results)) {
            categories = pageData.content.results;
          } else if (pageData.content && Array.isArray(pageData.content)) {
            // Fallback per la struttura precedente
            categories = pageData.content;
          } else {
            console.warn(`⚠️ Pagina ${pageNumber}: content non ha una struttura valida`, pageData.content);
            return;
          }

          console.log(`📄 Pagina ${pageNumber + 1}/${totalPages || '?'} - ${categories.length} categorie`);

          // Aggiorna il contatore
          totalCategoriesProcessed += categories.length;

          // Callback di progresso
          if (onProgress) {
            onProgress({
              currentPage: pageNumber,
              totalPages,
              totalCount,
              categoriesProcessed: totalCategoriesProcessed
            });
          }
        }
      );

      // Verifica se ci sono stati errori
      const hasErrors = allResponses.some(response => response.status === 'KO');
      if (hasErrors) {
        const errorResponses = allResponses.filter(response => response.status === 'KO');
        console.error('❌ Errori durante la sincronizzazione delle categorie:', errorResponses);

        // Se ci sono errori ma anche successi, considera parzialmente riuscita
        const successResponses = allResponses.filter(response => response.status === 'OK');
        if (successResponses.length > 0) {
          console.warn('⚠️ Sincronizzazione categorie parzialmente riuscita');
        } else {
          throw new Error('Tutte le pagine di sincronizzazione categorie sono fallite');
        }
      }

      // Salva tutte le categorie nel database
      console.log('💾 Salvataggio categorie nel database...');
      const timestamp = Date.now();
      let savedCount = 0;

      for (let i = 0; i < allResponses.length; i++) {
        const response = allResponses[i];
        if (response.status === 'OK' && response.content) {
          try {
            // Estrai le categorie dalla struttura della risposta
            let categories: Category[] = [];

            // Gestisce la nuova struttura con content.results
            if (response.content.results && Array.isArray(response.content.results)) {
              categories = response.content.results;
            } else if (Array.isArray(response.content)) {
              // Fallback per la struttura precedente
              categories = response.content;
            } else {
              console.warn(`⚠️ Pagina ${i}: content non ha una struttura valida per il salvataggio`, response.content);
              continue;
            }

            if (categories.length > 0) {
              await this._syncroV2DbService.saveCategoriesV2(
                categories,
                catalogId,
                i, // pageNumber
                timestamp
              );
              savedCount += categories.length;
              console.log(`✅ Salvate ${categories.length} categorie dalla pagina ${i}`);
            }
          } catch (saveError) {
            console.error(`❌ Errore nel salvataggio della pagina ${i}:`, saveError);
          }
        }
      }

      console.log(`✅ Sincronizzazione categorie completata: ${savedCount} categorie salvate su ${totalCategoriesProcessed} processate`);

      // Debug: verifica immediata delle categorie salvate
      try {
        const savedCategories = await this._hybridDbService.getAll(['categories'], ['id']);
        console.log(`🔍 Verifica post-salvataggio categorie: ${savedCategories.length} categorie nel database`);
        if (savedCategories.length > 0) {
          console.log('📋 Esempi di categorie salvate:', savedCategories.slice(0, 3).map(cat => ({ id: cat.id, name: cat.name })));
        }
      } catch (debugError) {
        console.warn('⚠️ Errore nel debug delle categorie:', debugError);
      }

      return {
        success: true,
        totalCategoriesProcessed: savedCount
      };

    } catch (error) {
      console.error('❌ Errore durante la sincronizzazione delle categorie:', error);
      return {
        success: false,
        totalCategoriesProcessed: 0,
        error: error.message
      };
    }
  }

  /**
   * Estrae e salva i prodotti dalle categorie già sincronizzate
   * Gestisce il fatto che l'API syncCategories restituisce sia categorie che prodotti
   */
  private async extractAndSaveProductsFromCategories(
    startSyncInstant?: number,
    onProgress?: (processed: number) => void
  ): Promise<number> {
    try {
      console.log('🔄 Estrazione prodotti dalle categorie sincronizzate...');

      // Ottieni tutte le categorie dal database che hanno isProduct = true
      const productCategories = await this._hybridDbService.getRecordsByANDCondition(
        'categories',
        [{ key: 'isProduct', value: 'true' }]
      ) as Category[];

      console.log(`📦 Trovate ${productCategories.length} categorie-prodotto da elaborare`);

      if (productCategories.length === 0) {
        console.log('ℹ️ Nessuna categoria-prodotto trovata');
        return 0;
      }

      let totalProductsProcessed = 0;
      const columns: string[] = [];
      const allRecords: { andConditionList: { key: string; value: any }[]; values: any[] }[] = [];

      // Elabora ogni categoria-prodotto
      for (const category of productCategories) {
        try {
          // Converte la categoria in prodotto
          const product = this.convertCategoryToProduct(category);

          if (product) {
            // Prepara le colonne se non sono già state preparate
            if (columns.length === 0) {
              columns.push(...this.prepareColumns([], product));
            }

            // Prepara i valori per questo prodotto
            const values = this.prepareValues([], product);
            const keys = [{ key: 'code', value: product.code }];

            allRecords.push({ andConditionList: keys, values: values });
            totalProductsProcessed++;

            if (onProgress && totalProductsProcessed % 100 === 0) {
              onProgress(totalProductsProcessed);
            }
          }
        } catch (error) {
          console.error(`❌ Errore nell'elaborazione della categoria-prodotto ${category.id}:`, error);
        }
      }

      // Salva tutti i prodotti in batch
      if (allRecords.length > 0) {
        console.log(`💾 Salvataggio di ${allRecords.length} prodotti estratti dalle categorie...`);
        await this.saveDataBatch('products', allRecords, columns, true, startSyncInstant);
        console.log(`✅ Salvati ${allRecords.length} prodotti estratti dalle categorie`);
      }

      if (onProgress) {
        onProgress(totalProductsProcessed);
      }

      return totalProductsProcessed;

    } catch (error) {
      console.error('❌ Errore durante l\'estrazione dei prodotti dalle categorie:', error);
      return 0;
    }
  }

  /**
   * Converte una categoria con isProduct=true in un oggetto Product
   */
  private convertCategoryToProduct(category: Category): Product | null {
    try {
      // Crea un oggetto Product dalla categoria
      const product = new Product();

      // Mappa i campi dalla categoria al prodotto
      product.code = category.id; // Usa l'ID della categoria come codice prodotto
      product.name = category.name;
      product.description = category.description;
      product.image = category.image || category.imageUrl;
      product.thumbnail = category.thumbnail;
      product.idSubCategory = category.idSubCategory;
      product.idCategory = category.idRootCategory;
      product.focus = category.focus?.toString();
      product.divisionStatusCode = category.divisionStatusCode;
      product.modifiedInstant = category.modifiedInstant?.toString();

      // Campi specifici del prodotto con valori di default
      product.minimumDeliveryQuantity = '1';
      product.professionJobProduct = 'false';

      return product;
    } catch (error) {
      console.error(`❌ Errore nella conversione categoria-prodotto ${category.id}:`, error);
      return null;
    }
  }

  /**
   * Sincronizza i prospect types
   */
  private async syncProspectTypes(
    startSyncInstant?: number,
    onProgress?: (processed: number) => void
  ): Promise<number> {
    try {
      console.log('🔄 Sincronizzazione prospect types...');

      const response: BasicResponse = await this.allprospectsbyuser(startSyncInstant);

      if (!response || response.status === 'KO') {
        console.error('Errore nella risposta prospect types:', response?.error?.details);
        return 0;
      }

      if (response.content && Array.isArray(response.content) && response.content.length > 0) {
        const columns = this.prepareColumns([], response.content[0] as ProspectType);
        const records: { andConditionList: { key: string; value: any }[]; values: any[] }[] = [];

        response.content.forEach((prospectType: ProspectType) => {
          const values = this.prepareValues([], prospectType);
          if (!prospectType.cancellationInstant) {
            records.push({
              andConditionList: [{ key: 'uid', value: prospectType.uid }],
              values: values
            });
          }
        });

        if (records.length > 0) {
          await this.saveDataBatch('prospectTypes', records, columns, true, startSyncInstant);
          console.log(`✅ Salvati ${records.length} prospect types`);

          if (onProgress) {
            onProgress(records.length);
          }

          return records.length;
        }
      }

      return 0;

    } catch (error) {
      console.error('Errore durante la sincronizzazione dei prospect types:', error);
      return 0;
    }
  }

  /**
   * Sincronizza gli sconti
   */
  private async syncDiscounts(
    salesOrganization: string,
    startSyncInstant?: number,
    onProgress?: (processed: number) => void
  ): Promise<number> {
    try {
      console.log('🔄 Sincronizzazione sconti...');

      const response: BasicResponse = await this.discountlist(salesOrganization, startSyncInstant);

      if (!response || response.status === 'KO') {
        console.error('Errore nella risposta sconti:', response?.error?.details);
        return 0;
      }

      if (response.content && Array.isArray(response.content) && response.content.length > 0) {
        // Pulisci la tabella sconti prima di inserire i nuovi dati
        await this._hybridDbService.clearTable('discounts');

        const columns = this.prepareColumns([], response.content[0] as Discount);
        const records: { andConditionList: { key: string; value: any }[]; values: any[] }[] = [];

        response.content.forEach((discount: Discount) => {
          const values = this.prepareValues([], discount);
          const keys = [
            { key: 'salesOrganization', value: discount.salesOrganization },
            { key: 'commissionGroup', value: discount.commissionGroup },
            { key: 'discount', value: discount.discount }
          ];
          records.push({ andConditionList: keys, values: values });
        });

        if (records.length > 0) {
          await this.saveDataBatch('discounts', records, columns, true, startSyncInstant);
          console.log(`✅ Salvati ${records.length} sconti`);

          if (onProgress) {
            onProgress(records.length);
          }

          return records.length;
        }
      }

      return 0;

    } catch (error) {
      console.error('Errore durante la sincronizzazione degli sconti:', error);
      return 0;
    }
  }

  /**
   * Sincronizza i preferiti
   */
  private async syncFavorites(
    startSyncInstant?: number,
    onProgress?: (processed: number) => void
  ): Promise<number> {
    try {
      console.log('🔄 Sincronizzazione preferiti...');

      const response: BasicResponse = await this.getProducts();

      if (!response || response.status === 'KO') {
        console.error('Errore nella risposta preferiti:', response?.error?.details);
        return 0;
      }

      if (response.content && Array.isArray(response.content) && response.content.length > 0) {
        // Pulisci la tabella preferiti prima di inserire i nuovi dati
        await this._hybridDbService.clearTable('favorites');

        let totalProcessed = 0;

        for (const favorite of response.content as Favorite[]) {
          await this._hybridDbService.addRecord(
            'favorites',
            ['idCategory', 'customerUid', 'catalogCard'],
            [favorite.idCategory, favorite.customerUid, favorite.catalogCard]
          );
          totalProcessed++;
        }

        console.log(`✅ Salvati ${totalProcessed} preferiti`);

        if (onProgress) {
          onProgress(totalProcessed);
        }

        return totalProcessed;
      }

      return 0;

    } catch (error) {
      console.error('Errore durante la sincronizzazione dei preferiti:', error);
      return 0;
    }
  }

  /**
   * Sincronizza i clienti
   */
  private async syncCustomers(
    startSyncInstant?: number,
    onProgress?: (processed: number) => void
  ): Promise<number> {
    try {
      console.log('🔄 Sincronizzazione clienti...');

      // Crea l'indice unico per la tabella customers se non esiste
      let forceDownload = false;
      try {
        await this._hybridDbService.createUniqueIndex('customers', ['uid']);
      } catch (error) {
        await this._hybridDbService.clearTable('customers');
        await this._hybridDbService.createUniqueIndex('customers', ['uid']);
        forceDownload = true;
      }

      const response: BasicResponse = await this.allcustomersbyuser(forceDownload ? null : startSyncInstant);

      console.log('📥 Risposta API clienti:', {
        status: response?.status,
        contentLength: response?.content?.length,
        hasContent: !!response?.content,
        forceDownload,
        startSyncInstant
      });

      if (!response || response.status === 'KO') {
        console.error('❌ Errore nella risposta clienti:', response?.error?.details);
        return 0;
      }

      if (response.content && Array.isArray(response.content) && response.content.length > 0) {
        console.log(`📋 Processando ${response.content.length} clienti dalla risposta API`);

        const columns = this.prepareColumns([], response.content[0] as Customer);
        const records: { andConditionList: { key: string; value: any }[]; values: any[] }[] = [];

        let processedCount = 0;
        let cancelledCount = 0;

        for (const customer of response.content as Customer[]) {
          const values = this.prepareValues([], customer);
          if (!customer.cancellationInstant) {
            records.push({
              andConditionList: [{ key: 'uid', value: customer.uid }],
              values: values
            });
            processedCount++;
          } else {
            // Elimina il cliente cancellato
            try {
              await this._hybridDbService.deleteRecord('customers', [{ key: 'uid', value: `'${customer.uid}'` }]);
              console.log(`Cliente cancellato: ${customer.uid}`);
              cancelledCount++;
            } catch (deleteError) {
              console.error(`Errore nell'eliminazione del cliente ${customer.uid}:`, deleteError);
            }
          }
        }

        console.log(`📊 Clienti processati: ${processedCount} attivi, ${cancelledCount} cancellati`);
        console.log(`💾 Preparando salvataggio di ${records.length} record clienti`);
        console.log(`🔍 Esempio primo cliente:`, records[0] ? {
          uid: records[0].andConditionList[0]?.value,
          valuesCount: records[0].values?.length
        } : 'Nessun record');

        if (records.length > 0) {
          console.log(`💾 Avvio salvataggio batch di ${records.length} clienti...`);
          try {
            await this.saveDataBatch('customers', records, columns, true, startSyncInstant);
            console.log(`✅ Salvati ${records.length} clienti nel database`);

            // Verifica immediata del salvataggio
            const savedCustomers = await this._hybridDbService.getAll(['customers'], ['uid']);
            console.log(`🔍 Verifica post-salvataggio: ${savedCustomers.length} clienti nel database`);

            if (onProgress) {
              onProgress(records.length);
            }

            return records.length;
          } catch (saveError) {
            console.error('❌ Errore durante il salvataggio dei clienti:', saveError);
            throw saveError;
          }
        } else {
          console.log('⚠️ Nessun cliente da salvare (records.length = 0)');
        }
      } else {
        console.log('⚠️ Nessun cliente nella risposta API:', {
          hasContent: !!response.content,
          isArray: Array.isArray(response.content),
          length: response.content?.length
        });
      }

      return 0;

    } catch (error) {
      const wasRecreated = await this.handleDatabaseError(error, 'sincronizzazione clienti');

      if (wasRecreated) {
        console.log('🔄 Database ricreato, la sincronizzazione dei clienti dovrà essere ripetuta');
      }

      return 0;
    }
  }

  // ===== METODI API =====

  /**
   * Ottiene l'ID del catalogo dalla lingua dell'utente
   */
  private async getCatalogIdFromLanguage(): Promise<number | null> {
    try {
      const language = localStorage.getItem('language') || 'it';
      console.log(`🌐 Recupero ID catalogo per lingua: ${language}`);

      const response: BasicResponse = await this._syncroV2Service.getCatalogByLang(language);

      if (!response || response.status === 'KO') {
        console.error('❌ Errore nel recupero del catalogo:', response?.error?.details);
        return null;
      }

      if (response.content && response.content.id) {
        const catalogId = parseInt(response.content.id);
        console.log(`✅ ID Catalogo trovato: ${catalogId} per lingua ${language}`);
        return catalogId;
      } else {
        console.error('❌ Nessun catalogo trovato per la lingua:', language);
        return null;
      }
    } catch (error) {
      console.error('❌ Errore durante il recupero dell\'ID del catalogo:', error);
      return null;
    }
  }

  /**
   * Ottiene i prodotti per categoria
   */
  private productsbycategory(categoryId: string, startInstant?: number): Promise<BasicResponse> {
    const language = localStorage.getItem('language');
    const incr = !!startInstant ? '&fromTimestamp=' + startInstant : '';
    return this.basicGet(`/catalogs/getsync/productsbycategory?fields=CUSTOM_PROD&lang=${language}&categoryId=${categoryId}${incr}`, false);
  }

  /**
   * Ottiene tutti i prospect types per utente
   */
  private allprospectsbyuser(startInstant?: number): Promise<BasicResponse> {
    const incr = !!startInstant ? '?fromTimestamp=' + startInstant : '';
    return this.basicGet("/customers/getsync/allprospectsbyuser" + incr, false);
  }

  /**
   * Ottiene la lista degli sconti
   */
  private discountlist(salesOrganization: string, startInstant?: number): Promise<BasicResponse> {
    const incr = !!startInstant ? '&fromTimestamp=' + startInstant : '';
    return this.basicGet(`/catalogs/getsync/discountlist?salesOrganisation=${salesOrganization}${incr}`, false);
  }

  /**
   * Ottiene i prodotti preferiti
   */
  private getProducts(): Promise<BasicResponse> {
    return this.basicGet("/favorites/getProducts", false);
  }

  /**
   * Ottiene tutti i clienti per utente
   */
  private allcustomersbyuser(startInstant?: number): Promise<BasicResponse> {
    const incr = !!startInstant ? '?fromTimestamp=' + startInstant : '';
    return this.basicGet(`/customers/getsync/allcustomersbyuser${incr}`, false);
  }

  // ===== METODI HELPER =====

  /**
   * Ottiene tutte le root categories dal database
   */
  async getRootCategories(): Promise<Category[]> {
    try {
      const categories = await this._hybridDbService.getRecordsByANDCondition(
        'categories',
        [{ key: 'isRootCategory', value: 'true' }]
      );
      console.log(`🔍 getRootCategories: trovate ${categories.length} root categories nel database`);
      return categories as Category[];
    } catch (error) {
      console.error('❌ Errore nel recupero delle root categories:', error);
      return [];
    }
  }

  /**
   * Ottiene le root categories identificate tramite regex durante la sincronizzazione v2
   * @param idCatalog ID del catalogo (opzionale)
   * @returns Promise<Category[]> Array delle root categories
   */
  async getRootCategoriesByRegex(idCatalog?: number): Promise<Category[]> {
    try {
      return await this._syncroV2DbService.getRootCategoriesByRegex(idCatalog);
    } catch (error) {
      console.error('❌ Errore nel recupero delle root categories tramite regex:', error);
      return [];
    }
  }

  /**
   * Ottiene le categorie figlie di una categoria specifica
   */
  async getCategoriesByParent(parentIdApp: string): Promise<Category[]> {
    try {
      // Utilizza lo store per recuperare le categorie
      return new Promise<Category[]>((resolve, reject) => {
        let categories: Category[] = [];
        this._store.pipe(select('categories')).subscribe(res => {
          categories = [...res].filter(cat => {
            return cat.idApp.includes(parentIdApp);
        });
        }).unsubscribe();
        console.log(`🔍 getCategoriesByParent (store): trovate ${categories.length} categorie figlie per parent ${parentIdApp}`);
        resolve(categories);
      });
    } catch (error) {
      console.error(`❌ Errore nel recupero delle categorie figlie per parent ${parentIdApp} dallo store:`, error);
      return [];
    }
  }



  /**
   * Ottiene una categoria specifica per ID
   */
  async getCategoryById(categoryId: string): Promise<Category | null> {
    try {
      const categories = await this._hybridDbService.getRecordsByANDCondition(
        'categories',
        [{ key: 'id', value: categoryId }]
      );
      if (categories.length > 0) {
        return categories[0] as Category;
      }
      return null;
    } catch (error) {
      console.error(`❌ Errore nel recupero della categoria ${categoryId}:`, error);
      return null;
    }
  }

  /**
   * Ottiene un prodotto specifico per codice
   */
  async getProductByCode(productCode: string): Promise<Product | null> {
    try {
      const products = await this._hybridDbService.getRecordsByANDCondition(
        'products',
        [{ key: 'code', value: productCode }]
      );
      if (products.length > 0) {
        return products[0] as Product;
      }
      return null;
    } catch (error) {
      console.error(`❌ Errore nel recupero del prodotto ${productCode}:`, error);
      return null;
    }
  }

  /**
   * Prepara le colonne per il salvataggio nel database
   */
  private prepareColumns(columns: string[], obj: any): string[] {
    if (columns.length === 0) {
      Object.keys(obj).forEach(key => {
        if (!columns.includes(key)) {
          columns.push(key);
        }
      });
    }
    return columns;
  }

  /**
   * Prepara i valori per il salvataggio nel database
   */
  private prepareValues(values: string[], obj: any): string[] {
    Object.entries(obj).forEach(([, value]) => {
      values.push(Utils.objToJson(value === null ? '' : value));
    });
    return values;
  }

  /**
   * Salva i dati nel database in batch
   */
  private async saveDataBatch(
    tableName: string,
    records: { andConditionList: { key: string; value: any }[]; values: any[] }[],
    columns: string[],
    withId: boolean,
    startSyncInstant?: number
  ): Promise<void> {
    try {
      const simpleRecords = !startSyncInstant ? records.map((record) => record.values) : records;
      const splitted = this.splitRecords(simpleRecords, 25);

      if (startSyncInstant && withId) {
        columns = ['id'].concat(columns);
      }

      for (const recordsGroup of splitted) {
        if (!startSyncInstant || tableName === 'favorites') {
          await this._hybridDbService.addRecords(tableName, columns, recordsGroup);
          console.log(`Inserted [${tableName}]: ${recordsGroup.length}`);
        } else {
          // Per la sincronizzazione incrementale, usa insert or replace
          for (const record of recordsGroup) {
            if (Array.isArray(record)) {
              await this._hybridDbService.addRecord(tableName, columns, record);
            }
          }
          console.log(`Updated [${tableName}]: ${recordsGroup.length}`);
        }
      }
    } catch (error) {
      console.error(`Error saving [${tableName}]:`, error);
      throw error;
    }
  }

  /**
   * Divide i record in gruppi più piccoli per il batch processing
   */
  private splitRecords(records: any[], batchSize: number): any[][] {
    const result: any[][] = [];
    for (let i = 0; i < records.length; i += batchSize) {
      result.push(records.slice(i, i + batchSize));
    }
    return result;
  }

  /**
   * Inizializza il database per la sincronizzazione unificata
   */
  async initializeDatabase(): Promise<void> {
    try {
      console.log('🔧 Inizializzazione database per sincronizzazione unificata...');

      // Verifica la disponibilità del database
      await this.checkDatabaseAvailability();

      await this._syncroV2DbService.updateDatabaseSchema();
      console.log('✅ Database inizializzato con successo');
    } catch (error) {
      console.error('❌ Errore durante l\'inizializzazione del database:', error);
      throw error;
    }
  }

  /**
   * Verifica la disponibilità del database
   */
  private async checkDatabaseAvailability(): Promise<void> {
    try {
      // Test di base per verificare se il database è accessibile
      await this._hybridDbService.getAll(['categories'], ['id']);
      console.log('✅ Database accessibile');
    } catch (error) {
      console.error('❌ Database non accessibile:', error);

      // Se l'errore è relativo alla versione del database, prova a ricrearlo
      if (error.message && error.message.includes('Database version error')) {
        console.log('🔄 Tentativo di ricreazione del database...');
        try {
          await this._hybridDbService.recreateDatabase();
          console.log('✅ Database ricreato con successo');
          return; // Database ricreato, non lanciare errore
        } catch (recreateError) {
          console.error('❌ Errore durante la ricreazione del database:', recreateError);
        }
      }

      throw new Error(`Database non disponibile: ${error.message}`);
    }
  }

  /**
   * Pulisce le tabelle prima di una sincronizzazione completa
   */
  async clearTablesForFullSync(): Promise<void> {
    try {
      console.log('🧹 Pulizia tabelle per sincronizzazione completa...');

      const tablesToClear = ['categories', 'products', 'prospectTypes', 'discounts', 'favorites', 'customers'];
      const errors: string[] = [];

      for (const table of tablesToClear) {
        try {
          await this._hybridDbService.clearTable(table);
          console.log(`✅ Tabella ${table} pulita`);
        } catch (error) {
          const errorMsg = `Errore pulizia tabella ${table}: ${error.message}`;
          console.warn(`⚠️ ${errorMsg}`);
          errors.push(errorMsg);
          // Continua con le altre tabelle invece di fermarsi
        }
      }

      if (errors.length > 0) {
        console.warn(`⚠️ Alcune tabelle non sono state pulite: ${errors.join(', ')}`);
        // Non lanciare errore, ma continua la sincronizzazione
      } else {
        console.log('✅ Tutte le tabelle sono state pulite');
      }
    } catch (error) {
      console.error('❌ Errore critico durante la pulizia delle tabelle:', error);
      // Anche in caso di errore critico, non bloccare la sincronizzazione
      console.warn('⚠️ Continuando la sincronizzazione nonostante gli errori di pulizia...');
    }
  }

  /**
   * Verifica lo stato della sincronizzazione
   */
  async getSyncStatus(catalogId: number): Promise<{
    lastSyncTimestamp: number | null;
    categoriesCount: number;
    productsCount: number;
    prospectsCount: number;
    discountsCount: number;
    favoritesCount: number;
    customersCount: number;
  }> {
    try {
      const lastSyncTimestamp = await this._syncroV2DbService.getLastSyncTimestamp(catalogId);

      const [categories, products, prospects, discounts, favorites, customers] = await Promise.all([
        this._hybridDbService.getAll(['categories']),
        this._hybridDbService.getAll(['products']),
        this._hybridDbService.getAll(['prospectTypes']),
        this._hybridDbService.getAll(['discounts']),
        this._hybridDbService.getAll(['favorites']),
        this._hybridDbService.getAll(['customers'])
      ]);

      return {
        lastSyncTimestamp,
        categoriesCount: categories.length,
        productsCount: products.length,
        prospectsCount: prospects.length,
        discountsCount: discounts.length,
        favoritesCount: favorites.length,
        customersCount: customers.length
      };
    } catch (error) {
      console.error('Errore nel recupero dello stato di sincronizzazione:', error);
      return {
        lastSyncTimestamp: null,
        categoriesCount: 0,
        productsCount: 0,
        prospectsCount: 0,
        discountsCount: 0,
        favoritesCount: 0,
        customersCount: 0
      };
    }
  }

  /**
   * Salva i preferiti sul server
   */
  async sendFavorites(favorites: Favorite[]): Promise<BasicResponse> {
    return this.basicPost({ favorites }, "/favorites/saveProducts", false);
  }

  /**
   * Gestisce gli errori di database e tenta la ricreazione se necessario
   */
  private async handleDatabaseError(error: any, context: string): Promise<boolean> {
    console.error(`❌ Errore database in ${context}:`, error);

    if (error.message && error.message.includes('Database version error')) {
      console.log(`🔄 Tentativo di ricreazione del database per ${context}...`);
      try {
        await this._hybridDbService.recreateDatabase();
        console.log('✅ Database ricreato con successo');
        return true; // Indica che il database è stato ricreato
      } catch (recreateError) {
        console.error('❌ Errore durante la ricreazione del database:', recreateError);
      }
    }

    return false; // Indica che il database non è stato ricreato
  }

  /**
   * Metodo di debug per verificare lo stato dei clienti nel database
   */
  async debugCustomersStatus(): Promise<{
    customersCount: number;
    sampleCustomers: any[];
    error?: string;
  }> {
    try {
      const customers = await this._hybridDbService.getAll(['customers'], ['uid', 'name']);
      const sampleCustomers = customers.slice(0, 5); // Primi 5 clienti come esempio

      console.log(`📊 Debug clienti: ${customers.length} clienti trovati nel database`);
      if (sampleCustomers.length > 0) {
        console.log('👥 Esempi di clienti:', sampleCustomers);
      }

      return {
        customersCount: customers.length,
        sampleCustomers: sampleCustomers
      };
    } catch (error) {
      console.error('❌ Errore nel debug dei clienti:', error);
      return {
        customersCount: 0,
        sampleCustomers: [],
        error: error.message
      };
    }
  }

  /**
   * Sincronizza i tag DC e la gerarchia dei settori
   * @param catalogId ID del catalogo
   * @param onProgress Callback per il progresso
   * @returns Promise con il numero di tag e gerarchia processati
   */
  private async syncTagsAndHierarchy(
    catalogId: number,
    onProgress?: (tagsProcessed: number, hierarchyProcessed: number) => void
  ): Promise<{ tagsProcessed: number; hierarchyProcessed: number; success: boolean; error?: string }> {
    try {
      console.log(`🏷️ [SYNC_UNIFIED] Avvio sincronizzazione tag e gerarchia per catalogo ${catalogId}`);

      let tagsProcessed = 0;
      let hierarchyProcessed = 0;

      // Sincronizza la gerarchia dei settori
      console.log('📊 [SYNC_UNIFIED] Sincronizzazione gerarchia settori...');
      const hierarchyResponse = await this._syncroV2Service.getSectorHierarchy(catalogId);
      if (hierarchyResponse.status === 'OK') {
        await this.saveSectorHierarchyToDatabase(catalogId, hierarchyResponse.content);
        hierarchyProcessed = 1;
        console.log('✅ [SYNC_UNIFIED] Gerarchia settori sincronizzata');

        if (onProgress) {
          onProgress(tagsProcessed, hierarchyProcessed);
        }
      } else {
        console.log('❌ [SYNC_UNIFIED] Errore nella sincronizzazione gerarchia settori:', hierarchyResponse.error);
      }

      // Sincronizza i tag DC
      console.log('🏷️ [SYNC_UNIFIED] Sincronizzazione tag DC delle categorie...');
      const tagsResponse = await this._syncroV2Service.syncDCTags(
        catalogId,
        0,
        500,
        Date.now() - (30 * 24 * 60 * 60 * 1000) // Ultimi 30 giorni
      );
      if (tagsResponse.status === 'OK') {
        tagsProcessed = await this.saveDCTagsToDatabase(catalogId, tagsResponse.content);
        console.log(`✅ [SYNC_UNIFIED] ${tagsProcessed} tag DC sincronizzati`);

        if (onProgress) {
          onProgress(tagsProcessed, hierarchyProcessed);
        }
      } else {
        console.log('❌ [SYNC_UNIFIED] Errore nella sincronizzazione tag DC:', tagsResponse.error);
      }

      console.log('🏷️ [SYNC_UNIFIED] Sincronizzazione tag e gerarchia completata');

      // Invalida la cache dei tag per forzare il ricaricamento
      this._tagFilterService.invalidateTagsCache();

      return {
        tagsProcessed,
        hierarchyProcessed,
        success: true
      };

    } catch (error) {
      console.error('❌ [SYNC_UNIFIED] Errore durante la sincronizzazione di tag e gerarchia:', error);
      return {
        tagsProcessed: 0,
        hierarchyProcessed: 0,
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Salva la gerarchia dei settori nel database locale
   */
  private async saveSectorHierarchyToDatabase(idCatalog: number, content: any): Promise<void> {
    try {
      console.log(`💾 [SYNC_UNIFIED] Salvataggio gerarchia settori per catalogo ${idCatalog}...`);

      // Prima elimina i dati esistenti per questo catalogo
      await this._hybridDbService.deleteRecord('sector_hierarchy', [
        { key: 'idCatalog', value: String(idCatalog) }
      ]);

      const timestamp = Date.now();

      // Salva tutti i tag della gerarchia in modo ricorsivo
      if (content.tags && content.tags.length > 0) {
        await this.saveTags(content.tags, idCatalog, timestamp, null, 1);
      }

      // Salva il tag universale se presente
      if (content.universalTag) {
        await this.saveTag(content.universalTag, idCatalog, timestamp, null, 0);
      }

      console.log(`✅ [SYNC_UNIFIED] Gerarchia settori salvata per catalogo ${idCatalog}`);
    } catch (error) {
      console.error(`❌ [SYNC_UNIFIED] Errore nel salvataggio gerarchia settori:`, error);
      throw error;
    }
  }

  /**
   * Salva i tag DC nel database locale
   */
  private async saveDCTagsToDatabase(idCatalog: number, content: any): Promise<number> {
    try {
      console.log(`💾 [SYNC_UNIFIED] Salvataggio ${content?.length || 0} tag DC per catalogo ${idCatalog}`);

      if (!content || !Array.isArray(content)) {
        console.log(`⚠️ [SYNC_UNIFIED] Nessun tag DC da salvare per catalogo ${idCatalog}`);
        return 0;
      }

      const timestamp = Date.now();
      let savedCount = 0;

      // Salva ogni tag DC nel database
      for (const dcTag of content) {
        if (dcTag.idSubCategory && dcTag.tags && Array.isArray(dcTag.tags)) {
          // Prima elimina i tag esistenti per questa categoria
          await this._hybridDbService.deleteRecord(
            'datacol_category_tags',
            [
              { key: 'idSubCategory', value: String(dcTag.idSubCategory) },
              { key: 'idCatalog', value: String(idCatalog) }
            ]
          );

          // Poi inserisce i nuovi tag
          for (const tag of dcTag.tags) {
            await this._hybridDbService.addRecord(
              'datacol_category_tags',
              ['idSubCategory', 'idCatalog', 'keyTag', 'description', 'lastSync'],
              [
                String(dcTag.idSubCategory),
                String(idCatalog),
                tag.keyTag || '',
                tag.description || '',
                String(timestamp)
              ]
            );
            savedCount++;
          }
        }
      }

      console.log(`✅ [SYNC_UNIFIED] Salvati ${savedCount} tag DC per catalogo ${idCatalog}`);
      return savedCount;
    } catch (error) {
      console.error(`❌ [SYNC_UNIFIED] Errore nel salvataggio tag DC:`, error);
      throw error;
    }
  }

  /**
   * Salva ricorsivamente i tag della gerarchia
   */
  private async saveTags(tags: any[], idCatalog: number, timestamp: number, parentIdTag: number | null, level: number): Promise<void> {
    for (const tag of tags) {
      await this.saveTag(tag, idCatalog, timestamp, parentIdTag, level);

      // Salva ricorsivamente i sottotag
      if (tag.subTags && tag.subTags.length > 0) {
        await this.saveTags(tag.subTags, idCatalog, timestamp, tag.idTag, level + 1);
      }
    }
  }

  /**
   * Salva un singolo tag nel database
   */
  private async saveTag(tag: any, idCatalog: number, timestamp: number, parentIdTag: number | null, level: number): Promise<void> {
    try {
      await this._hybridDbService.addRecord(
        'sector_hierarchy',
        ['idCatalog', 'idTag', 'keyTag', 'description', 'parentIdTag', 'level', 'lastSync'],
        [
          String(idCatalog),
          String(tag.idTag),
          tag.keyTag,
          tag.description,
          parentIdTag ? String(parentIdTag) : null,
          String(level),
          String(timestamp)
        ]
      );
    } catch (error) {
      console.error(`❌ [SYNC_UNIFIED] Errore nel salvataggio del tag ${tag.keyTag}:`, error);
      throw error;
    }
  }
}
