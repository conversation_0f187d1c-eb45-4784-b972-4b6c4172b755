import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SafePipe } from 'src/app/shared/safe-pipe';

@Component({
    selector: 'app-videoplayer',
    templateUrl: './videoplayer.component.html',
    styleUrls: ['./videoplayer.component.scss'],
    standalone: true,
    imports: [
      SafePipe      
    ]
})
export class VideoplayerComponent {
  @Input() videoUrl: String;
  @Output() hideVideo= new EventEmitter();

  close(event) {
    event.stopPropagation();
    this.hideVideo.emit();
  }

}
