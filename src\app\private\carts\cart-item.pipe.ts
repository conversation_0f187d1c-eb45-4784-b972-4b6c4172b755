import { Pipe, PipeTransform } from '@angular/core';
import { CartItem } from 'src/app/service/data/cart-item';
import { CartItemExtended } from 'src/app/service/data/cart-item-extended';

@Pipe({
    name: 'cartItem',
    standalone: true,
    pure: true
})
export class CartItemPipe implements PipeTransform {

  transform(items: CartItem[]): CartItemExtended[] {
    return items.map(({id, name, idCustomer, products}) => ({
          id, 
          name, 
          idCustomer,
          quantity: products.reduce((result, item) => {
                return result + item.quantity;
              }, 0)
        }));
  }

}
