import { createReducer, on } from "@ngrx/store";
import { 
  TagFilterState, 
  CustomerTagFilter, 
  FilterConfiguration 
} from "../../private/model/sector-hierarchy-response";
import {
  setTagFilterState,
  setActiveCustomerFilter,
  updateCurrentFilter,
  addCustomTag,
  removeCustomTag,
  setFilteringEnabled,
  loadFilterConfigurations,
  saveFilterConfiguration,
  deleteFilterConfiguration,
  selectFilterConfiguration,
  resetTagFilter,
  setFilteredCategories,
  updateCategoryVisibility,
  applyDefaultConfiguration,
  applyCustomConfiguration
} from "../actions/tag-filter.actions";

// Stato iniziale del filtro tag
const initialTagFilterState: TagFilterState = {
  activeCustomerUid: null,
  currentFilter: null,
  availableConfigurations: [],
  isFilteringEnabled: false,
  lastFilterUpdate: 0
};

// Stato iniziale per le categorie filtrate
interface FilteredCategoriesState {
  visibleCategoryIds: string[];
  hiddenCategoryIds: string[];
  lastUpdate: number;
}

const initialFilteredCategoriesState: FilteredCategoriesState = {
  visibleCategoryIds: [],
  hiddenCategoryIds: [],
  lastUpdate: 0
};

// Reducer per lo stato del filtro tag
export const tagFilterReducer = createReducer(
  initialTagFilterState,
  
  // Imposta l'intero stato del filtro
  on(setTagFilterState, (state, { filterState }) => ({
    ...filterState
  })),

  // Imposta il filtro per un cliente specifico
  on(setActiveCustomerFilter, (state, { customerUid, filter }) => ({
    ...state,
    activeCustomerUid: customerUid,
    currentFilter: filter,
    isFilteringEnabled: true,
    lastFilterUpdate: Date.now()
  })),

  // Aggiorna il filtro corrente
  on(updateCurrentFilter, (state, { filter }) => ({
    ...state,
    currentFilter: filter,
    lastFilterUpdate: Date.now()
  })),

  // Aggiunge un tag personalizzato
  on(addCustomTag, (state, { keyTag }) => {
    if (!state.currentFilter) {
      return state;
    }

    // Verifica che il tag non sia già presente
    if (state.currentFilter.customTags.includes(keyTag)) {
      return state;
    }

    const updatedFilter: CustomerTagFilter = {
      ...state.currentFilter,
      customTags: [...state.currentFilter.customTags, keyTag],
      isDefault: false
    };

    return {
      ...state,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };
  }),

  // Rimuove un tag personalizzato
  on(removeCustomTag, (state, { keyTag }) => {
    if (!state.currentFilter) {
      return state;
    }

    // Verifica che non sia un tag predefinito
    const predefinedTags = [
      state.currentFilter.settore,
      state.currentFilter.attivita,
      state.currentFilter.professione,
      'UNIVERSALE'
    ].filter(Boolean);

    if (predefinedTags.includes(keyTag)) {
      console.warn(`Tentativo di rimuovere tag predefinito: ${keyTag}`);
      return state;
    }

    const updatedFilter: CustomerTagFilter = {
      ...state.currentFilter,
      customTags: state.currentFilter.customTags.filter(tag => tag !== keyTag)
    };

    return {
      ...state,
      currentFilter: updatedFilter,
      lastFilterUpdate: Date.now()
    };
  }),

  // Abilita/disabilita il filtraggio
  on(setFilteringEnabled, (state, { enabled }) => ({
    ...state,
    isFilteringEnabled: enabled,
    lastFilterUpdate: Date.now()
  })),

  // Carica le configurazioni disponibili
  on(loadFilterConfigurations, (state, { configurations }) => ({
    ...state,
    availableConfigurations: configurations
  })),

  // Salva una nuova configurazione
  on(saveFilterConfiguration, (state, { configuration }) => {
    const existingIndex = state.availableConfigurations.findIndex(
      config => config.id === configuration.id
    );

    let updatedConfigurations: FilterConfiguration[];
    if (existingIndex >= 0) {
      // Aggiorna configurazione esistente
      updatedConfigurations = [...state.availableConfigurations];
      updatedConfigurations[existingIndex] = configuration;
    } else {
      // Aggiungi nuova configurazione
      updatedConfigurations = [...state.availableConfigurations, configuration];
    }

    return {
      ...state,
      availableConfigurations: updatedConfigurations
    };
  }),

  // Elimina una configurazione
  on(deleteFilterConfiguration, (state, { configurationId }) => ({
    ...state,
    availableConfigurations: state.availableConfigurations.filter(
      config => config.id !== configurationId
    )
  })),

  // Seleziona una configurazione
  on(selectFilterConfiguration, (state, { configurationId }) => {
    const selectedConfig = state.availableConfigurations.find(
      config => config.id === configurationId
    );

    if (!selectedConfig) {
      return state;
    }

    // Crea un filtro basato sulla configurazione selezionata
    const filter: CustomerTagFilter = {
      customerUid: selectedConfig.customerUid,
      settore: undefined, // Questi verranno estratti dai tag attivi
      attivita: undefined,
      professione: undefined,
      universale: selectedConfig.activeTags.includes('UNIVERSALE'),
      customTags: selectedConfig.activeTags.filter(tag => tag !== 'UNIVERSALE'),
      isDefault: selectedConfig.isDefault
    };

    return {
      ...state,
      currentFilter: filter,
      lastFilterUpdate: Date.now()
    };
  }),

  // Applica configurazione predefinita
  on(applyDefaultConfiguration, (state, { customerUid }) => {
    // Trova la configurazione predefinita per il cliente
    const defaultConfig = state.availableConfigurations.find(
      config => config.customerUid === customerUid && config.isDefault
    );

    if (!defaultConfig) {
      return state;
    }

    const filter: CustomerTagFilter = {
      customerUid: customerUid,
      settore: undefined,
      attivita: undefined,
      professione: undefined,
      universale: true,
      customTags: [],
      isDefault: true
    };

    return {
      ...state,
      activeCustomerUid: customerUid,
      currentFilter: filter,
      isFilteringEnabled: true,
      lastFilterUpdate: Date.now()
    };
  }),

  // Applica configurazione personalizzata
  on(applyCustomConfiguration, (state, { customerUid, configurationId }) => {
    const customConfig = state.availableConfigurations.find(
      config => config.id === configurationId && config.customerUid === customerUid
    );

    if (!customConfig) {
      return state;
    }

    const filter: CustomerTagFilter = {
      customerUid: customerUid,
      settore: undefined,
      attivita: undefined,
      professione: undefined,
      universale: customConfig.activeTags.includes('UNIVERSALE'),
      customTags: customConfig.activeTags.filter(tag => tag !== 'UNIVERSALE'),
      isDefault: false
    };

    return {
      ...state,
      activeCustomerUid: customerUid,
      currentFilter: filter,
      isFilteringEnabled: true,
      lastFilterUpdate: Date.now()
    };
  }),

  // Reset del filtro
  on(resetTagFilter, () => initialTagFilterState)
);

// Reducer per le categorie filtrate
export const filteredCategoriesReducer = createReducer(
  initialFilteredCategoriesState,

  // Imposta le categorie filtrate
  on(setFilteredCategories, (state, { visibleCategoryIds, hiddenCategoryIds }) => ({
    ...state,
    visibleCategoryIds,
    hiddenCategoryIds,
    lastUpdate: Date.now()
  })),

  // Aggiorna la visibilità di una singola categoria
  on(updateCategoryVisibility, (state, { categoryId, isVisible }) => {
    if (isVisible) {
      // Sposta dalla lista nascosta a quella visibile
      return {
        ...state,
        visibleCategoryIds: state.visibleCategoryIds.includes(categoryId) 
          ? state.visibleCategoryIds 
          : [...state.visibleCategoryIds, categoryId],
        hiddenCategoryIds: state.hiddenCategoryIds.filter(id => id !== categoryId),
        lastUpdate: Date.now()
      };
    } else {
      // Sposta dalla lista visibile a quella nascosta
      return {
        ...state,
        visibleCategoryIds: state.visibleCategoryIds.filter(id => id !== categoryId),
        hiddenCategoryIds: state.hiddenCategoryIds.includes(categoryId)
          ? state.hiddenCategoryIds
          : [...state.hiddenCategoryIds, categoryId],
        lastUpdate: Date.now()
      };
    }
  }),

  // Reset quando si resetta il filtro tag
  on(resetTagFilter, () => initialFilteredCategoriesState)
);
