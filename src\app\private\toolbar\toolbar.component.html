<div class="tb-content">
  <button (click)="search()" class="tbar-btn search"
    [ngClass]="{'visible': items.search }">
  </button>
  <button (click)="goTo('home')" class="tbar-btn home"
    [ngClass]="{'visible': items.home }">
  </button>
  <button (click)="goTo('catalog')" class="tbar-btn catalog {{catalogColor}}"
    [ngClass]="{'visible': items.catalog }">
  </button>
  <button (click)="goTo('extras')" class="tbar-btn extras {{extrasColor}}"
    [ngClass]="{'visible': items.extras }">
  </button>
  <button (click)="goTo('sync')" class="tbar-btn sync"
    [ngClass]="{'visible': items.sync }">
  </button>
  <button (click)="goTo('favorites')" class="tbar-btn favorites {{favoritesColor}}"
    [ngClass]="{'visible': items.favorites }">
  </button>
  <button (click)="goTo('customers')" class="tbar-btn customers"
    [ngClass]="{'visible': items.customers }">
  </button>
  <button (click)="goBackRefactor()" class="tbar-btn back"
    [ngClass]="{'visible': items.goback }">
  </button>
</div>
