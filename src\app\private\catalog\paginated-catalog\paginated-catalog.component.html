<div class="p-3">
  <!-- Loading state -->
  <div *ngIf="isLoading" class="loading-container">
    <ion-spinner></ion-spinner>
    <p>Caricamento...</p>
  </div>

  <!-- Main swiper container -->
  <div *ngIf="!isLoading">
    <swiper-container 
      #swiperContainer 
      (swiperslidechange)="slideChanges($event)" 
      class="h-full catalog-swiper" 
      [modules]="swiperModules"
      [slidesPerView]="1"
      [slidesPerGroup]="1"
      [spaceBetween]="0"
      [virtual]="false"
      [allowTouchMove]="true"
      [speed]="300">

      <!-- Slide per ogni pagina -->
      <swiper-slide 
        *ngFor="let pageIndex of getVirtualSlides(); trackBy: swiperTrackByFn" 
        class="page-slide">
        <div class="page-grid" 
             [style.grid-template-columns]="'repeat(' + columns + ', 1fr)'"
             [style.grid-template-rows]="'repeat(' + rows + ', 1fr)'">
          
          <!-- Mostra le cards se la pagina è caricata -->
          <ng-container *ngIf="isPageLoaded(pageIndex); else pageLoader">
            <div *ngFor="let item of getPageItems(pageIndex); trackBy: trackById" class="card-container">
              <app-small-catalog-card class="w-full"
                *ngIf="catalogService.isRootView"
                [item]="item" 
                (openCategory)="openRootCategory($event)">
              </app-small-catalog-card>
              
              <app-small-catalog-card 
                *ngIf="!catalogService.isRootView && catalogService.isSmallCardView"
                [item]="item" 
                (openCategory)="openCategory($event)">
              </app-small-catalog-card>
              
              <app-medium-catalog-card 
                *ngIf="catalogService.isMediumCardView"
                [item]="item" 
                (openCategory)="openCategory($event)">
              </app-medium-catalog-card>
              
              <app-large-catalog-card 
                *ngIf="catalogService.isLargeCardView"
                [item]="item" 
                (openCategory)="openCategory($event)">
              </app-large-catalog-card>
            </div>
            
            <!-- Slot vuoti per completare la griglia -->
            <div 
              *ngFor="let empty of createArray(getEmptySlotsCount(pageIndex)); let i = index; trackBy: swiperTrackByFn" 
              class="card-container empty-slot">
            </div>
          </ng-container>
          
          <!-- Template per pagina non caricata -->
          <ng-template #pageLoader>
            <div class="page-placeholder">
              <ion-spinner></ion-spinner>
              <p>Caricamento pagina {{ pageIndex + 1 }}...</p>
            </div>
          </ng-template>
        </div>
      </swiper-slide>
    </swiper-container>
  </div>

  <!-- Custom pagination -->
  <div *ngIf="!isLoading && totalPages > 1" class="custom-pagination">
    <!-- Bottone pagina precedente -->
    <span 
      *ngIf="currentPage > 1"
      class="pagination-arrow" 
      (click)="goToPage(currentPage - 1)">
      ‹
    </span>

    <!-- Prima pagina se non visibile -->
    <ng-container *ngIf="getVisiblePages()[0] > 1">
      <span class="pagination-number" (click)="goToPage(1)">1</span>
      <span *ngIf="getVisiblePages()[0] > 2" class="pagination-ellipsis">...</span>
    </ng-container>

    <!-- Pagine visibili -->
    <span 
      *ngFor="let page of getVisiblePages(); trackBy: swiperTrackByFn"
      class="pagination-number" 
      [class.active]="page === currentPage" 
      (click)="goToPage(page)">
      {{ page }}
    </span>

    <!-- Ultima pagina se non visibile -->
    <ng-container *ngIf="getVisiblePages()[getVisiblePages().length - 1] < totalPages">
      <span 
        *ngIf="getVisiblePages()[getVisiblePages().length - 1] < totalPages - 1" 
        class="pagination-ellipsis">
        ...
      </span>
      <span class="pagination-number" (click)="goToPage(totalPages)">{{ totalPages }}</span>
    </ng-container>

    <!-- Bottone pagina successiva -->
    <span 
      *ngIf="currentPage < totalPages"
      class="pagination-arrow" 
      (click)="goToPage(currentPage + 1)">
      ›
    </span>
  </div>

  <!-- Info pagina corrente -->
  <div *ngIf="!isLoading && totalPages > 1" class="pagination-info">
    Pagina {{ currentPage }} di {{ totalPages }} 
    ({{ totalItems }} elementi totali)
  </div>
</div>