import { Component, inject } from '@angular/core';
import { CatalogCardComponent } from '../catalog-card.component';
import { IonicModule } from '@ionic/angular';
import { TranslateModule } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { SafePipe } from 'src/app/shared/safe-pipe';
import { addIcons } from 'ionicons';
import { caretDownOutline, logoEuro, shareSocialOutline, star, starOutline } from 'ionicons/icons';
import { CatalogService } from 'src/app/service/catalog/catalog.service';

@Component({
    selector: 'app-medium-catalog-card',
    templateUrl: './medium-catalog-card.component.html',
    styleUrls: ['./medium-catalog-card.component.scss'],
    standalone: true,
    imports: [
      IonicModule,
      TranslateModule,
      CommonModule,
      SafePipe
    ]
})
export class MediumCatalogCardComponent extends CatalogCardComponent {

  protected catalogService = inject(CatalogService);

  constructor() {
    super();
    addIcons({ caretDownOutline, logoEuro, shareSocialOutline, star, starOutline });
  }

}
