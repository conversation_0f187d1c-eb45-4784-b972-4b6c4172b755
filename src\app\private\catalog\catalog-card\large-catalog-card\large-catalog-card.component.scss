@import '../_common.scss';

.card div {
  flex-direction: column;
  height: 100%;
}

.card.category.red-arrow {
  height: 225px;
  width: 85%;
  margin: auto;
}

.card.category.red-arrow .name {
  font-size: x-large;
}

.card.white-card div div.detail .name {
  color: var(--ion-dat-red);
  font-size: x-large;
  margin-top: 20px;
  display: inline-block;
  text-align: left;
  height: min-content !important;
  padding-left: 0;
}

.card.white-card div div.detail .description {
  margin-top: 20px;
  display: block;
  height: 100% !important;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: left;
  padding-right: 25px;
}

.card.white-card div div.detail .buttons {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 20px;
  height: min-content !important;
  ion-icon {
    height: 30px;
    font-size: 25px;
    background: var(--ion-dat-middle-gray);
    padding: 5px;
    border-radius: 3px;
    color: var(--ion-dat-white);
    display: block;
    margin-right: 25px;
  }
  ion-icon[name='star-outline'] {
    font-size: 30px;
    background: none;
    color: var(--ion-dat-middle-gray);
  }
  ion-icon[name='star'] {
    font-size: 30px;
    background: none;
    color: var(--ion-dat-yellow);
  }
}

.card div div.icon,
.card div div.detail {
  height: 50% !important;
}

.card div div.icon {
  width: 100%;
}

::ng-deep .swiper-slide .large img {
  width: auto !important;
  max-width: 100% !important;
  height: 100% !important;
  max-height: 100% !important;
}

.card div div.to-be-continued {
  margin-bottom: 16px;
}

.focusProd {
  background-size: 20px;
}


div.buttons .productsCount {
  text-align: left;
  align-items: baseline !important;
  height: 40px !important;
  justify-content: center;
  color: var(--ion-dat-middle-gray);
}
