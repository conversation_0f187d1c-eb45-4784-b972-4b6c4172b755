table {
   width: 100%;
    .price-class {
        border: 1px solid var(--ion-dat-black);
        font-size: xx-large !important;
        font-weight: bold;
        color: var(--ion-dat-black);
        background-color: var(--ion-dat-white);
   }
   .title {
        background-color: var(--ion-dat-red);
        color: var(--ion-dat-white);
        font-weight: bold;
        font-size: x-large;
        text-align: left;
   }
   td {
        background-color: var(--ion-dat-dark-gray);
        color: var(--ion-dat-white);
        font-weight: bold;
        text-align: center;
        padding: 10px;
        font-size: x-large;
        &.discount {
            font-weight: initial;
        }
        &.price {
            background-color: var(--ion-dat-black);
        }
   }

 
}
.scroll {
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
}