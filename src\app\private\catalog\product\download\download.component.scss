.popover {
    background: var(--ion-dat-popover);
    position: absolute;
    top: 0;
    left: 0;
    height: 100vh;
    width: 100vw;
    z-index: 9999;
    display: flex;
    .container {
        background-color: var(--ion-dat-white);
        min-height: 50vh;
        max-height: 600px;
        width: 50vw;
        margin: auto;
        .header {
            width: 100%;
            text-align: right;
            padding: 20px;
            .close {
                background-color: transparent;
                justify-content: end;
            }
        }
        .row {
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-top: -77px;
            padding: 25px;
            text-align: center;
            h1 {
                text-transform: uppercase;
                font-weight: bold;
                font-size: 19px;
                color: var(--ion-dat-black);
            }
            .documents {
                display: block;
                margin-top: 20px;
                overflow: scroll;
                max-height: calc(600px - 200px);
                &::-webkit-scrollbar {
                  width: 12px;
                }
                &::-webkit-scrollbar-thumb {
                    background-color: var(--ion-dat-gray);
                    border-radius: 20px;
                }
                .single {
                    display: block;
                    float: left;
                    width: 170px;
                    height: 170px;
                    .document {
                        display: block;
                        // flex-direction: column;
                        margin: auto;
                        &:active {
                            transform: translateY(5px);
                        }
                        object {
                            width: 100px;
                            height: 100px;
                            pointer-events: none; /* questo serve per a far funzionare il click sulla svg */
                        }
                        .fileName {
                            word-break: break-all;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }
            }
        }
    }
}
