/**
 * Configurazione per il Sistema di Sincronizzazione v2
 * 
 * Questo file contiene tutte le configurazioni e costanti utilizzate
 * dal sistema di sincronizzazione v2.
 */

export interface SyncroV2Config {
  // Configurazioni API
  api: {
    timeout: number;
    maxRetries: number;
    retryDelay: number;
    baseUrl?: string;
  };

  // Configurazioni paginazione
  pagination: {
    categoriesPageSize: number;
    productsPageSize: number;
    deletedCategoriesPageSize: number;
    maxPagesPerBatch: number;
  };

  // Configurazioni immagini
  images: {
    enableOfflineDownload: boolean;
    maxConcurrentDownloads: number;
    supportedFormats: string[];
    maxFileSize: number; // in bytes
    compressionQuality: number; // 0-1
  };

  // Configurazioni database
  database: {
    batchSize: number;
    enableIndexes: boolean;
    vacuumInterval: number; // in milliseconds
  };

  // Configurazioni pulizia
  cleanup: {
    enableAutoCleanup: boolean;
    dataRetentionDays: number;
    imageRetentionDays: number;
    cleanupInterval: number; // in milliseconds
  };

  // Configurazioni logging
  logging: {
    enableDetailedLogs: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    maxLogEntries: number;
  };

  // Configurazioni performance
  performance: {
    enableProgressCallbacks: boolean;
    progressUpdateInterval: number; // in milliseconds
    enablePerformanceMetrics: boolean;
  };
}

/**
 * Configurazione di default per il sistema di sincronizzazione v2
 */
export const DEFAULT_SYNCRO_V2_CONFIG: SyncroV2Config = {
  api: {
    timeout: 30000, // 30 secondi
    maxRetries: 3,
    retryDelay: 1000, // 1 secondo
  },

  pagination: {
    categoriesPageSize: 50,
    productsPageSize: 100,
    deletedCategoriesPageSize: 10,
    maxPagesPerBatch: 10,
  },

  images: {
    enableOfflineDownload: true,
    maxConcurrentDownloads: 3,
    supportedFormats: ['jpg', 'jpeg', 'png', 'webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    compressionQuality: 0.8,
  },

  database: {
    batchSize: 100,
    enableIndexes: true,
    vacuumInterval: 24 * 60 * 60 * 1000, // 24 ore
  },

  cleanup: {
    enableAutoCleanup: true,
    dataRetentionDays: 30,
    imageRetentionDays: 7,
    cleanupInterval: 24 * 60 * 60 * 1000, // 24 ore
  },

  logging: {
    enableDetailedLogs: true,
    logLevel: 'info',
    maxLogEntries: 1000,
  },

  performance: {
    enableProgressCallbacks: true,
    progressUpdateInterval: 500, // 0.5 secondi
    enablePerformanceMetrics: true,
  },
};

/**
 * Configurazione ottimizzata per dispositivi con connessione lenta
 */
export const SLOW_CONNECTION_CONFIG: Partial<SyncroV2Config> = {
  pagination: {
    categoriesPageSize: 25,
    productsPageSize: 50,
    deletedCategoriesPageSize: 5,
    maxPagesPerBatch: 5,
  },

  api: {
    timeout: 60000, // 60 secondi
    maxRetries: 5,
    retryDelay: 2000, // 2 secondi
  },

  images: {
    maxConcurrentDownloads: 1,
    maxFileSize: 2 * 1024 * 1024, // 2MB
    compressionQuality: 0.6,
  },
};

/**
 * Configurazione ottimizzata per dispositivi con storage limitato
 */
export const LIMITED_STORAGE_CONFIG: Partial<SyncroV2Config> = {
  images: {
    enableOfflineDownload: false,
    maxFileSize: 1 * 1024 * 1024, // 1MB
  },

  cleanup: {
    dataRetentionDays: 7,
    imageRetentionDays: 3,
    cleanupInterval: 12 * 60 * 60 * 1000, // 12 ore
  },

  database: {
    batchSize: 50,
    vacuumInterval: 12 * 60 * 60 * 1000, // 12 ore
  },
};

/**
 * Configurazione per ambiente di sviluppo/debug
 */
export const DEBUG_CONFIG: Partial<SyncroV2Config> = {
  logging: {
    enableDetailedLogs: true,
    logLevel: 'debug',
    maxLogEntries: 5000,
  },

  performance: {
    enablePerformanceMetrics: true,
    progressUpdateInterval: 100, // 0.1 secondi per debug più frequente
  },

  pagination: {
    categoriesPageSize: 10, // Pagine più piccole per test
    productsPageSize: 20,
    deletedCategoriesPageSize: 5,
  },
};

/**
 * Configurazione per ambiente di produzione
 */
export const PRODUCTION_CONFIG: Partial<SyncroV2Config> = {
  logging: {
    enableDetailedLogs: false,
    logLevel: 'error',
    maxLogEntries: 500,
  },

  api: {
    timeout: 45000, // 45 secondi
    maxRetries: 2,
  },

  performance: {
    enablePerformanceMetrics: false,
    progressUpdateInterval: 1000, // 1 secondo
  },
};

/**
 * Enum per i tipi di configurazione predefiniti
 */
export enum SyncroV2ConfigType {
  DEFAULT = 'default',
  SLOW_CONNECTION = 'slow_connection',
  LIMITED_STORAGE = 'limited_storage',
  DEBUG = 'debug',
  PRODUCTION = 'production',
}

/**
 * Factory per ottenere configurazioni predefinite
 */
export class SyncroV2ConfigFactory {
  
  /**
   * Ottiene una configurazione predefinita
   * @param type Tipo di configurazione
   * @returns Configurazione completa
   */
  static getConfig(type: SyncroV2ConfigType): SyncroV2Config {
    let partialConfig: Partial<SyncroV2Config>;

    switch (type) {
      case SyncroV2ConfigType.SLOW_CONNECTION:
        partialConfig = SLOW_CONNECTION_CONFIG;
        break;
      case SyncroV2ConfigType.LIMITED_STORAGE:
        partialConfig = LIMITED_STORAGE_CONFIG;
        break;
      case SyncroV2ConfigType.DEBUG:
        partialConfig = DEBUG_CONFIG;
        break;
      case SyncroV2ConfigType.PRODUCTION:
        partialConfig = PRODUCTION_CONFIG;
        break;
      default:
        partialConfig = {};
    }

    return this.mergeConfigs(DEFAULT_SYNCRO_V2_CONFIG, partialConfig);
  }

  /**
   * Crea una configurazione personalizzata
   * @param baseType Tipo di configurazione base
   * @param overrides Override personalizzati
   * @returns Configurazione personalizzata
   */
  static createCustomConfig(
    baseType: SyncroV2ConfigType = SyncroV2ConfigType.DEFAULT,
    overrides: Partial<SyncroV2Config> = {}
  ): SyncroV2Config {
    const baseConfig = this.getConfig(baseType);
    return this.mergeConfigs(baseConfig, overrides);
  }

  /**
   * Merge di due configurazioni
   * @param base Configurazione base
   * @param override Configurazione da sovrapporre
   * @returns Configurazione merged
   */
  private static mergeConfigs(
    base: SyncroV2Config,
    override: Partial<SyncroV2Config>
  ): SyncroV2Config {
    return {
      api: { ...base.api, ...override.api },
      pagination: { ...base.pagination, ...override.pagination },
      images: { ...base.images, ...override.images },
      database: { ...base.database, ...override.database },
      cleanup: { ...base.cleanup, ...override.cleanup },
      logging: { ...base.logging, ...override.logging },
      performance: { ...base.performance, ...override.performance },
    };
  }

  /**
   * Valida una configurazione
   * @param config Configurazione da validare
   * @returns true se valida, altrimenti lancia un errore
   */
  static validateConfig(config: SyncroV2Config): boolean {
    // Validazione API
    if (config.api.timeout <= 0) {
      throw new Error('API timeout deve essere maggiore di 0');
    }
    if (config.api.maxRetries < 0) {
      throw new Error('API maxRetries deve essere >= 0');
    }

    // Validazione paginazione
    if (config.pagination.categoriesPageSize <= 0) {
      throw new Error('categoriesPageSize deve essere maggiore di 0');
    }
    if (config.pagination.productsPageSize <= 0) {
      throw new Error('productsPageSize deve essere maggiore di 0');
    }

    // Validazione immagini
    if (config.images.maxConcurrentDownloads <= 0) {
      throw new Error('maxConcurrentDownloads deve essere maggiore di 0');
    }
    if (config.images.compressionQuality < 0 || config.images.compressionQuality > 1) {
      throw new Error('compressionQuality deve essere tra 0 e 1');
    }

    // Validazione cleanup
    if (config.cleanup.dataRetentionDays <= 0) {
      throw new Error('dataRetentionDays deve essere maggiore di 0');
    }

    return true;
  }

  /**
   * Ottiene la configurazione ottimale basata sulle caratteristiche del dispositivo
   * @param deviceInfo Informazioni sul dispositivo
   * @returns Configurazione ottimale
   */
  static getOptimalConfig(deviceInfo: {
    isSlowConnection?: boolean;
    hasLimitedStorage?: boolean;
    isDebugMode?: boolean;
    isProduction?: boolean;
  }): SyncroV2Config {
    if (deviceInfo.isProduction) {
      return this.getConfig(SyncroV2ConfigType.PRODUCTION);
    }
    
    if (deviceInfo.isDebugMode) {
      return this.getConfig(SyncroV2ConfigType.DEBUG);
    }
    
    if (deviceInfo.hasLimitedStorage) {
      return this.getConfig(SyncroV2ConfigType.LIMITED_STORAGE);
    }
    
    if (deviceInfo.isSlowConnection) {
      return this.getConfig(SyncroV2ConfigType.SLOW_CONNECTION);
    }
    
    return this.getConfig(SyncroV2ConfigType.DEFAULT);
  }
}

/**
 * Costanti per il sistema di sincronizzazione v2
 */
export const SYNCRO_V2_CONSTANTS = {
  // Nomi delle tabelle
  TABLES: {
    CATEGORIES: 'categories',
    PRODUCTS: 'products',
    OFFLINE_IMAGES: 'offline_images',
  },

  // Colonne aggiunte per v2
  V2_COLUMNS: {
    LAST_SYNC_TIMESTAMP: 'lastSyncTimestamp',
    ID_CATALOG: 'idCatalog',
    SYNC_PAGE_NUMBER: 'syncPageNumber',
    SYNCED_WITH_V2: 'syncedWithV2',
    IS_ROOT_CATEGORY_BY_REGEX: 'isRootCategoryByRegex',
    ID_ROOT_CATEGORY: 'idRootCategory',
    IMAGE_OFFLINE_AVAILABLE: 'imageOfflineAvailable',
  },

  // Endpoint API
  API_ENDPOINTS: {
    SYNC_CATEGORIES: '/appcatalogs/syncCategories',
    SYNC_PRODUCTS: '/appcatalogs/syncProducts',
    SYNC_DELETED_CATEGORIES: '/appcatalogs/syncDeletedCategories',
    GET_IMAGE: '/multimedia/get/image',
  },

  // Tipi di entità per immagini
  ENTITY_TYPES: {
    PRODUCT: 'PRODUCT' as const,
    CATEGORY: 'CATEGORY' as const,
  },

  // Stati di sincronizzazione
  SYNC_STATUS: {
    NOT_STARTED: 'NOT_STARTED',
    IN_PROGRESS: 'IN_PROGRESS',
    COMPLETED: 'COMPLETED',
    ERROR: 'ERROR',
  },

  // Formati immagine supportati
  SUPPORTED_IMAGE_FORMATS: ['jpg', 'jpeg', 'png', 'webp', 'gif'],

  // Directory per le immagini offline
  OFFLINE_IMAGES_DIR: 'images',
} as const;
