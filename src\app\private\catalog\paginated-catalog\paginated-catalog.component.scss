// Variabili principali
:host {
  display: block;
  width: 100%;
  height: 100%;
}

.catalog-swiper {
  height: calc(100vh - 200px);
  min-height: 400px;
  width: 100%;

  swiper-container {
    height: 100%;
    width: 100%;
  }

  swiper-slide {
    height: 100%;
    display: flex !important;
    align-items: center;
    justify-content: center;
  }
}

.page-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  height: 100%;
  width: 100%;
}

.page-grid {
  display: grid;
  gap: 15px;
  width: 100%;
  height: 100%;
  align-items: stretch;
  justify-items: center;
  
  // Responsive breakpoints
  @media (max-width: 768px) {
    gap: 10px;
  }
  
  @media (max-width: 480px) {
    gap: 8px;
  }
}

.card-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 120px;
  width: 100%;
  
  &.empty-slot {
    background: transparent;
    visibility: hidden;
  }
}

.page-placeholder {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--ion-color-medium);
  gap: 16px;
  
  p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
  }
  
  ion-spinner {
    --color: var(--ion-color-primary);
  }
}

.custom-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
  flex-wrap: wrap;
  padding: 0 16px;

  @media (max-width: 480px) {
    gap: 4px;
    margin-top: 16px;
  }
}

.pagination-number,
.pagination-arrow {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  border: 1px solid var(--ion-color-light-shade);
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  user-select: none;
  background: var(--ion-color-light);
  color: var(--ion-color-dark);

  &:hover {
    background-color: var(--ion-color-light-shade);
    border-color: var(--ion-color-medium);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  @media (max-width: 480px) {
    min-width: 32px;
    height: 32px;
    font-size: 12px;
  }
}

.pagination-number {
  &.active {
    background-color: var(--ion-color-primary);
    color: var(--ion-color-primary-contrast);
    border-color: var(--ion-color-primary);
    box-shadow: 0 2px 8px rgba(var(--ion-color-primary-rgb), 0.3);

    &:hover {
      background-color: var(--ion-color-primary-shade);
      border-color: var(--ion-color-primary-shade);
    }
  }
}

.pagination-arrow {
  font-size: 18px;
  font-weight: bold;
  
  @media (max-width: 480px) {
    font-size: 16px;
  }
}

.pagination-ellipsis {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 36px;
  font-size: 14px;
  color: var(--ion-color-medium);
  user-select: none;
  
  @media (max-width: 480px) {
    min-width: 24px;
    height: 32px;
    font-size: 12px;
  }
}

.pagination-info {
  text-align: center;
  margin-top: 10px;
  font-size: 12px;
  color: var(--ion-color-medium);
  padding: 8px 16px;
  
  @media (max-width: 480px) {
    font-size: 11px;
    margin-top: 8px;
  }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
  
  p {
    margin: 0;
    color: var(--ion-color-medium);
    font-size: 16px;
  }
  
  ion-spinner {
    --color: var(--ion-color-primary);
  }
}

// Animazioni
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-container {
  animation: fadeIn 0.3s ease-out;
}

// Fix per swiper in Ionic
swiper-container {
  --swiper-theme-color: var(--ion-color-primary);
  --swiper-navigation-color: var(--ion-color-primary);
  --swiper-pagination-color: var(--ion-color-primary);
}

// Responsive grid adjustments
@media (max-width: 1200px) {
  .page-grid {
    gap: 12px;
  }
}

@media (max-width: 992px) {
  .catalog-swiper {
    height: calc(100vh - 180px);
    min-height: 350px;
  }
}

@media (max-width: 768px) {
  .catalog-swiper {
    height: calc(100vh - 160px);
    min-height: 300px;
  }
  
  .page-slide {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .catalog-swiper {
    height: calc(100vh - 140px);
    min-height: 280px;
  }
  
  .page-slide {
    padding: 6px;
  }
  
  .card-container {
    min-height: 100px;
  }
}