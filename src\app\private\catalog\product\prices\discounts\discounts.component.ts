import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component, Input, OnChanges } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { IonicModule } from '@ionic/angular';
import { Discount } from 'src/app/service/data/discount';
import { HybridDbService } from 'src/app/shared/hybrid-db.service';
import { ZeroRemoverPipe } from 'src/app/shared/zero-remover.pipe';

@Component({
    selector: 'app-discounts',
    templateUrl: './discounts.component.html',
    styleUrls: ['./discounts.component.scss'],
    standalone: true,
    imports: [
      CommonModule,
      IonicModule,
      TranslateModule,
      ZeroRemoverPipe
    ]
})
export class DiscountsComponent implements OnChanges {
  @Input() productGp: string;
  @Input() productValueZR00 : string;
  @Input() productCode : string;
  @Input() productName : string;
  @Input() salesOrganization: string;
  @Input() discountList: string;
  discounts: Discount[] = [];
  constructor(private _dbService: HybridDbService,  private _cdr: ChangeDetectorRef) { }

  ngOnChanges() {
    this._dbService.getRecordsByANDCondition("discounts", [{key: 'salesOrganization', value: this.salesOrganization},{key: 'commissionGroup', value: this.productGp}]).then((data) => {
      if(!!data)
      {
        this.discounts = data.map( (item:Discount)=>{
          return item as unknown as Discount
        });
      }
      else
        this.discounts = [];
      this._cdr.markForCheck();
    });
  }

  formatPercent(percent:string){
    return (parseFloat(percent)).toFixed(0);
  }

  formatPrice(discount:string){
    const percent = parseInt(discount) / 100;
    const originalPrice = parseInt(this.productValueZR00);
    let price = originalPrice - (originalPrice*percent);
    return this.cutDecimals(price, 3);
  }

  private cutDecimals(number,decimals){
    return number.toLocaleString('fullwide', {maximumFractionDigits:decimals})
  }
}
