import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule, ToastController } from '@ionic/angular';
import { SimpleDbTestService } from './simple-db-test.service';
import { HybridDbService } from './hybrid-db.service';

@Component({
  selector: 'app-simple-db-test',
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  template: `
    <ion-header>
      <ion-toolbar>
        <ion-title>Test Database Unificato - Diagnostica</ion-title>
      </ion-toolbar>
    </ion-header>

    <ion-content class="ion-padding">
      <ion-card>
        <ion-card-header>
          <ion-card-title>Informazioni Sistema</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p><strong>Piattaforma:</strong> {{ platformInfo?.platform || 'Caricamento...' }}</p>
          <p><strong>Database:</strong> {{ platformInfo?.databaseType || 'Caricamento...' }}</p>
          <p><strong>Stato:</strong> 
            <ion-badge [color]="systemStatus === 'OK' ? 'success' : 'danger'">
              {{ systemStatus }}
            </ion-badge>
          </p>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Test Rapidi</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runQuickTest()"
            [disabled]="testing">
            <ion-icon name="flash-outline" slot="start"></ion-icon>
            Test Rapido Sistema
          </ion-button>

          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runOperationsTest()"
            [disabled]="testing">
            <ion-icon name="cog-outline" slot="start"></ion-icon>
            Test Operazioni Database
          </ion-button>

          <ion-button 
            expand="block" 
            fill="outline" 
            (click)="runPlatformTest()"
            [disabled]="testing">
            <ion-icon name="information-circle-outline" slot="start"></ion-icon>
            Test Informazioni Piattaforma
          </ion-button>

          <ion-button 
            expand="block" 
            color="primary" 
            (click)="runFullDiagnostic()"
            [disabled]="testing">
            <ion-icon name="medical-outline" slot="start"></ion-icon>
            Diagnostica Completa
          </ion-button>
        </ion-card-content>
      </ion-card>

      <ion-card *ngIf="lastTestResult">
        <ion-card-header>
          <ion-card-title>Risultati Test</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <pre>{{ lastTestResult }}</pre>
        </ion-card-content>
      </ion-card>

      <ion-card *ngIf="errors.length > 0">
        <ion-card-header>
          <ion-card-title style="color: var(--ion-color-danger)">Errori Rilevati</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <ion-list>
            <ion-item *ngFor="let error of errors">
              <ion-icon name="warning-outline" color="danger" slot="start"></ion-icon>
              <ion-label class="ion-text-wrap">{{ error }}</ion-label>
            </ion-item>
          </ion-list>
        </ion-card-content>
      </ion-card>

      <ion-card>
        <ion-card-header>
          <ion-card-title>Console Log</ion-card-title>
        </ion-card-header>
        <ion-card-content>
          <p><small>Controlla la console del browser per log dettagliati</small></p>
          <ion-button 
            size="small" 
            fill="clear" 
            (click)="clearConsole()">
            <ion-icon name="trash-outline" slot="start"></ion-icon>
            Pulisci Console
          </ion-button>
        </ion-card-content>
      </ion-card>
    </ion-content>
  `,
  styles: [`
    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      font-size: 12px;
      background: var(--ion-color-light);
      padding: 10px;
      border-radius: 4px;
      max-height: 300px;
      overflow-y: auto;
    }
    
    ion-button {
      margin: 8px 0;
    }
  `]
})
export class SimpleDbTestComponent implements OnInit {
  testing = false;
  lastTestResult: string = '';
  errors: string[] = [];
  platformInfo: any = null;
  systemStatus: string = 'Caricamento...';

  constructor(
    private simpleDbTestService: SimpleDbTestService,
    private hybridDbService: HybridDbService,
    private toastController: ToastController
  ) {}

  async ngOnInit() {
    console.log('🔧 SimpleDbTestComponent inizializzato');
    await this.loadPlatformInfo();
    await this.checkSystemStatus();
  }

  private async loadPlatformInfo() {
    try {
      this.platformInfo = await this.simpleDbTestService.testPlatformInfo();
      console.log('📱 Platform Info:', this.platformInfo);
    } catch (error) {
      console.error('❌ Errore caricamento info piattaforma:', error);
      this.errors.push(`Errore info piattaforma: ${error.message}`);
    }
  }

  private async checkSystemStatus() {
    try {
      const isAvailable = await this.hybridDbService.isDatabaseAvailable();
      this.systemStatus = isAvailable ? 'OK' : 'ERRORE';
      
      if (!isAvailable) {
        this.errors.push('Database non disponibile');
      }
    } catch (error) {
      this.systemStatus = 'ERRORE';
      this.errors.push(`Errore verifica sistema: ${error.message}`);
      console.error('❌ Errore verifica sistema:', error);
    }
  }

  async runQuickTest() {
    await this.runTest('Test Rapido', async () => {
      const result = await this.simpleDbTestService.quickTest();
      
      if (!result.isWorking) {
        this.errors.push(...result.errors);
      }
      
      return `Test Rapido Completato
Platform: ${result.platform}
Database: ${result.databaseType}
Funzionante: ${result.isWorking ? '✅' : '❌'}
Errori: ${result.errors.length}`;
    });
  }

  async runOperationsTest() {
    await this.runTest('Test Operazioni', async () => {
      const result = await this.simpleDbTestService.testDatabaseOperations();
      
      if (result.errors.length > 0) {
        this.errors.push(...result.errors);
      }
      
      return `Test Operazioni Database
CREATE TABLE: ${result.createTable ? '✅' : '❌'}
ADD RECORD: ${result.addRecord ? '✅' : '❌'}
GET RECORDS: ${result.getRecords ? '✅' : '❌'}
UPDATE RECORD: ${result.updateRecord ? '✅' : '❌'}
DELETE RECORD: ${result.deleteRecord ? '✅' : '❌'}
Errori: ${result.errors.length}`;
    });
  }

  async runPlatformTest() {
    await this.runTest('Test Piattaforma', async () => {
      const result = await this.simpleDbTestService.testPlatformInfo();
      
      return `Informazioni Piattaforma
Platform: ${result.platform}
Is Web: ${result.isWeb}
Is Mobile: ${result.isMobile}
Database Type: ${result.databaseType}
Is Capacitor: ${result.platformDetails.isCapacitor}
Is Android: ${result.platformDetails.isAndroid}
Is iOS: ${result.platformDetails.isIOS}`;
    });
  }

  async runFullDiagnostic() {
    await this.runTest('Diagnostica Completa', async () => {
      this.errors = []; // Reset errori
      
      const report = await this.simpleDbTestService.showSimpleReport();
      
      // Test compatibilità SyncroV2
      const syncroCompatible = await this.simpleDbTestService.testBasicSyncroV2Compatibility();
      
      return report + `\n🔄 SyncroV2 Compatibile: ${syncroCompatible ? '✅' : '❌'}`;
    });
  }

  private async runTest(testName: string, testFunction: () => Promise<string>) {
    this.testing = true;
    console.log(`🧪 Avvio ${testName}...`);

    try {
      this.lastTestResult = await testFunction();
      
      const toast = await this.toastController.create({
        message: `${testName} completato`,
        duration: 2000,
        color: 'success',
        position: 'top'
      });
      await toast.present();
      
      console.log(`✅ ${testName} completato`);
    } catch (error) {
      this.lastTestResult = `Errore durante ${testName}: ${error.message}`;
      this.errors.push(`${testName}: ${error.message}`);
      
      const toast = await this.toastController.create({
        message: `Errore durante ${testName}`,
        duration: 3000,
        color: 'danger',
        position: 'top'
      });
      await toast.present();
      
      console.error(`❌ ${testName} fallito:`, error);
    } finally {
      this.testing = false;
    }
  }

  clearConsole() {
    console.clear();
    this.errors = [];
    this.lastTestResult = '';
    console.log('🧹 Console pulita');
  }
}
