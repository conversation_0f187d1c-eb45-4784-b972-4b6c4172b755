/**
 * Modello per le informazioni del catalogo restituito dall'endpoint getCatalogByLang
 */
export class CatalogInfo {
    id: string;
    type: string;
    name: string;
    categories: any; // Può essere null o contenere dati delle categorie

    constructor(id: string = null, type: string = null, name: string = null, categories: any = null) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.categories = categories;
    }
}
